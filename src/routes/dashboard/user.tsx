import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getAllUsers, updateUser, deleteUser } from "@/db";
import type { User } from "@/db/schema";

export const Route = createFileRoute("/dashboard/user")({
	component: RouteComponent,
});

// 编辑用户表单组件
function EditUserForm({
	user,
	onSave,
	onCancel,
}: {
	user: User;
	onSave: (updates: Partial<User>) => void;
	onCancel: () => void;
}) {
	const [formData, setFormData] = useState({
		name: user.name || "",
		mobile: user.mobile || "",
		token: user.token,
		isAdmin: user.isAdmin,
	});

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSave(formData);
	};

	return (
		<Card className="mt-4">
			<CardHeader>
				<CardTitle>编辑用户信息</CardTitle>
				<CardDescription>修改用户的基本信息和权限</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium mb-1">姓名</label>
							<input
								type="text"
								value={formData.name}
								onChange={(e) =>
									setFormData({ ...formData, name: e.target.value })
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
						<div>
							<label className="block text-sm font-medium mb-1">手机号</label>
							<input
								type="tel"
								value={formData.mobile}
								onChange={(e) =>
									setFormData({ ...formData, mobile: e.target.value })
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
						<div>
							<label className="block text-sm font-medium mb-1">Token</label>
							<input
								type="number"
								value={formData.token}
								onChange={(e) =>
									setFormData({
										...formData,
										token: parseInt(e.target.value) || 0,
									})
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
					</div>
					<div className="flex items-center space-x-2">
						<input
							id="isAdmin"
							type="checkbox"
							checked={formData.isAdmin}
							onChange={(e) =>
								setFormData({ ...formData, isAdmin: e.target.checked })
							}
							className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
						/>
						<label htmlFor="isAdmin" className="text-sm font-medium">
							管理员权限
						</label>
					</div>
					<div className="flex gap-2">
						<Button type="submit">保存更改</Button>
						<Button type="button" variant="outline" onClick={onCancel}>
							取消
						</Button>
					</div>
				</form>
			</CardContent>
		</Card>
	);
}

function RouteComponent() {
	const [users, setUsers] = useState<User[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [editingUser, setEditingUser] = useState<User | null>(null);
	const [searchTerm, setSearchTerm] = useState("");

	// 获取用户列表
	const fetchUsers = async () => {
		try {
			setLoading(true);
			const userList = await getAllUsers();
			setUsers(userList);
		} catch (err) {
			console.error("获取用户列表失败:", err);
			setError("获取用户列表失败");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchUsers();
	}, []);

	// 保存用户更改
	const handleSaveUser = async (updates: Partial<User>) => {
		if (!editingUser) return;

		try {
			await updateUser(editingUser.dingTalkUnionId, updates);
			setEditingUser(null);
			await fetchUsers(); // 重新获取用户列表
		} catch (err) {
			console.error("更新用户失败:", err);
			alert("更新用户失败");
		}
	};

	// 删除用户
	const handleDeleteUser = async (dingTalkUnionId: string, userName: string) => {
		if (!confirm(`确定要删除用户 "${userName}" 吗？此操作不可撤销。`)) {
			return;
		}

		try {
			await deleteUser(dingTalkUnionId);
			await fetchUsers(); // 重新获取用户列表
		} catch (err) {
			console.error("删除用户失败:", err);
			alert("删除用户失败");
		}
	};

	// 过滤用户
	const filteredUsers = users.filter(
		(user) =>
			user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
			user.dingTalkUnionId.toLowerCase().includes(searchTerm.toLowerCase())
	);

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg">加载中...</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg text-red-500">{error}</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6 space-y-6">
			{/* 页面标题 */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
					<p className="text-muted-foreground">管理系统用户信息和权限</p>
				</div>
				<div className="flex gap-2">
					<Badge variant="secondary" className="text-sm">
						总用户: {users.length}
					</Badge>
					<a
						href="/dashboard"
						className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
					>
						返回仪表板
					</a>
				</div>
			</div>

			{/* 搜索框 */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex items-center space-x-2">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth="2"
							className="h-4 w-4 text-muted-foreground"
						>
							<circle cx="11" cy="11" r="8" />
							<path d="M21 21l-4.35-4.35" />
						</svg>
						<input
					type="text"
					placeholder="搜索用户（姓名、钉钉ID）..."
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
					className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
					</div>
				</CardContent>
			</Card>

			{/* 编辑表单 */}
			{editingUser && (
				<EditUserForm
					user={editingUser}
					onSave={handleSaveUser}
					onCancel={() => setEditingUser(null)}
				/>
			)}

			{/* 用户列表 */}
			<div className="grid gap-4">
				{filteredUsers.length === 0 ? (
					<Card>
						<CardContent className="pt-6">
							<div className="text-center text-muted-foreground">
								{searchTerm ? "没有找到匹配的用户" : "暂无用户数据"}
							</div>
						</CardContent>
					</Card>
				) : (
					filteredUsers.map((user) => (
						<Card key={user.id} className="hover:shadow-md transition-shadow">
							<CardContent className="pt-6">
								<div className="flex items-center justify-between">
									<div className="flex-1">
										<div className="flex items-center gap-3 mb-2">
											<h3 className="text-lg font-semibold">
												{user.name || "未设置姓名"}
											</h3>
											{user.isAdmin && (
												<Badge variant="destructive">管理员</Badge>
											)}
										</div>
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-muted-foreground">
													<div>
										<span className="font-medium">钉钉ID:</span>{" "}
										{user.dingTalkUnionId}
									</div>
													<div>
														<span className="font-medium">手机:</span>{" "}
														{user.mobile || "未设置"}
													</div>
													<div>
														<span className="font-medium">Token:</span>{" "}
														{user.token.toLocaleString()}
													</div>
													<div>
														<span className="font-medium">请求次数:</span>{" "}
														{user.requestTimes}
													</div>
													<div>
														<span className="font-medium">创建时间:</span>{" "}
														{new Date(user.createdAt).toLocaleDateString()}
													</div>
													<div>
														<span className="font-medium">更新时间:</span>{" "}
														{new Date(user.updatedAt).toLocaleDateString()}
													</div>
												</div>
									</div>
									<div className="flex gap-2 ml-4">
										<Button
											variant="outline"
											size="sm"
											onClick={() => setEditingUser(user)}
										>
											编辑
										</Button>
										<Button
								variant="destructive"
								size="sm"
								onClick={() =>
									handleDeleteUser(user.dingTalkUnionId, user.name || user.dingTalkUnionId)
								}
							>
								删除
							</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))
				)}
			</div>
		</div>
	);
}
