import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getAllUsers, type User } from "@/db";

export const Route = createFileRoute("/dashboard/")({
	component: RouteComponent,
});

// 统计数据接口
interface DashboardStats {
	totalUsers: number;
	totalRequests: number;
	totalTokensUsed: number;
	adminUsers: number;
	regularUsers: number;
	averageRequestsPerUser: number;
}

function RouteComponent() {
	const [stats, setStats] = useState<DashboardStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// 获取统计数据
	useEffect(() => {
		const fetchStats = async () => {
			try {
				setLoading(true);
				const users = await getAllUsers();
				
				// 计算统计数据
				const totalUsers = users.length;
				const totalRequests = users.reduce((sum, user) => sum + user.requestTimes, 0);
				// 假设每次请求消耗100个token（可以根据实际情况调整）
				const totalTokensUsed = totalRequests * 100;
				const adminUsers = users.filter(user => user.isAdmin).length;
				const regularUsers = totalUsers - adminUsers;
				const averageRequestsPerUser = totalUsers > 0 ? Math.round(totalRequests / totalUsers) : 0;

				setStats({
					totalUsers,
					totalRequests,
					totalTokensUsed,
					adminUsers,
					regularUsers,
					averageRequestsPerUser
				});
			} catch (err) {
				console.error('获取统计数据失败:', err);
				setError('获取统计数据失败');
			} finally {
				setLoading(false);
			}
		};

		fetchStats();
	}, []);

	if (loading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg">加载中...</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-lg text-red-500">{error}</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-6 space-y-6">
			{/* 页面标题 */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">管理员仪表板</h1>
					<p className="text-muted-foreground">查看系统使用统计和用户数据</p>
				</div>
				<Badge variant="secondary" className="text-sm">
					管理员面板
				</Badge>
			</div>

			{/* 统计卡片网格 */}
			<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
				{/* 总用户数 */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">总用户数</CardTitle>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth="2"
							className="h-4 w-4 text-muted-foreground"
						>
							<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
							<circle cx="9" cy="7" r="4" />
							<path d="M22 21v-2a4 4 0 0 0-3-3.87" />
							<path d="M16 3.13a4 4 0 0 1 0 7.75" />
						</svg>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats?.totalUsers || 0}</div>
						<p className="text-xs text-muted-foreground">
							管理员: {stats?.adminUsers || 0} | 普通用户: {stats?.regularUsers || 0}
						</p>
					</CardContent>
				</Card>

				{/* 总请求次数 */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">总请求次数</CardTitle>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth="2"
							className="h-4 w-4 text-muted-foreground"
						>
							<path d="M22 12h-4l-3 9L9 3l-3 9H2" />
						</svg>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats?.totalRequests.toLocaleString() || 0}</div>
						<p className="text-xs text-muted-foreground">
							平均每用户: {stats?.averageRequestsPerUser || 0} 次
						</p>
					</CardContent>
				</Card>

				{/* Token使用量 */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Token使用量</CardTitle>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth="2"
							className="h-4 w-4 text-muted-foreground"
						>
							<path d="M12 2v20m8-10H4" />
						</svg>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats?.totalTokensUsed.toLocaleString() || 0}</div>
						<p className="text-xs text-muted-foreground">
							估算值（每请求100 tokens）
						</p>
					</CardContent>
				</Card>
			</div>

			{/* 快速操作 */}
			<Card>
				<CardHeader>
					<CardTitle>快速操作</CardTitle>
					<CardDescription>
						管理系统用户和查看详细数据
					</CardDescription>
				</CardHeader>
				<CardContent className="flex gap-4">
					<a 
						href="/dashboard/user" 
						className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2"
					>
						用户管理
					</a>
				</CardContent>
			</Card>
		</div>
	);
}
