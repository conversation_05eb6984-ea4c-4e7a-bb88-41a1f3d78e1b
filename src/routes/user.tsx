import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
	User as UserIcon,
	Phone,
	Building,
	Calendar,
	Loader2,
	ArrowLeft,
	RefreshCw,
	Mail,
} from "lucide-react";
import * as React from "react";
import { getUserByDingTalkUnionId, getUserById } from "@/db";
import type { User } from "@/db/schema";
import { useUserStore } from "@/stores/user-store";
import { useAuth } from "@/hooks/use-auth";

export const Route = createFileRoute("/user")({
	component: RouteComponent,
});

function RouteComponent() {
	return (
		<div className="min-h-screen bg-gray-50">
			<div className="max-w-4xl mx-auto p-6">
				{/* 页面标题 */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900">账号管理</h1>
					<p className="text-gray-600 mt-2">管理您的个人信息和账号设置</p>
				</div>

				<UserProfileCard />
			</div>
		</div>
	);
}

function UserProfileCard() {
	// 从用户store获取用户信息
	const { user: userInfo, loading, error, setUser, setLoading, setError } = useUserStore();
	// 从认证hook获取当前登录用户信息
	const { user: authUser } = useAuth();
	const navigate = useNavigate();
	const [isRefreshing, setIsRefreshing] = React.useState(false);

	// 重新获取用户数据的函数
	const fetchUserData = React.useCallback(async (forceRefresh = false) => {
		// 如果store中已有用户信息且不是强制刷新，则直接使用
		if (userInfo && !forceRefresh) {
			return;
		}

		// 优先使用认证用户的信息，如果没有则使用store中的信息
		const currentUser = authUser || userInfo;
		if (!currentUser) {
			setError("未找到用户信息，请重新登录");
			return;
		}

		try {
			setIsRefreshing(true);
			setLoading(true);
			setError(null);

			let updatedUser: User | null = null;

			// 根据用户类型选择获取方式
			if (currentUser.id) {
				// 通过用户ID获取（适用于邮箱登录用户）
				updatedUser = await getUserById(currentUser.id);
			} else if (currentUser.dingTalkUnionId) {
				// 通过钉钉UnionId获取（适用于钉钉登录用户）
				updatedUser = await getUserByDingTalkUnionId(currentUser.dingTalkUnionId);
			}

			if (updatedUser) {
				setUser(updatedUser);
			} else {
				setError("用户信息不存在");
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : "获取用户信息失败");
		} finally {
			setLoading(false);
			setIsRefreshing(false);
		}
	}, [authUser?.id, authUser?.dingTalkUnionId, userInfo]);


	// 页面进入时检查是否需要获取数据
	React.useEffect(() => {
		// 只有当没有用户信息时才获取
		if (!userInfo && authUser) {
			fetchUserData();
		}
	}, [authUser, userInfo, fetchUserData]);

	// 返回AI页面的处理函数
	const handleGoBack = () => {
		navigate({ to: "/ai" });
	};

	// 手动刷新数据的处理函数
	const handleRefresh = () => {
		fetchUserData(true); // 强制刷新
	};

	// 加载状态
	if (loading) {
		return (
			<Card className="p-8">
				<div className="flex items-center justify-center h-32">
					<Loader2 className="h-8 w-8 animate-spin" />
					<span className="ml-2">加载用户信息...</span>
				</div>
			</Card>
		);
	}

	// 错误状态
	if (error || !userInfo) {
		return (
			<Card className="p-8">
				<div className="text-center text-red-600">
					<p>{error || "用户信息不存在"}</p>
				</div>
			</Card>
		);
	}

	return (
		<Card className="p-8">
			{/* 操作按钮 */}
			<div className="mb-6 flex items-center gap-3">
				<Button
					variant="outline"
					size="sm"
					onClick={handleGoBack}
					className="flex items-center gap-2"
				>
					<ArrowLeft className="h-4 w-4" />
					返回AI页面
				</Button>
				<Button
					variant="outline"
					size="sm"
					onClick={handleRefresh}
					disabled={isRefreshing}
					className="flex items-center gap-2"
				>
					<RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
					{isRefreshing ? '刷新中...' : '刷新数据'}
				</Button>
			</div>

			<div className="flex items-start justify-between mb-6">
				<div className="flex items-center space-x-6">
					{/* 用户头像 */}
					<Avatar className="h-24 w-24">
						<AvatarImage
							src={userInfo.avatar || ""}
							alt={userInfo.name || "用户"}
						/>
						<AvatarFallback className="text-2xl bg-blue-500 text-white">
							{userInfo.name ? userInfo.name.slice(0, 2) : "用户"}
						</AvatarFallback>
					</Avatar>

					{/* 基本信息 */}
					<div>
						<h2 className="text-2xl font-bold text-gray-900">
							{userInfo.name || "未设置姓名"}
						</h2>
						<p className="text-gray-500 text-sm mt-1">
							{userInfo.isAdmin ? "管理员" : "普通用户"}
						</p>
						<p className="text-green-600 font-semibold mt-1">
							Token: {userInfo.token?.toLocaleString()}
						</p>
					</div>
				</div>
			</div>

			<Separator className="my-6" />

			{/* 详细信息 */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* 姓名 */}
				<div className="space-y-2">
					<label className="flex items-center text-sm font-medium text-gray-700">
						<UserIcon className="h-4 w-4 mr-2" />
						姓名
					</label>
					<p className="text-gray-900">{userInfo.name || "未设置"}</p>
				</div>

				{/* 邮箱 */}
				{userInfo.email && (
					<div className="space-y-2">
						<label className="flex items-center text-sm font-medium text-gray-700">
							<Mail className="h-4 w-4 mr-2" />
							邮箱
						</label>
						<p className="text-gray-900">{userInfo.email}</p>
					</div>
				)}

				{/* 手机号 */}
				<div className="space-y-2">
					<label className="flex items-center text-sm font-medium text-gray-700">
						<Phone className="h-4 w-4 mr-2" />
						手机号
					</label>
					<p className="text-gray-900">{userInfo.mobile || "未设置"}</p>
				</div>

				{/* Token */}
				<div className="space-y-2">
					<label className="flex items-center text-sm font-medium text-gray-700">
						<Building className="h-4 w-4 mr-2" />
						账户Token
					</label>
					<p className="font-semibold text-green-600">
						{userInfo.token?.toLocaleString()}
					</p>
				</div>

				{/* 请求次数 */}
				<div className="space-y-2">
					<label className="flex items-center text-sm font-medium text-gray-700">
						<UserIcon className="h-4 w-4 mr-2" />
						请求次数
					</label>
					<p className="text-gray-900">{userInfo.requestTimes}</p>
				</div>

				{/* 创建时间 */}
				<div className="space-y-2">
					<label className="flex items-center text-sm font-medium text-gray-700">
						<Calendar className="h-4 w-4 mr-2" />
						创建时间
					</label>
					<p className="text-gray-900">
						{userInfo.createdAt
							? new Date(userInfo.createdAt).toLocaleDateString("zh-CN")
							: "未知"}
					</p>
				</div>
			</div>
		</Card>
	);
}
