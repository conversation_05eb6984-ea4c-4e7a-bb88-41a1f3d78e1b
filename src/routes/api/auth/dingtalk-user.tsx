import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'

/**
 * 钉钉获取用户个人信息的API代理
 * 解决前端直接调用钉钉API的CORS问题
 */
export const ServerRoute = createServerFileRoute('/api/auth/dingtalk-user').methods({
  GET: async ({ request }) => {
    try {
      const url = new URL(request.url)
      const unionId = url.searchParams.get('unionId') || 'me'
      const accessToken = request.headers.get('x-acs-dingtalk-access-token')

      // 验证必需参数
      if (!accessToken) {
        return json(
          { 
            error: 'Missing access token',
            message: '缺少访问令牌，请在Header中提供x-acs-dingtalk-access-token'
          },
          { 
            status: 400
          }
        )
      }

      // 调用钉钉API获取用户信息
      const response = await fetch(`https://api.dingtalk.com/v1.0/contact/users/${unionId}`, {
        method: 'GET',
        headers: {
          'x-acs-dingtalk-access-token': accessToken,
          'Content-Type': 'application/json',
        }
      })

      const data = await response.json()

      if (!response.ok) {
        console.error('钉钉用户信息API调用失败:', data)
        return json(
          { 
            error: 'DingTalk API Error',
            message: data.message || '钉钉用户信息API调用失败',
            details: data
          },
          { 
            status: response.status
          }
        )
      }

      // 返回成功响应
      return json(data)
    } catch (error) {
      console.error('用户信息API代理错误:', error)
      return json(
        { 
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : '服务器内部错误'
        },
        { 
          status: 500
        }
      )
    }
  },
  
  // 处理预检请求
  OPTIONS: async () => {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, x-acs-dingtalk-access-token'
      }
    })
  }
})