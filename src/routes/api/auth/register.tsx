import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'
import { createUserWithEmail } from '@/db/user'

/**
 * User registration API endpoint
 * Handles email and password registration
 */
export const ServerRoute = createServerFileRoute('/api/auth/register')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { email, password, name } = body

        // Validate required fields
        if (!email || !password) {
          return json(
            { 
              error: 'Missing required fields',
              message: '邮箱和密码为必填项'
            },
            { status: 400 }
          )
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(email)) {
          return json(
            { 
              error: 'Invalid email format',
              message: '邮箱格式不正确'
            },
            { status: 400 }
          )
        }

        // Validate password strength
        if (password.length < 6) {
          return json(
            { 
              error: 'Password too weak',
              message: '密码长度至少6位'
            },
            { status: 400 }
          )
        }

        // Create user
        const user = await createUserWithEmail({
          email,
          password,
          name: name || '新用户'
        })

        // Remove password from response
        const { password: _, ...userWithoutPassword } = user

        return json({
          success: true,
          message: '注册成功',
          user: userWithoutPassword
        })

      } catch (error: any) {
        console.error('Registration error:', error)
        
        if (error.message === 'Email already exists') {
          return json(
            { 
              error: 'Email already exists',
              message: '该邮箱已被注册'
            },
            { status: 409 }
          )
        }

        return json(
          { 
            error: 'Registration failed',
            message: '注册失败，请稍后重试'
          },
          { status: 500 }
        )
      }
    }
  })
