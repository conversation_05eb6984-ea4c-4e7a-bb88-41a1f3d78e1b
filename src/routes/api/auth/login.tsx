import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'
import { verifyUserPassword } from '@/db/user'
import { sign } from 'jsonwebtoken'

/**
 * User login API endpoint
 * Handles email and password authentication
 */
export const ServerRoute = createServerFileRoute('/api/auth/login')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { email, password } = body

        // Validate required fields
        if (!email || !password) {
          return json(
            { 
              error: 'Missing required fields',
              message: '邮箱和密码为必填项'
            },
            { status: 400 }
          )
        }

        // Verify user credentials
        const user = await verifyUserPassword(email, password)
        
        if (!user) {
          return json(
            { 
              error: 'Invalid credentials',
              message: '邮箱或密码错误'
            },
            { status: 401 }
          )
        }

        // Generate JWT token (optional - you can use a simple token or session)
        const token = `auth_${user.id}_${Date.now()}`

        // Remove password from response
        const { password: _, ...userWithoutPassword } = user

        return json({
          success: true,
          message: '登录成功',
          user: userWithoutPassword,
          token
        })

      } catch (error: any) {
        console.error('Login error:', error)
        
        return json(
          { 
            error: 'Login failed',
            message: '登录失败，请稍后重试'
          },
          { status: 500 }
        )
      }
    }
  })
