import { useCallback, useEffect, useRef, useState } from "react";

/**
 * 图片上传Hook的属性接口
 */
interface UseImageUploadProps {
	onUpload?: (url: string) => void;
}

/**
 * 图片上传自定义Hook
 * 提供图片上传、预览、删除等功能
 */
export function useImageUpload({ onUpload }: UseImageUploadProps = {}) {
	const previewRef = useRef<string | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [previewUrl, setPreviewUrl] = useState<string | null>(null);
	const [fileName, setFileName] = useState<string | null>(null);

	/**
	 * 处理缩略图点击事件
	 */
	const handleThumbnailClick = useCallback(() => {
		fileInputRef.current?.click();
	}, []);

	/**
	 * 处理文件选择变化
	 */
	const handleFileChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0];
			if (file) {
				setFileName(file.name);
				const url = URL.createObjectURL(file);
				setPreviewUrl(url);
				previewRef.current = url;
				onUpload?.(url);
			}
		},
		[onUpload]
	);

	/**
	 * 处理移除文件
	 */
	const handleRemove = useCallback(() => {
		if (previewUrl) {
			URL.revokeObjectURL(previewUrl);
		}
		setPreviewUrl(null);
		setFileName(null);
		previewRef.current = null;
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	}, [previewUrl]);

	/**
	 * 清理资源
	 */
	useEffect(() => {
		return () => {
			if (previewRef.current) {
				URL.revokeObjectURL(previewRef.current);
			}
		};
	}, []);

	return {
		previewUrl,
		fileName,
		fileInputRef,
		handleThumbnailClick,
		handleFileChange,
		handleRemove,
	};
}