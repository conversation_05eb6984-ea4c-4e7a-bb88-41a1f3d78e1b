"use client";

import { HeroSection } from "@/components/ui/hero-section";
import { Icons } from "@/components/ui/icons";
import { useAuth } from "@/hooks/use-auth";
import { useUserStore } from "@/stores/user-store";
import { useEffect, useState } from "react";

/**
 * HomeHero组件 - 首页英雄区域组件
 * 包含完整的配置示例，可以直接在项目中使用
 */
export function HomeHero() {
	const { isAuthenticated } = useAuth();
	const { user } = useUserStore();
	const [startUrl, setStartUrl] = useState("/ai");

	// 根据用户登录状态和角色决定"开始使用"按钮的跳转路径
	useEffect(() => {
		if (isAuthenticated && user) {
			// 已登录用户：管理员跳转到dashboard，普通用户跳转到AI页面
			setStartUrl(user.isAdmin ? "/dashboard" : "/ai");
		} else {
			// 未登录用户：跳转到登录页面
			setStartUrl("/auth/login");
		}
	}, [isAuthenticated, user]);

	return (
		<HeroSection
			badge={{
				text: "介绍我们的新模型",
				action: {
					text: "了解更多",
					href: "/model",
				},
			}}
			title="库无忧助手"
			description="一款专注于石化仓储领域的全链路技术服务平台，旨在为石化仓储工程的建设、运营维护及检修升级提供智能化支持。"
			actions={[
				{
					text: "开始使用",
					href: startUrl,
					variant: "default",
				},
			]}
			image={{
				light: "/home.png",
				dark: "/home.png",
				alt: "UI组件预览",
			}}
		/>
	);
}
