"use client";

import * as React from "react";
import { motion } from "framer-motion";

import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

/**
 * Tab组件的属性接口
 */
interface TabProps {
	/** Tab显示的文本 */
	text: string;
	/** 是否被选中 */
	selected: boolean;
	/** 设置选中状态的回调函数 */
	setSelected: (text: string) => void;
	/** 是否显示折扣标识 */
	discount?: boolean;
}

/**
 * 可切换的Tab组件
 * 支持动画效果和折扣标识显示
 */
export function Tab({
	text,
	selected,
	setSelected,
	discount = false,
}: TabProps) {
	return (
		<button
			onClick={() => setSelected(text)}
			className={cn(
				"relative w-fit px-4 py-2 text-sm font-semibold capitalize",
				"text-foreground transition-colors",
				discount && "flex items-center justify-center gap-2.5"
			)}
		>
			<span className="relative z-10">{text}</span>
			{selected && (
				<motion.span
					layoutId="tab"
					transition={{ type: "spring", duration: 0.4 }}
					className="absolute inset-0 z-0 rounded-full bg-background shadow-sm"
				/>
			)}

		</button>
	);
}