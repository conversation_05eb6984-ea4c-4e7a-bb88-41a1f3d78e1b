"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { MessageLoading } from "@/components/ui/message-loading";

/**
 * 聊天气泡容器组件属性
 */
interface ChatBubbleProps {
	variant?: "sent" | "received";
	layout?: "default" | "ai";
	className?: string;
	children: React.ReactNode;
}

/**
 * 聊天气泡容器组件
 * 用于包装聊天消息的容器
 */
export function ChatBubble({
	variant = "received",
	layout = "default",
	className,
	children,
}: ChatBubbleProps) {
	return (
		<div
			className={cn(
				"flex items-start gap-2 mb-4",
				variant === "sent" && "flex-row-reverse",
				className
			)}
		>
			{children}
		</div>
	);
}

/**
 * 聊天气泡消息组件属性
 */
interface ChatBubbleMessageProps {
	variant?: "sent" | "received";
	isLoading?: boolean;
	className?: string;
	children?: React.ReactNode;
}

/**
 * 聊天气泡消息组件
 * 显示实际的消息内容
 */
export function ChatBubbleMessage({
	variant = "received",
	isLoading,
	className,
	children,
}: ChatBubbleMessageProps) {
	return (
		<div
			className={cn(
				"rounded-lg p-3",
				variant === "sent" ? "bg-primary text-primary-foreground" : "bg-muted",
				className
			)}
		>
			{isLoading ? (
				<div className="flex items-center space-x-2">
					<MessageLoading />
				</div>
			) : (
				children
			)}
		</div>
	);
}

/**
 * 聊天气泡头像组件属性
 */
interface ChatBubbleAvatarProps {
	src?: string;
	fallback?: string;
	className?: string;
}

/**
 * 聊天气泡头像组件
 * 显示用户或AI的头像
 */
export function ChatBubbleAvatar({
	src,
	fallback = "AI",
	className,
}: ChatBubbleAvatarProps) {
	return (
		<Avatar className={cn("h-8 w-8", className)}>
			{src && <AvatarImage src={src} />}
			<AvatarFallback>{fallback}</AvatarFallback>
		</Avatar>
	);
}

/**
 * 聊天气泡操作按钮组件属性
 */
interface ChatBubbleActionProps {
	icon?: React.ReactNode;
	onClick?: () => void;
	className?: string;
}

/**
 * 聊天气泡操作按钮组件
 * 用于复制、重新生成等操作
 */
export function ChatBubbleAction({
	icon,
	onClick,
	className,
}: ChatBubbleActionProps) {
	return (
		<Button
			variant="ghost"
			size="icon"
			className={cn("h-6 w-6", className)}
			onClick={onClick}
		>
			{icon}
		</Button>
	);
}

/**
 * 聊天气泡操作按钮包装器组件属性
 */
interface ChatBubbleActionWrapperProps {
	className?: string;
	children: React.ReactNode;
}

/**
 * 聊天气泡操作按钮包装器组件
 * 用于包装多个操作按钮
 */
export function ChatBubbleActionWrapper({
	className,
	children,
}: ChatBubbleActionWrapperProps) {
	return (
		<div className={cn("flex items-center gap-1 mt-2", className)}>
			{children}
		</div>
	);
}
