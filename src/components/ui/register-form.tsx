"use client";

import * as React from "react";
import { useState } from "react";
import { UserPlus, Mail, Lock, User, Eye, EyeOff } from "lucide-react";
import { useRouter } from "@tanstack/react-router";

/**
 * User registration form component
 * Handles email and password registration with validation
 */
export const RegisterForm = () => {
	const router = useRouter();
	const [formData, setFormData] = useState({
		email: "",
		password: "",
		confirmPassword: "",
		name: "",
	});
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");

	/**
	 * Handle form input changes
	 */
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
		// Clear error when user starts typing
		if (error) setError("");
	};

	/**
	 * Validate form data
	 */
	const validateForm = () => {
		if (!formData.email || !formData.password || !formData.confirmPassword) {
			setError("请填写所有必填字段");
			return false;
		}

		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(formData.email)) {
			setError("邮箱格式不正确");
			return false;
		}

		if (formData.password.length < 6) {
			setError("密码长度至少6位");
			return false;
		}

		if (formData.password !== formData.confirmPassword) {
			setError("两次输入的密码不一致");
			return false;
		}

		return true;
	};

	/**
	 * Handle form submission
	 */
	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		if (!validateForm()) {
			return;
		}

		setIsLoading(true);
		setError("");

		try {
			const response = await fetch("/api/auth/register", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					email: formData.email,
					password: formData.password,
					name: formData.name || "新用户",
				}),
			});

			const data = await response.json();

			if (response.ok) {
				// Registration successful, redirect to login
				alert("注册成功！请登录");
				router.navigate({ to: "/auth/login" });
			} else {
				setError(data.message || "注册失败");
			}
		} catch (error) {
			console.error("Registration error:", error);
			setError("网络错误，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1">
			<div className="w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black">
				{/* Registration icon */}
				<div className="flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5">
					<UserPlus className="w-7 h-7 text-black" />
				</div>

				{/* Title and description */}
				<h2 className="text-2xl font-semibold mb-2 text-center">用户注册</h2>
				<p className="text-gray-500 text-sm mb-8 text-center">
					创建您的账户
				</p>

				{/* Error message */}
				{error && (
					<div className="w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm">
						{error}
					</div>
				)}

				{/* Registration form */}
				<form onSubmit={handleSubmit} className="w-full space-y-4">
					{/* Name field */}
					<div className="relative">
						<User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
						<input
							type="text"
							name="name"
							placeholder="姓名（可选）"
							value={formData.name}
							onChange={handleInputChange}
							className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						/>
					</div>

					{/* Email field */}
					<div className="relative">
						<Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
						<input
							type="email"
							name="email"
							placeholder="邮箱地址"
							value={formData.email}
							onChange={handleInputChange}
							required
							className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						/>
					</div>

					{/* Password field */}
					<div className="relative">
						<Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
						<input
							type={showPassword ? "text" : "password"}
							name="password"
							placeholder="密码（至少6位）"
							value={formData.password}
							onChange={handleInputChange}
							required
							className="w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						/>
						<button
							type="button"
							onClick={() => setShowPassword(!showPassword)}
							className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
						>
							{showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
						</button>
					</div>

					{/* Confirm password field */}
					<div className="relative">
						<Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
						<input
							type={showConfirmPassword ? "text" : "password"}
							name="confirmPassword"
							placeholder="确认密码"
							value={formData.confirmPassword}
							onChange={handleInputChange}
							required
							className="w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
						/>
						<button
							type="button"
							onClick={() => setShowConfirmPassword(!showConfirmPassword)}
							className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
						>
							{showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
						</button>
					</div>

					{/* Submit button */}
					<button
						type="submit"
						disabled={isLoading}
						className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed"
					>
						{isLoading ? "注册中..." : "注册"}
					</button>
				</form>

				{/* Login link */}
				<div className="mt-6 text-center">
					<p className="text-gray-500 text-sm">
						已有账户？{" "}
						<button
							onClick={() => router.navigate({ to: "/auth/login" })}
							className="text-blue-500 hover:text-blue-600 font-medium"
						>
							立即登录
						</button>
					</p>
				</div>
			</div>
		</div>
	);
};
