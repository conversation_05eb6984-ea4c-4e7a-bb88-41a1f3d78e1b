"use client";

import React, { useState, useCallback } from "react";
import { ChevronUp, ChevronDown } from "lucide-react";
import {
	PureMultimodalInput,
	type Attachment,
	type UIMessage,
	type VisibilityType,
} from "./multimodal-ai-chat-input";

/**
 * 多模态输入组件属性接口
 */
interface MultimodalChatInputProps {
	onSend?: (content: string) => void;
	showSuggestedActions?: boolean; // 控制是否显示预设问题卡片
}

/**
 * 多模态输入组件
 * 支持文本和文件输入的聊天界面组件
 */
export function MultimodalChatInput({ onSend, showSuggestedActions: initialShowSuggestedActions = true }: MultimodalChatInputProps = {}) {
	// 最小状态和处理器，满足 PureMultimodalInput 的要求
	const [attachments, setAttachments] = useState<Attachment[]>([]);
	const [isGenerating, setIsGenerating] = useState(false); // 控制停止按钮的可见性
	const [chatId] = useState("demo-input-only"); // 虚拟聊天 ID
	const [showSuggestedActions, setShowSuggestedActions] = useState(initialShowSuggestedActions); // 控制快捷输入内容的显示

	/**
	 * 处理发送消息
	 */
	const handleSendMessage = useCallback(
		({ input, attachments }: { input: string; attachments: Attachment[] }) => {
			console.log("--- 发送消息 ---");
			console.log("输入:", input);
			console.log("附件:", attachments);
			console.log("---------------------------------");

			// 调用父组件传入的onSend回调
			if (onSend && input.trim()) {
				onSend(input.trim());
			}

			setIsGenerating(true);
			setTimeout(() => {
				setIsGenerating(false);
				// 清除附件
				setAttachments([]);
			}, 500); // 短暂延迟后重置状态
		},
		[onSend]
	);

	/**
	 * 处理停止生成
	 */
	const handleStopGenerating = useCallback(() => {
		console.log("停止按钮被点击（模拟）。");
		setIsGenerating(false);
	}, []);

	// PureMultimodalInput 需要的其他属性
	const canSend = true; // 在这个最小演示中始终允许发送
	const messages: UIMessage[] = []; // 提供一个空数组作为类型要求，虽然在这里不使用
	const selectedVisibilityType: VisibilityType = "private"; // 虚拟可见性

	return (
		// 添加一个简单的容器 div 用于基本的居中/填充
		// 但重点是输入组件本身
		<div className="w-full max-w-3xl mx-auto p-4">
			<div className="flex flex-col gap-4">
				{/* 收起/展开按钮 - 移到最上方，只在允许显示预设问题时显示 */}
				{initialShowSuggestedActions && (
					<div className="flex justify-center">
						<button
							onClick={() => setShowSuggestedActions(!showSuggestedActions)}
							className="p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm"
							title={showSuggestedActions ? "收起快捷输入" : "展开快捷输入"}
						>
							{showSuggestedActions ? (
								<ChevronUp className="w-4 h-4 text-gray-600" />
							) : (
								<ChevronDown className="w-4 h-4 text-gray-600" />
							)}
						</button>
					</div>
				)}
				
				{/* 输入组件 */}
				<div>
					<PureMultimodalInput
						chatId={chatId}
						messages={(initialShowSuggestedActions && showSuggestedActions) ? [] : [{ id: "dummy", content: "", role: "user" }]} // 控制快捷输入显示
						attachments={attachments}
						setAttachments={setAttachments}
						onSendMessage={handleSendMessage}
						onStopGenerating={handleStopGenerating}
						isGenerating={isGenerating}
						canSend={canSend} // True
						selectedVisibilityType={selectedVisibilityType} // 'private'
					/>
				</div>
			</div>
		</div>
	);
}
