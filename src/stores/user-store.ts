/**
@fileoverview 用户状态管理接口
@description 用来存储用户信息，并管理用户登录状态
@interface
@typedef {Object} UserStore
@property {User | null} user - 当前用户信息
@property {boolean} isLoading - 是否正在加载用户信息
@property {boolean} isAuthenticated - 用户是否已登录
 */

import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { User } from "@/db/schema";

/**
 * 用户状态管理接口
 * 定义用户存储的所有状态和操作方法
 */
interface UserStore {
	/** 当前用户信息 */
	user: User | null;
	/** 是否正在加载用户信息 */
	loading: boolean;
	/** 错误信息 */
	error: string | null;

	/**
	 * 设置用户信息
	 * @param user - 用户对象，null表示未登录或需要清除用户信息
	 */
	setUser: (user: User | null) => void;

	/**
	 * 设置加载状态
	 * @param loading - 是否正在加载用户信息
	 */
	setLoading: (loading: boolean) => void;

	/**
	 * 设置错误信息
	 * @param error - 错误信息，null表示清除错误
	 */
	setError: (error: string | null) => void;

	/**
	 * 清除用户信息
	 * 通常在用户登出时调用
	 */
	clearUser: () => void;

	/**
	 * 更新用户信息的部分字段
	 * @param updates - 需要更新的用户字段
	 */
	updateUser: (updates: Partial<User>) => void;
}

/**
 * 用户状态管理Store实例
 * 使用zustand实现的状态管理，支持持久化存储
 */
export const useUserStore = create<UserStore>()(
	persist(
		(set, get) => ({
			user: null,
			loading: false,
			error: null,

			setUser: (user) => {
				set({ user, error: null });
			},

			setLoading: (loading) => {
				set({ loading });
			},

			setError: (error) => {
				set({ error, loading: false });
			},

			clearUser: () => {
				set({ user: null, error: null, loading: false });
			},

			updateUser: (updates) => {
				const currentUser = get().user;
				if (currentUser) {
					set({ user: { ...currentUser, ...updates } });
				}
			},
		}),
		{
			name: "user-store",
			partialize: (state) => ({
				user: state.user,
			}),
		}
	)
);
