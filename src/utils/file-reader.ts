// Dynamic imports to avoid SSR issues
let mammoth: any = null;
let pdfjsLib: any = null;

// Initialize libraries only on client side
const initializeLibraries = async () => {
  if (typeof window === 'undefined') {
    throw new Error('File reading is only supported on the client side');
  }

  if (!mammoth) {
    mammoth = await import('mammoth');
  }

  if (!pdfjsLib) {
    pdfjsLib = await import('pdfjs-dist');
    // Configure PDF.js worker
    pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
  }
};

/**
 * File reading utility functions
 * Supports reading content from Word (.docx), PDF (.pdf), and text (.txt) files
 */

/**
 * Read text content from a TXT file
 * @param file - The text file to read
 * @returns Promise<string> - The text content
 */
export async function readTextFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve(content);
    };
    reader.onerror = () => reject(new Error('Failed to read text file'));
    reader.readAsText(file, 'utf-8');
  });
}

/**
 * Read text content from a Word (.docx) file
 * @param file - The Word file to read
 * @returns Promise<string> - The extracted text content
 */
export async function readWordFile(file: File): Promise<string> {
  await initializeLibraries();

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        const result = await mammoth.extractRawText({ arrayBuffer });
        resolve(result.value);
      } catch (error) {
        reject(new Error(`Failed to read Word file: ${error}`));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read Word file'));
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Read text content from a PDF file
 * @param file - The PDF file to read
 * @returns Promise<string> - The extracted text content
 */
export async function readPdfFile(file: File): Promise<string> {
  await initializeLibraries();

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

        let fullText = '';

        // Extract text from each page
        for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
          const page = await pdf.getPage(pageNum);
          const textContent = await page.getTextContent();
          const pageText = textContent.items
            .map((item: any) => item.str)
            .join(' ');
          fullText += `Page ${pageNum}:\n${pageText}\n\n`;
        }

        resolve(fullText);
      } catch (error) {
        reject(new Error(`Failed to read PDF file: ${error}`));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read PDF file'));
    reader.readAsArrayBuffer(file);
  });
}

/**
 * Read file content based on file type
 * @param file - The file to read
 * @returns Promise<string> - The extracted text content
 */
export async function readFileContent(file: File): Promise<string> {
  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.split('.').pop();
  
  console.log(`Reading file: ${file.name} (${file.size} bytes)`);
  console.log(`File type: ${file.type}`);
  console.log(`File extension: ${fileExtension}`);
  
  try {
    let content = '';
    
    if (fileExtension === 'txt' || file.type === 'text/plain') {
      content = await readTextFile(file);
    } else if (fileExtension === 'docx' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      content = await readWordFile(file);
    } else if (fileExtension === 'pdf' || file.type === 'application/pdf') {
      content = await readPdfFile(file);
    } else {
      throw new Error(`Unsupported file type: ${fileExtension}. Only TXT, DOCX, and PDF files are supported.`);
    }
    
    console.log(`Successfully read file content (${content.length} characters)`);
    console.log('File content preview:', content.substring(0, 200) + '...');
    
    return content;
  } catch (error) {
    console.error('Error reading file:', error);
    throw error;
  }
}

/**
 * Check if a file type is supported
 * @param file - The file to check
 * @returns boolean - Whether the file type is supported
 */
export function isSupportedFileType(file: File): boolean {
  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.split('.').pop();
  const supportedExtensions = ['txt', 'docx', 'pdf'];
  const supportedMimeTypes = [
    'text/plain',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf'
  ];
  
  return supportedExtensions.includes(fileExtension || '') || 
         supportedMimeTypes.includes(file.type);
}
