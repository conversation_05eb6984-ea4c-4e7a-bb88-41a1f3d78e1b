import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from './schema';

// 创建 libsql 客户端
const client = createClient({
	url: import.meta.env.VITE_TURSO_DATABASE_URL!,
	authToken: import.meta.env.VITE_TURSO_AUTH_TOKEN!,
});

// 创建 Drizzle 数据库实例
export const db = drizzle(client, { schema });

// 导出 schema 以便在其他地方使用
export { schema };

// 导出类型
export type { User, NewUser } from './schema';