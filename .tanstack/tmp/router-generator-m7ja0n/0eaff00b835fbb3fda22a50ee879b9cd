import { createAPIFileRoute, createFileRoute } from "@tanstack/react-router";

/**
 * 文件上传API路由
 * 处理文件上传到DashScope并返回session_file_id
 */
export const Route = createAPIFileRoute("/api/upload-file")(
	{
		POST: async ({ request }) => {
			try {
				// 获取API配置
				const API_KEY = process.env.VITE_DASHSCOPE_API_KEY || "YOUR_API_KEY";
				
				if (!API_KEY || API_KEY === "YOUR_API_KEY") {
					return new Response(
						JSON.stringify({ error: "API密钥未配置" }),
						{ status: 500, headers: { "Content-Type": "application/json" } }
					)
				}

				// 解析FormData
				const formData = await request.formData();
				const file = formData.get("file") as File;

				if (!file) {
					return new Response(
						JSON.stringify({ error: "未找到文件" }),
						{ status: 400, headers: { "Content-Type": "application/json" } }
					)
				}

				// 验证文件大小（最大100MB）
				const MAX_FILE_SIZE = 100 * 1024 * 1024;
				if (file.size > MAX_FILE_SIZE) {
					return new Response(
						JSON.stringify({ error: "文件大小超过100MB限制" }),
						{ status: 400, headers: { "Content-Type": "application/json" } }
					)
				}

				// 验证文件类型
				const allowedTypes = [
					// 文档类型
					"application/pdf",
					"application/msword",
					"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
					"application/vnd.ms-powerpoint",
					"application/vnd.openxmlformats-officedocument.presentationml.presentation",
					"application/vnd.ms-excel",
					"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
					"text/markdown",
					"text/plain",
					// 图片类型
					"image/png",
					"image/jpeg",
					"image/bmp",
					"image/gif",
					// 视频类型
					"video/mp4",
					"video/x-msvideo",
					"video/quicktime",
					"video/x-ms-wmv",
					// 音频类型
					"audio/aac",
					"audio/amr",
					"audio/flac",
					"audio/mpeg",
					"audio/ogg",
					"audio/wav",
					"audio/webm",
				]

				if (!allowedTypes.includes(file.type)) {
					return new Response(
						JSON.stringify({ error: `不支持的文件类型: ${file.type}` }),
						{ status: 400, headers: { "Content-Type": "application/json" } }
					)
				}

				// 创建上传到DashScope的FormData
				const uploadFormData = new FormData();
				uploadFormData.append("file", file);
				uploadFormData.append("purpose", "file-extract");

				// 上传文件到DashScope
				const uploadResponse = await fetch(
					"https://dashscope.aliyuncs.com/api/v1/files",
					{
						method: "POST",
						headers: {
							Authorization: `Bearer ${API_KEY}`,
						},
						body: uploadFormData,
					}
				)

				if (!uploadResponse.ok) {
					const errorText = await uploadResponse.text();
					console.error("DashScope上传失败:", errorText);
					return new Response(
						JSON.stringify({ 
							error: `文件上传失败: ${uploadResponse.status} ${uploadResponse.statusText}`,
							details: errorText
						}),
						{ status: 500, headers: { "Content-Type": "application/json" } }
					)
				}

				const uploadResult = await uploadResponse.json();
				console.log("DashScope上传成功:", uploadResult);

				// 返回文件ID和相关信息
				return new Response(
					JSON.stringify({
						success: true,
						file_id: uploadResult.id,
						filename: uploadResult.filename || file.name,
						size: uploadResult.bytes || file.size,
						status: uploadResult.status,
						created_at: uploadResult.created_at,
					}),
					{ status: 200, headers: { "Content-Type": "application/json" } }
				)
			} catch (error) {
				console.error("文件上传API错误:", error);
				return new Response(
					JSON.stringify({ 
						error: "服务器内部错误",
						details: error instanceof Error ? error.message : "未知错误"
					}),
					{ status: 500, headers: { "Content-Type": "application/json" } }
				)
			}
		},
	}
);