/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as UserSimpleRouteImport } from './routes/user-simple'
import { Route as UserRouteImport } from './routes/user'
import { Route as SettingRouteImport } from './routes/setting'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DashboardIndexRouteImport } from './routes/dashboard/index'
import { Route as AiIndexRouteImport } from './routes/ai/index'
import { Route as DashboardUserRouteImport } from './routes/dashboard/user'
import { Route as AuthLoginRouteImport } from './routes/auth/login'
import { Route as AuthDingtalkCallbackRouteImport } from './routes/auth/dingtalk/callback'
import { Route as ApiAuthDingtalkCallbackRouteImport } from './routes/api/auth/dingtalk-callback'
import { Route as ApiAuthDingtalkRouteImport } from './routes/api/auth/dingtalk'

const UserSimpleRoute = UserSimpleRouteImport.update({
  id: '/user-simple',
  path: '/user-simple',
  getParentRoute: () => rootRouteImport,
} as any)
const UserRoute = UserRouteImport.update({
  id: '/user',
  path: '/user',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingRoute = SettingRouteImport.update({
  id: '/setting',
  path: '/setting',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardIndexRoute = DashboardIndexRouteImport.update({
  id: '/dashboard/',
  path: '/dashboard/',
  getParentRoute: () => rootRouteImport,
} as any)
const AiIndexRoute = AiIndexRouteImport.update({
  id: '/ai/',
  path: '/ai/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardUserRoute = DashboardUserRouteImport.update({
  id: '/dashboard/user',
  path: '/dashboard/user',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/auth/login',
  path: '/auth/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthDingtalkCallbackRoute = AuthDingtalkCallbackRouteImport.update({
  id: '/auth/dingtalk/callback',
  path: '/auth/dingtalk/callback',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiAuthDingtalkCallbackRoute = ApiAuthDingtalkCallbackRouteImport.update({
  id: '/api/auth/dingtalk-callback',
  path: '/api/auth/dingtalk-callback',
  getParentRoute: () => rootRouteImport,
} as any)
const ApiAuthDingtalkRoute = ApiAuthDingtalkRouteImport.update({
  id: '/api/auth/dingtalk',
  path: '/api/auth/dingtalk',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/setting': typeof SettingRoute
  '/user': typeof UserRoute
  '/user-simple': typeof UserSimpleRoute
  '/auth/login': typeof AuthLoginRoute
  '/dashboard/user': typeof DashboardUserRoute
  '/ai': typeof AiIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/api/auth/dingtalk': typeof ApiAuthDingtalkRoute
  '/api/auth/dingtalk-callback': typeof ApiAuthDingtalkCallbackRoute
  '/auth/dingtalk/callback': typeof AuthDingtalkCallbackRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/setting': typeof SettingRoute
  '/user': typeof UserRoute
  '/user-simple': typeof UserSimpleRoute
  '/auth/login': typeof AuthLoginRoute
  '/dashboard/user': typeof DashboardUserRoute
  '/ai': typeof AiIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/api/auth/dingtalk': typeof ApiAuthDingtalkRoute
  '/api/auth/dingtalk-callback': typeof ApiAuthDingtalkCallbackRoute
  '/auth/dingtalk/callback': typeof AuthDingtalkCallbackRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/setting': typeof SettingRoute
  '/user': typeof UserRoute
  '/user-simple': typeof UserSimpleRoute
  '/auth/login': typeof AuthLoginRoute
  '/dashboard/user': typeof DashboardUserRoute
  '/ai/': typeof AiIndexRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/api/auth/dingtalk': typeof ApiAuthDingtalkRoute
  '/api/auth/dingtalk-callback': typeof ApiAuthDingtalkCallbackRoute
  '/auth/dingtalk/callback': typeof AuthDingtalkCallbackRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/setting'
    | '/user'
    | '/user-simple'
    | '/auth/login'
    | '/dashboard/user'
    | '/ai'
    | '/dashboard'
    | '/api/auth/dingtalk'
    | '/api/auth/dingtalk-callback'
    | '/auth/dingtalk/callback'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/setting'
    | '/user'
    | '/user-simple'
    | '/auth/login'
    | '/dashboard/user'
    | '/ai'
    | '/dashboard'
    | '/api/auth/dingtalk'
    | '/api/auth/dingtalk-callback'
    | '/auth/dingtalk/callback'
  id:
    | '__root__'
    | '/'
    | '/setting'
    | '/user'
    | '/user-simple'
    | '/auth/login'
    | '/dashboard/user'
    | '/ai/'
    | '/dashboard/'
    | '/api/auth/dingtalk'
    | '/api/auth/dingtalk-callback'
    | '/auth/dingtalk/callback'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SettingRoute: typeof SettingRoute
  UserRoute: typeof UserRoute
  UserSimpleRoute: typeof UserSimpleRoute
  AuthLoginRoute: typeof AuthLoginRoute
  DashboardUserRoute: typeof DashboardUserRoute
  AiIndexRoute: typeof AiIndexRoute
  DashboardIndexRoute: typeof DashboardIndexRoute
  ApiAuthDingtalkRoute: typeof ApiAuthDingtalkRoute
  ApiAuthDingtalkCallbackRoute: typeof ApiAuthDingtalkCallbackRoute
  AuthDingtalkCallbackRoute: typeof AuthDingtalkCallbackRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/user-simple': {
      id: '/user-simple'
      path: '/user-simple'
      fullPath: '/user-simple'
      preLoaderRoute: typeof UserSimpleRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/user': {
      id: '/user'
      path: '/user'
      fullPath: '/user'
      preLoaderRoute: typeof UserRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/setting': {
      id: '/setting'
      path: '/setting'
      fullPath: '/setting'
      preLoaderRoute: typeof SettingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/ai/': {
      id: '/ai/'
      path: '/ai'
      fullPath: '/ai'
      preLoaderRoute: typeof AiIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/user': {
      id: '/dashboard/user'
      path: '/dashboard/user'
      fullPath: '/dashboard/user'
      preLoaderRoute: typeof DashboardUserRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/auth/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth/dingtalk/callback': {
      id: '/auth/dingtalk/callback'
      path: '/auth/dingtalk/callback'
      fullPath: '/auth/dingtalk/callback'
      preLoaderRoute: typeof AuthDingtalkCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/api/auth/dingtalk-callback': {
      id: '/api/auth/dingtalk-callback'
      path: '/api/auth/dingtalk-callback'
      fullPath: '/api/auth/dingtalk-callback'
      preLoaderRoute: typeof ApiAuthDingtalkCallbackRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/api/auth/dingtalk': {
      id: '/api/auth/dingtalk'
      path: '/api/auth/dingtalk'
      fullPath: '/api/auth/dingtalk'
      preLoaderRoute: typeof ApiAuthDingtalkRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SettingRoute: SettingRoute,
  UserRoute: UserRoute,
  UserSimpleRoute: UserSimpleRoute,
  AuthLoginRoute: AuthLoginRoute,
  DashboardUserRoute: DashboardUserRoute,
  AiIndexRoute: AiIndexRoute,
  DashboardIndexRoute: DashboardIndexRoute,
  ApiAuthDingtalkRoute: ApiAuthDingtalkRoute,
  ApiAuthDingtalkCallbackRoute: ApiAuthDingtalkCallbackRoute,
  AuthDingtalkCallbackRoute: AuthDingtalkCallbackRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
