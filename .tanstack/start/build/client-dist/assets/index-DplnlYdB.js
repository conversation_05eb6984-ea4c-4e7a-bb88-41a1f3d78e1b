import{R as d,j as e,z as b,r as n}from"./main-CXx9abZr.js";import{B as j}from"./button-DSogGQaG.js";import{B as N}from"./badge-CJmvZTiH.js";import{c as l,a as c}from"./index-DFx3G0N8.js";import{c as w}from"./createLucideIcon-6fKCiQbJ.js";import{a as y,u as _}from"./use-auth-DVLhXigO.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],z=w("arrow-right",k),S=c("flex relative z-10 overflow-hidden shadow-2xl border border-border/5 border-t-border/15",{variants:{type:{mobile:"rounded-[48px] max-w-[350px]",responsive:"rounded-md"}},defaultVariants:{type:"responsive"}}),p=d.forwardRef(({className:a,type:t,...r},s)=>e.jsx("div",{ref:s,className:l(S({type:t,className:a})),...r}));p.displayName="Mockup";const R=c("bg-accent/5 flex relative z-10 overflow-hidden rounded-2xl",{variants:{size:{small:"p-2",large:"p-4"}},defaultVariants:{size:"small"}}),x=d.forwardRef(({className:a,size:t,...r},s)=>e.jsx("div",{ref:s,className:l(R({size:t,className:a})),...r}));x.displayName="MockupFrame";const M=c("absolute w-full",{variants:{variant:{top:"top-0",above:"-top-[128px]",bottom:"bottom-0",below:"-bottom-[128px]",center:"top-[50%]"}},defaultVariants:{variant:"top"}}),h=d.forwardRef(({className:a,variant:t,...r},s)=>e.jsxs("div",{ref:s,className:l(M({variant:t}),a),...r,children:[e.jsx("div",{className:l("absolute left-1/2 h-[256px] w-[60%] -translate-x-1/2 scale-[2.5] rounded-[50%] bg-[radial-gradient(ellipse_at_center,_hsla(var(--brand-foreground)/.5)_10%,_hsla(var(--brand-foreground)/0)_60%)] sm:h-[512px]",t==="center"&&"-translate-y-1/2")}),e.jsx("div",{className:l("absolute left-1/2 h-[128px] w-[40%] -translate-x-1/2 scale-[2] rounded-[50%] bg-[radial-gradient(ellipse_at_center,_hsla(var(--brand)/.3)_10%,_hsla(var(--brand-foreground)/0)_60%)] sm:h-[256px]",t==="center"&&"-translate-y-1/2")})]}));h.displayName="Glow";function V({badge:a,title:t,description:r,actions:s,image:o}){const{resolvedTheme:m}=b(),[u,f]=n.useState(o.light),[B,g]=n.useState(!1);return n.useEffect(()=>{g(!0),f(m==="dark"?o.dark:o.light)},[m,o.light,o.dark]),e.jsx("section",{className:l("bg-background text-foreground","py-12 sm:py-24 md:py-32 px-4","fade-bottom overflow-hidden pb-0"),children:e.jsx("div",{className:"mx-auto flex max-w-container flex-col gap-12 pt-16 sm:gap-24",children:e.jsxs("div",{className:"flex flex-col items-center gap-6 text-center sm:gap-12",children:[a&&e.jsxs(N,{variant:"outline",className:"animate-appear gap-2",children:[e.jsx("span",{className:"text-muted-foreground",children:a.text}),e.jsxs("a",{href:a.action.href,className:"flex items-center gap-1",children:[a.action.text,e.jsx(z,{className:"h-3 w-3"})]})]}),e.jsx("h1",{className:"relative z-10 inline-block animate-appear bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-4xl font-semibold leading-tight text-transparent drop-shadow-2xl sm:text-6xl sm:leading-tight md:text-8xl md:leading-tight",children:t}),e.jsx("p",{className:"text-md relative z-10 max-w-[550px] animate-appear font-medium text-muted-foreground opacity-0 delay-100 sm:text-xl",children:r}),e.jsx("div",{className:"relative z-10 flex animate-appear justify-center gap-4 opacity-0 delay-300",children:s.map((i,v)=>e.jsx(j,{variant:i.variant,size:"lg",asChild:!0,children:e.jsxs("a",{href:i.href,className:"flex items-center gap-2",children:[i.icon,i.text]})},v))}),e.jsxs("div",{className:"relative pt-12",children:[e.jsx(x,{className:"animate-appear opacity-0 delay-700",size:"small",children:e.jsx(p,{type:"responsive",children:e.jsx("img",{src:u,alt:o.alt,width:1248,height:765,className:"w-full h-auto",loading:"eager"})})}),e.jsx(h,{variant:"top",className:"animate-appear-zoom opacity-0 delay-1000"})]})]})})})}function A(){const{isAuthenticated:a}=y(),{user:t}=_(),[r,s]=n.useState("/ai");return n.useEffect(()=>{s(a&&t?t.isAdmin?"/dashboard":"/ai":"/auth/login")},[a,t]),e.jsx(V,{badge:{text:"介绍我们的新模型",action:{text:"了解更多",href:"/model"}},title:"库无忧助手",description:"一款专注于石化仓储领域的全链路技术服务平台，旨在为石化仓储工程的建设、运营维护及检修升级提供智能化支持。",actions:[{text:"开始使用",href:r,variant:"default"}],image:{light:"/home.png",dark:"/home.png",alt:"UI组件预览"}})}const G=function(){return e.jsx("div",{className:"min-h-screen",children:e.jsx(A,{})})};export{G as component};
