var Cs=c=>{throw TypeError(c)};var Ue=(c,t,e)=>t.has(c)||Cs("Cannot "+e);var z=(c,t,e)=>(Ue(c,t,"read from private field"),e?e.call(c):t.get(c)),wt=(c,t,e)=>t.has(c)?Cs("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(c):t.set(c,e),ct=(c,t,e,s)=>(Ue(c,t,"write to private field"),s?s.call(c,e):t.set(c,e),e),vt=(c,t,e)=>(Ue(c,t,"access private method"),e);var xs=(c,t,e,s)=>({set _(i){ct(c,t,i,e)},get _(){return z(c,t,s)}});const ot=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser"),Je=[.001,0,0,.001,0,0],je=1.35,ft={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,IS_EDITING:128,OPLIST:256},Dt={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},Xs="pdfjs_internal_editor_",D={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101},O={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},ki={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},nt={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},Te={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},K={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},Xt={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},De={ERRORS:0,WARNINGS:1,INFOS:5},Ie={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},we={moveTo:0,lineTo:1,curveTo:2,closePath:3},Mi={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let Fe=De.WARNINGS;function Li(c){Number.isInteger(c)&&(Fe=c)}function Di(){return Fe}function Ne(c){Fe>=De.INFOS&&console.log(`Info: ${c}`)}function F(c){Fe>=De.WARNINGS&&console.log(`Warning: ${c}`)}function $(c){throw new Error(c)}function J(c,t){c||$(t)}function Fi(c){switch(c?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function Ys(c,t=null,e=null){if(!c)return null;if(e&&typeof c=="string"&&(e.addDefaultProtocol&&c.startsWith("www.")&&c.match(/\./g)?.length>=2&&(c=`http://${c}`),e.tryConvertEncoding))try{c=$i(c)}catch{}const s=t?URL.parse(c,t):URL.parse(c);return Fi(s)?s:null}function Ks(c,t,e=!1){const s=URL.parse(c);return s?(s.hash=t,s.href):e&&Ys(c,"http://example.com")?c.split("#",1)[0]+`${t?`#${t}`:""}`:""}function N(c,t,e,s=!1){return Object.defineProperty(c,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const Vt=function(){function t(e,s){this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();class Ts extends Vt{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Ve extends Vt{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Ze extends Vt{constructor(t){super(t,"InvalidPDFException")}}class ke extends Vt{constructor(t,e,s){super(t,"ResponseException"),this.status=e,this.missing=s}}class Ni extends Vt{constructor(t){super(t,"FormatError")}}class Nt extends Vt{constructor(t){super(t,"AbortException")}}function Qs(c){(typeof c!="object"||c?.length===void 0)&&$("Invalid argument for bytesToString");const t=c.length,e=8192;if(t<e)return String.fromCharCode.apply(null,c);const s=[];for(let i=0;i<t;i+=e){const n=Math.min(i+e,t),r=c.subarray(i,n);s.push(String.fromCharCode.apply(null,r))}return s.join("")}function pe(c){typeof c!="string"&&$("Invalid argument for stringToBytes");const t=c.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=c.charCodeAt(s)&255;return e}function Oi(c){return String.fromCharCode(c>>24&255,c>>16&255,c>>8&255,c&255)}function Bi(){const c=new Uint8Array(4);return c[0]=1,new Uint32Array(c.buffer,0,1)[0]===1}function Hi(){try{return new Function(""),!0}catch{return!1}}class it{static get isLittleEndian(){return N(this,"isLittleEndian",Bi())}static get isEvalSupported(){return N(this,"isEvalSupported",Hi())}static get isOffscreenCanvasSupported(){return N(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get isImageDecoderSupported(){return N(this,"isImageDecoderSupported",typeof ImageDecoder<"u")}static get platform(){const{platform:t,userAgent:e}=navigator;return N(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}static get isCSSRoundSupported(){return N(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const We=Array.from(Array(256).keys(),c=>c.toString(16).padStart(2,"0"));class R{static makeHexColor(t,e,s){return`#${We[t]}${We[e]}${We[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,s=0){const i=t[s],n=t[s+1];t[s]=i*e[0]+n*e[2]+e[4],t[s+1]=i*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,s=0){const i=e[0],n=e[1],r=e[2],a=e[3],o=e[4],l=e[5];for(let h=0;h<6;h+=2){const d=t[s+h],u=t[s+h+1];t[s+h]=d*i+u*r+o,t[s+h+1]=d*n+u*a+l}}static applyInverseTransform(t,e){const s=t[0],i=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(s*e[3]-i*e[2]+e[2]*e[5]-e[4]*e[3])/n,t[1]=(-s*e[1]+i*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,s){const i=e[0],n=e[1],r=e[2],a=e[3],o=e[4],l=e[5],h=t[0],d=t[1],u=t[2],f=t[3];let g=i*h+o,p=g,b=i*u+o,m=b,v=a*d+l,A=v,w=a*f+l,y=w;if(n!==0||r!==0){const _=n*h,S=n*u,E=r*d,C=r*f;g+=E,m+=E,b+=C,p+=C,v+=_,y+=_,w+=S,A+=S}s[0]=Math.min(s[0],g,b,p,m),s[1]=Math.min(s[1],v,w,A,y),s[2]=Math.max(s[2],g,b,p,m),s[3]=Math.max(s[3],v,w,A,y)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const s=t[0],i=t[1],n=t[2],r=t[3],a=s**2+i**2,o=s*n+i*r,l=n**2+r**2,h=(a+l)/2,d=Math.sqrt(h**2-(a*l-o**2));e[0]=Math.sqrt(h+d||1),e[1]=Math.sqrt(h-d||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>r?null:[s,n,i,r]}static pointBoundingBox(t,e,s){s[0]=Math.min(s[0],t),s[1]=Math.min(s[1],e),s[2]=Math.max(s[2],t),s[3]=Math.max(s[3],e)}static rectBoundingBox(t,e,s,i,n){n[0]=Math.min(n[0],t,s),n[1]=Math.min(n[1],e,i),n[2]=Math.max(n[2],t,s),n[3]=Math.max(n[3],e,i)}static#t(t,e,s,i,n,r,a,o,l,h){if(l<=0||l>=1)return;const d=1-l,u=l*l,f=u*l,g=d*(d*(d*t+3*l*e)+3*u*s)+f*i,p=d*(d*(d*n+3*l*r)+3*u*a)+f*o;h[0]=Math.min(h[0],g),h[1]=Math.min(h[1],p),h[2]=Math.max(h[2],g),h[3]=Math.max(h[3],p)}static#e(t,e,s,i,n,r,a,o,l,h,d,u){if(Math.abs(l)<1e-12){Math.abs(h)>=1e-12&&this.#t(t,e,s,i,n,r,a,o,-d/h,u);return}const f=h**2-4*d*l;if(f<0)return;const g=Math.sqrt(f),p=2*l;this.#t(t,e,s,i,n,r,a,o,(-h+g)/p,u),this.#t(t,e,s,i,n,r,a,o,(-h-g)/p,u)}static bezierBoundingBox(t,e,s,i,n,r,a,o,l){l[0]=Math.min(l[0],t,a),l[1]=Math.min(l[1],e,o),l[2]=Math.max(l[2],t,a),l[3]=Math.max(l[3],e,o),this.#e(t,s,n,a,e,i,r,o,3*(-t+3*(s-n)+a),6*(t-2*s+n),3*(s-t),l),this.#e(t,s,n,a,e,i,r,o,3*(-e+3*(i-r)+o),6*(e-2*i+r),3*(i-e),l)}}function $i(c){return decodeURIComponent(escape(c))}let qe=null,Ps=null;function Gi(c){return qe||(qe=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Ps=new Map([["ﬅ","ſt"]])),c.replaceAll(qe,(t,e,s)=>e?e.normalize("NFKC"):Ps.get(s))}function Js(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();const c=new Uint8Array(32);return crypto.getRandomValues(c),Qs(c)}const ls="pdfjs_internal_id_";function zi(c,t,e){if(!Array.isArray(e)||e.length<2)return!1;const[s,i,...n]=e;if(!c(s)&&!Number.isInteger(s)||!t(i))return!1;const r=n.length;let a=!0;switch(i.name){case"XYZ":if(r<2||r>3)return!1;break;case"Fit":case"FitB":return r===0;case"FitH":case"FitBH":case"FitV":case"FitBV":if(r>1)return!1;break;case"FitR":if(r!==4)return!1;a=!1;break;default:return!1}for(const o of n)if(!(typeof o=="number"||a&&o===null))return!1;return!0}function lt(c,t,e){return Math.min(Math.max(c,t),e)}function Zs(c){return Uint8Array.prototype.toBase64?c.toBase64():btoa(Qs(c))}function Ui(c){return Uint8Array.fromBase64?Uint8Array.fromBase64(c):pe(atob(c))}typeof Promise.try!="function"&&(Promise.try=function(c,...t){return new Promise(e=>{e(c(...t))})});typeof Math.sumPrecise!="function"&&(Math.sumPrecise=function(c){return c.reduce((t,e)=>t+e,0)});const Rt="http://www.w3.org/2000/svg";class ee{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function ge(c,t="text"){if(oe(c,document.baseURI)){const e=await fetch(c);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",c,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE){if(i.status===200||i.status===0){switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText);return}s(new Error(i.statusText))}},i.send(null)})}class me{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:n=0,offsetY:r=0,dontFlip:a=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=n,this.offsetY=r,s*=e;const o=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let h,d,u,f;switch(i%=360,i<0&&(i+=360),i){case 180:h=-1,d=0,u=0,f=1;break;case 90:h=0,d=1,u=1,f=0;break;case 270:h=0,d=-1,u=-1,f=0;break;case 0:h=1,d=0,u=0,f=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}a&&(u=-u,f=-f);let g,p,b,m;h===0?(g=Math.abs(l-t[1])*s+n,p=Math.abs(o-t[0])*s+r,b=(t[3]-t[1])*s,m=(t[2]-t[0])*s):(g=Math.abs(o-t[0])*s+n,p=Math.abs(l-t[1])*s+r,b=(t[2]-t[0])*s,m=(t[3]-t[1])*s),this.transform=[h*s,d*s,u*s,f*s,g-h*s*o-u*s*l,p-d*s*o-f*s*l],this.width=b,this.height=m}get rawDims(){const t=this.viewBox;return N(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:n=!1}={}){return new me({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}convertToViewportPoint(t,e){const s=[t,e];return R.applyTransform(s,this.transform),s}convertToViewportRectangle(t){const e=[t[0],t[1]];R.applyTransform(e,this.transform);const s=[t[2],t[3]];return R.applyTransform(s,this.transform),[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){const s=[t,e];return R.applyInverseTransform(s,this.transform),s}}class hs extends Vt{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function Oe(c){const t=c.length;let e=0;for(;e<t&&c[e].trim()==="";)e++;return c.substring(e,e+5).toLowerCase()==="data:"}function cs(c){return typeof c=="string"&&/\.pdf$/i.test(c)}function ji(c){return[c]=c.split(/[#?]/,1),c.substring(c.lastIndexOf("/")+1)}function Vi(c,t="document.pdf"){if(typeof c!="string")return t;if(Oe(c))return F('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const s=(a=>{try{return new URL(a)}catch{try{return new URL(decodeURIComponent(a))}catch{try{return new URL(a,"https://foo.bar")}catch{try{return new URL(decodeURIComponent(a),"https://foo.bar")}catch{return null}}}}})(c);if(!s)return t;const i=a=>{try{let o=decodeURIComponent(a);return o.includes("/")?(o=o.split("/").at(-1),o.test(/^\.pdf$/i)?o:a):o}catch{return a}},n=/\.pdf$/i,r=s.pathname.split("/").at(-1);if(n.test(r))return i(r);if(s.searchParams.size>0){const a=Array.from(s.searchParams.values()).reverse();for(const l of a)if(n.test(l))return i(l);const o=Array.from(s.searchParams.keys()).reverse();for(const l of o)if(n.test(l))return i(l)}if(s.hash){const o=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i.exec(s.hash);if(o)return i(o[0])}return t}class Rs{started=Object.create(null);times=[];time(t){t in this.started&&F(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||F(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:n}of this.times)t.push(`${s.padEnd(e)} ${n-i}ms
`);return t.join("")}}function oe(c,t){const e=t?URL.parse(c,t):URL.parse(c);return e?.protocol==="http:"||e?.protocol==="https:"}function yt(c){c.preventDefault()}function Q(c){c.preventDefault(),c.stopPropagation()}function Wi(c){console.log("Deprecated API usage: "+c)}class ti{static#t;static toDateObject(t){if(!t||typeof t!="string")return null;this.#t||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#t.exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,l==="-"?(r+=h,a+=d):l==="+"&&(r-=h,a-=d),new Date(Date.UTC(s,i,n,r,a,o))}}function qi(c,{scale:t=1,rotation:e=0}){const{width:s,height:i}=c.attributes.style,n=[0,0,parseInt(s),parseInt(i)];return new me({viewBox:n,userUnit:1,scale:t,rotation:e})}function ds(c){if(c.startsWith("#")){const t=parseInt(c.slice(1),16);return[(t&16711680)>>16,(t&65280)>>8,t&255]}return c.startsWith("rgb(")?c.slice(4,-1).split(",").map(t=>parseInt(t)):c.startsWith("rgba(")?c.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(F(`Not a valid color format: "${c}"`),[0,0,0])}function Xi(c){const t=document.createElement("span");t.style.visibility="hidden",t.style.colorScheme="only light",document.body.append(t);for(const e of c.keys()){t.style.color=e;const s=window.getComputedStyle(t).color;c.set(e,ds(s))}t.remove()}function j(c){const{a:t,b:e,c:s,d:i,e:n,f:r}=c.getTransform();return[t,e,s,i,n,r]}function _t(c){const{a:t,b:e,c:s,d:i,e:n,f:r}=c.getTransform().invertSelf();return[t,e,s,i,n,r]}function Ut(c,t,e=!1,s=!0){if(t instanceof me){const{pageWidth:i,pageHeight:n}=t.rawDims,{style:r}=c,a=it.isCSSRoundSupported,o=`var(--total-scale-factor) * ${i}px`,l=`var(--total-scale-factor) * ${n}px`,h=a?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,d=a?`round(down, ${l}, var(--scale-round-y))`:`calc(${l})`;!e||t.rotation%180===0?(r.width=h,r.height=d):(r.width=d,r.height=h)}s&&c.setAttribute("data-main-rotation",t.rotation)}class xt{constructor(){const{pixelRatio:t}=xt;this.sx=t,this.sy=t}get scaled(){return this.sx!==1||this.sy!==1}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,s,i,n=-1){let r=1/0,a=1/0,o=1/0;s=xt.capPixels(s,n),s>0&&(r=Math.sqrt(s/(t*e))),i!==-1&&(a=i/t,o=i/e);const l=Math.min(r,a,o);return this.sx>l||this.sy>l?(this.sx=l,this.sy=l,!0):!1}static get pixelRatio(){return globalThis.devicePixelRatio||1}static capPixels(t,e){if(e>=0){const s=Math.ceil(window.screen.availWidth*window.screen.availHeight*this.pixelRatio**2*(1+e/100));return t>0?Math.min(t,s):s}return t}}const ts=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class ce{#t=null;#e=null;#s;#i=null;#r=null;#n=null;static#o=null;constructor(t){this.#s=t,ce.#o||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){const t=this.#t=document.createElement("div");t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=this.#s._uiManager._signal;t.addEventListener("contextmenu",yt,{signal:e}),t.addEventListener("pointerdown",ce.#a,{signal:e});const s=this.#i=document.createElement("div");s.className="buttons",t.append(s);const i=this.#s.toolbarPosition;if(i){const{style:n}=t,r=this.#s._uiManager.direction==="ltr"?1-i[0]:i[0];n.insetInlineEnd=`${100*r}%`,n.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return t}get div(){return this.#t}static#a(t){t.stopPropagation()}#d(t){this.#s._focusEventsAllowed=!1,Q(t)}#l(t){this.#s._focusEventsAllowed=!0,Q(t)}#u(t){const e=this.#s._uiManager._signal;t.addEventListener("focusin",this.#d.bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",this.#l.bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",yt,{signal:e})}hide(){this.#t.classList.add("hidden"),this.#e?.hideDropdown()}show(){this.#t.classList.remove("hidden"),this.#r?.shown()}addDeleteButton(){const{editorType:t,_uiManager:e}=this.#s,s=document.createElement("button");s.className="delete",s.tabIndex=0,s.setAttribute("data-l10n-id",ce.#o[t]),this.#u(s),s.addEventListener("click",i=>{e.delete()},{signal:e._signal}),this.#i.append(s)}get#c(){const t=document.createElement("div");return t.className="divider",t}async addAltText(t){const e=await t.render();this.#u(e),this.#i.append(e,this.#c),this.#r=t}addColorPicker(t){this.#e=t;const e=t.renderButton();this.#u(e),this.#i.append(e,this.#c)}async addEditSignatureButton(t){const e=this.#n=await t.renderEditButton(this.#s);this.#u(e),this.#i.append(e,this.#c)}async addButton(t,e){switch(t){case"colorPicker":this.addColorPicker(e);break;case"altText":await this.addAltText(e);break;case"editSignature":await this.addEditSignatureButton(e);break;case"delete":this.addDeleteButton();break}}updateEditSignatureButton(t){this.#n&&(this.#n.title=t)}remove(){this.#t.remove(),this.#e?.destroy(),this.#e=null}}class Yi{#t=null;#e=null;#s;constructor(t){this.#s=t}#i(){const t=this.#e=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",yt,{signal:this.#s._signal});const e=this.#t=document.createElement("div");return e.className="buttons",t.append(e),this.#n(),t}#r(t,e){let s=0,i=0;for(const n of t){const r=n.y+n.height;if(r<s)continue;const a=n.x+(e?n.width:0);if(r>s){i=a,s=r;continue}e?a>i&&(i=a):a<i&&(i=a)}return[e?1-i:i,s]}show(t,e,s){const[i,n]=this.#r(e,s),{style:r}=this.#e||=this.#i();t.append(this.#e),r.insetInlineEnd=`${100*i}%`,r.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#e.remove()}#n(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=this.#s._signal;t.addEventListener("contextmenu",yt,{signal:s}),t.addEventListener("click",()=>{this.#s.highlightSelection("floating_button")},{signal:s}),this.#t.append(t)}}function ei(c,t,e){for(const s of e)t.addEventListener(s,c[s].bind(c))}class Ki{#t=0;get id(){return`${Xs}${this.#t++}`}}class us{#t=Js();#e=0;#s=null;static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',s=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),i=new Image;i.src=t;const n=i.decode().then(()=>(s.drawImage(i,0,0,1,1,0,0,1,3),new Uint32Array(s.getImageData(0,0,1,1).data.buffer)[0]===0));return N(this,"_isSVGFittingCanvas",n)}async#i(t,e){this.#s||=new Map;let s=this.#s.get(t);if(s===null)return null;if(s?.bitmap)return s.refCounter+=1,s;try{s||={bitmap:null,id:`image_${this.#t}_${this.#e++}`,refCounter:0,isSvg:!1};let i;if(typeof e=="string"?(s.url=e,i=await ge(e,"blob")):e instanceof File?i=s.file=e:e instanceof Blob&&(i=e),i.type==="image/svg+xml"){const n=us._isSVGFittingCanvas,r=new FileReader,a=new Image,o=new Promise((l,h)=>{a.onload=()=>{s.bitmap=a,s.isSvg=!0,l()},r.onload=async()=>{const d=s.svgUrl=r.result;a.src=await n?`${d}#svgView(preserveAspectRatio(none))`:d},a.onerror=r.onerror=h});r.readAsDataURL(i),await o}else s.bitmap=await createImageBitmap(i);s.refCounter=1}catch(i){F(i),s=null}return this.#s.set(t,s),s&&this.#s.set(s.id,s),s}async getFromFile(t){const{lastModified:e,name:s,size:i,type:n}=t;return this.#i(`${e}_${s}_${i}_${n}`,t)}async getFromUrl(t){return this.#i(t,t)}async getFromBlob(t,e){const s=await e;return this.#i(t,s)}async getFromId(t){this.#s||=new Map;const e=this.#s.get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:s}=e;return delete e.blobPromise,this.getFromBlob(e.id,s)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#s||=new Map;let s=this.#s.get(t);if(s?.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${this.#t}_${this.#e++}`,refCounter:1,isSvg:!1},this.#s.set(t,s),this.#s.set(s.id,s),s}getSvgUrl(t){const e=this.#s.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#s||=new Map;const e=this.#s.get(t);if(!e||(e.refCounter-=1,e.refCounter!==0))return;const{bitmap:s}=e;if(!e.url&&!e.file){const i=new OffscreenCanvas(s.width,s.height);i.getContext("bitmaprenderer").transferFromImageBitmap(s),e.blobPromise=i.convertToBlob()}s.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#t}_`)}}class Qi{#t=[];#e=!1;#s;#i=-1;constructor(t=128){this.#s=t}add({cmd:t,undo:e,post:s,mustExec:i,type:n=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){if(i&&t(),this.#e)return;const o={cmd:t,undo:e,post:s,type:n};if(this.#i===-1){this.#t.length>0&&(this.#t.length=0),this.#i=0,this.#t.push(o);return}if(r&&this.#t[this.#i].type===n){a&&(o.undo=this.#t[this.#i].undo),this.#t[this.#i]=o;return}const l=this.#i+1;l===this.#s?this.#t.splice(0,1):(this.#i=l,l<this.#t.length&&this.#t.splice(l)),this.#t.push(o)}undo(){if(this.#i===-1)return;this.#e=!0;const{undo:t,post:e}=this.#t[this.#i];t(),e?.(),this.#e=!1,this.#i-=1}redo(){if(this.#i<this.#t.length-1){this.#i+=1,this.#e=!0;const{cmd:t,post:e}=this.#t[this.#i];t(),e?.(),this.#e=!1}}hasSomethingToUndo(){return this.#i!==-1}hasSomethingToRedo(){return this.#i<this.#t.length-1}cleanType(t){if(this.#i!==-1){for(let e=this.#i;e>=0;e--)if(this.#t[e].type!==t){this.#t.splice(e+1,this.#i-e),this.#i=e;return}this.#t.length=0,this.#i=-1}}destroy(){this.#t=null}}class be{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=it.platform;for(const[s,i,n={}]of t)for(const r of s){const a=r.startsWith("mac+");e&&a?(this.callbacks.set(r.slice(4),{callback:i,options:n}),this.allKeys.add(r.split("+").at(-1))):!e&&!a&&(this.callbacks.set(r,{callback:i,options:n}),this.allKeys.add(r.split("+").at(-1)))}}#t(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(this.#t(e));if(!s)return;const{callback:i,options:{bubbles:n=!1,args:r=[],checker:a=null}}=s;a&&!a(t,e)||(i.bind(t,...r,e)(),n||Q(e))}}class fs{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return Xi(t),N(this,"_colors",t)}convert(t){const e=ds(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((n,r)=>n===e[r]))return fs._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?R.makeHexColor(...e):t}}class Ot{#t=new AbortController;#e=null;#s=new Map;#i=new Map;#r=null;#n=null;#o=null;#a=new Qi;#d=null;#l=null;#u=0;#c=new Set;#f=null;#p=null;#g=new Set;_editorUndoBar=null;#h=!1;#m=!1;#b=!1;#A=null;#w=null;#v=null;#_=null;#C=!1;#T=null;#R=new Ki;#S=!1;#P=!1;#x=null;#k=null;#M=null;#L=null;#$=null;#E=D.NONE;#y=new Set;#O=null;#B=null;#U=null;#j=null;#V={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#G=[0,0];#D=null;#F=null;#z=null;#Y=null;#N=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=Ot.prototype,e=r=>r.#F.contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&r.hasSomethingToControl(),s=(r,{target:a})=>{if(a instanceof HTMLInputElement){const{type:o}=a;return o!=="text"&&o!=="number"}return!0},i=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return N(this,"_keyboardManager",new be([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(r,{target:a})=>!(a instanceof HTMLButtonElement)&&r.#F.contains(a)&&!r.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(r,{target:a})=>!(a instanceof HTMLButtonElement)&&r.#F.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}constructor(t,e,s,i,n,r,a,o,l,h,d,u,f,g,p){const b=this._signal=this.#t.signal;this.#F=t,this.#z=e,this.#Y=s,this.#r=i,this.#B=n,this._eventBus=r,r._on("editingaction",this.onEditingAction.bind(this),{signal:b}),r._on("pagechanging",this.onPageChanging.bind(this),{signal:b}),r._on("scalechanging",this.onScaleChanging.bind(this),{signal:b}),r._on("rotationchanging",this.onRotationChanging.bind(this),{signal:b}),r._on("setpreference",this.onSetPreference.bind(this),{signal:b}),r._on("switchannotationeditorparams",m=>this.updateParams(m.type,m.value),{signal:b}),this.#nt(),this.#lt(),this.#Q(),this.#n=a.annotationStorage,this.#A=a.filterFactory,this.#U=o,this.#_=l||null,this.#h=h,this.#m=d,this.#b=u,this.#$=f||null,this.viewParameters={realScale:ee.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=g||null,this._supportsPinchToZoom=p!==!1}destroy(){this.#N?.resolve(),this.#N=null,this.#t?.abort(),this.#t=null,this._signal=null;for(const t of this.#i.values())t.destroy();this.#i.clear(),this.#s.clear(),this.#g.clear(),this.#L?.clear(),this.#e=null,this.#y.clear(),this.#a.destroy(),this.#r?.destroy(),this.#B?.destroy(),this.#T?.hide(),this.#T=null,this.#M?.destroy(),this.#M=null,this.#w&&(clearTimeout(this.#w),this.#w=null),this.#D&&(clearTimeout(this.#D),this.#D=null),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#$}get useNewAltTextFlow(){return this.#m}get useNewAltTextWhenAddingImage(){return this.#b}get hcmFilter(){return N(this,"hcmFilter",this.#U?this.#A.addHCMFilter(this.#U.foreground,this.#U.background):"none")}get direction(){return N(this,"direction",getComputedStyle(this.#F).direction)}get highlightColors(){return N(this,"highlightColors",this.#_?new Map(this.#_.split(",").map(t=>(t=t.split("=").map(e=>e.trim()),t[1]=t[1].toUpperCase(),t))):null)}get highlightColorNames(){return N(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),this.#l=t}setMainHighlightColorPicker(t){this.#M=t}editAltText(t,e=!1){this.#r?.editAltText(this,t,e)}getSignature(t){this.#B?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#B}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){switch(t){case"enableNewAltTextWhenAddingImage":this.#b=e;break}}onPageChanging({pageNumber:t}){this.#u=t-1}focusMainContainer(){this.#F.focus()}findParent(t,e){for(const s of this.#i.values()){const{x:i,y:n,width:r,height:a}=s.div.getBoundingClientRect();if(t>=i&&t<=i+r&&e>=n&&e<=n+a)return s}return null}disableUserSelect(t=!1){this.#z.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#g.add(t)}removeShouldRescale(t){this.#g.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*ee.PDF_TO_CSS_UNITS;for(const e of this.#g)e.onScaleChanging();this.#l?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#q({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#K(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of this.#i.values())if(s.hasTextLayer(t))return s;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:r}=e,a=e.toString(),l=this.#q(e).closest(".textLayer"),h=this.getSelectionBoxes(l);if(!h)return;e.empty();const d=this.#K(l),u=this.#E===D.NONE,f=()=>{d?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:h,anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:r,text:a}),u&&this.showAllEditors("highlight",!0,!0)};if(u){this.switchToMode(D.HIGHLIGHT,f);return}f()}#st(){const t=document.getSelection();if(!t||t.isCollapsed)return;const s=this.#q(t).closest(".textLayer"),i=this.getSelectionBoxes(s);i&&(this.#T||=new Yi(this),this.#T.show(s,i,this.direction==="ltr"))}addToAnnotationStorage(t){!t.isEmpty()&&this.#n&&!this.#n.has(t.id)&&this.#n.setValue(t.id,t)}a11yAlert(t,e=null){const s=this.#Y;s&&(s.setAttribute("data-l10n-id",t),e?s.setAttribute("data-l10n-args",JSON.stringify(e)):s.removeAttribute("data-l10n-args"))}#it(){const t=document.getSelection();if(!t||t.isCollapsed){this.#O&&(this.#T?.hide(),this.#O=null,this.#I({hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===this.#O)return;const i=this.#q(t).closest(".textLayer");if(!i){this.#O&&(this.#T?.hide(),this.#O=null,this.#I({hasSelectedText:!1}));return}if(this.#T?.hide(),this.#O=e,this.#I({hasSelectedText:!0}),!(this.#E!==D.HIGHLIGHT&&this.#E!==D.NONE)&&(this.#E===D.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#C=this.isShiftKeyDown,!this.isShiftKeyDown)){const n=this.#E===D.HIGHLIGHT?this.#K(i):null;n?.toggleDrawing();const r=new AbortController,a=this.combinedSignal(r),o=l=>{l.type==="pointerup"&&l.button!==0||(r.abort(),n?.toggleDrawing(!0),l.type==="pointerup"&&this.#X("main_toolbar"))};window.addEventListener("pointerup",o,{signal:a}),window.addEventListener("blur",o,{signal:a})}}#X(t=""){this.#E===D.HIGHLIGHT?this.highlightSelection(t):this.#h&&this.#st()}#nt(){document.addEventListener("selectionchange",this.#it.bind(this),{signal:this._signal})}#rt(){if(this.#v)return;this.#v=new AbortController;const t=this.combinedSignal(this.#v);window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})}#at(){this.#v?.abort(),this.#v=null}blur(){if(this.isShiftKeyDown=!1,this.#C&&(this.#C=!1,this.#X("main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#y)if(e.div.contains(t)){this.#k=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#k)return;const[t,e]=this.#k;this.#k=null,e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}#Q(){if(this.#x)return;this.#x=new AbortController;const t=this.combinedSignal(this.#x);window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#ot(){this.#x?.abort(),this.#x=null}#J(){if(this.#d)return;this.#d=new AbortController;const t=this.combinedSignal(this.#d);document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})}#Z(){this.#d?.abort(),this.#d=null}#lt(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#Q(),this.#J()}removeEditListeners(){this.#ot(),this.#Z()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of this.#p)if(s.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const s of this.#p)if(s.isHandlingMimeForPasting(e.type)){s.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){if(t.preventDefault(),this.#e?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const s of this.#y){const i=s.serialize(!0);i&&e.push(i)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const n of e.items)for(const r of this.#p)if(r.isHandlingMimeForPasting(n.type)){r.paste(n,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(n){F(`paste: "${n.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const n=[];for(const o of s){const l=await i.deserialize(o);if(!l)return;n.push(l)}const r=()=>{for(const o of n)this.#tt(o);this.#et(n)},a=()=>{for(const o of n)o.remove()};this.addCommands({cmd:r,undo:a,mustExec:!0})}catch(n){F(`paste: "${n.message}".`)}}keydown(t){!this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!0),this.#E!==D.NONE&&!this.isEditorHandlingKeyboard&&Ot._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,this.#C&&(this.#C=!1,this.#X("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu");break}}#I(t){Object.entries(t).some(([s,i])=>this.#V[s]!==i)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#V,t)}),this.#E===D.HIGHLIGHT&&t.hasSelectedEditor===!1&&this.#H([[O.HIGHLIGHT_FREE,!0]]))}#H(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#rt(),this.#J(),this.#I({isEditing:this.#E!==D.NONE,isEmpty:this.#W(),hasSomethingToUndo:this.#a.hasSomethingToUndo(),hasSomethingToRedo:this.#a.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#at(),this.#Z(),this.#I({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#p){this.#p=t;for(const e of this.#p)this.#H(e.defaultPropertiesToUpdate)}}getId(){return this.#R.id}get currentLayer(){return this.#i.get(this.#u)}getLayer(t){return this.#i.get(t)}get currentPageIndex(){return this.#u}addLayer(t){this.#i.set(t.pageIndex,t),this.#S?t.enable():t.disable()}removeLayer(t){this.#i.delete(t.pageIndex)}async updateMode(t,e=null,s=!1,i=!1){if(this.#E!==t&&!(this.#N&&(await this.#N.promise,!this.#N))){if(this.#N=Promise.withResolvers(),this.#l?.commitOrRemove(),this.#E=t,t===D.NONE){this.setEditingState(!1),this.#ct(),this._editorUndoBar?.hide(),this.#N.resolve();return}t===D.SIGNATURE&&await this.#B?.loadSignatures(),this.setEditingState(!0),await this.#ht(),this.unselectAll();for(const n of this.#i.values())n.updateMode(t);if(!e){s&&this.addNewEditorFromKeyboard(),this.#N.resolve();return}for(const n of this.#s.values())n.annotationElementId===e||n.id===e?(this.setSelected(n),i&&n.enterInEditMode()):n.unselect();this.#N.resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t.mode!==this.#E&&this._eventBus.dispatch("switchannotationeditormode",{source:this,...t})}updateParams(t,e){if(this.#p){switch(t){case O.CREATE:this.currentLayer.addNewEditor(e);return;case O.HIGHLIGHT_DEFAULT_COLOR:this.#M?.updateColor(e);break;case O.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#j||=new Map).set(t,e),this.showAllEditors("highlight",e);break}for(const s of this.#y)s.updateParams(t,e);for(const s of this.#p)s.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){for(const n of this.#s.values())n.editorType===t&&n.show(e);(this.#j?.get(O.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#H([[O.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#P!==t){this.#P=t;for(const e of this.#i.values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}async#ht(){if(!this.#S){this.#S=!0;const t=[];for(const e of this.#i.values())t.push(e.enable());await Promise.all(t);for(const e of this.#s.values())e.enable()}}#ct(){if(this.unselectAll(),this.#S){this.#S=!1;for(const t of this.#i.values())t.disable();for(const t of this.#s.values())t.disable()}}getEditors(t){const e=[];for(const s of this.#s.values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return this.#s.get(t)}addEditor(t){this.#s.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#w&&clearTimeout(this.#w),this.#w=setTimeout(()=>{this.focusMainContainer(),this.#w=null},0)),this.#s.delete(t.id),t.annotationElementId&&this.#L?.delete(t.annotationElementId),this.unselect(t),(!t.annotationElementId||!this.#c.has(t.annotationElementId))&&this.#n?.remove(t.id)}addDeletedAnnotationElement(t){this.#c.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#c.has(t)}removeDeletedAnnotationElement(t){this.#c.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#tt(t){const e=this.#i.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){this.#e!==t&&(this.#e=t,t&&this.#H(t.propertiesToUpdate))}get#dt(){let t=null;for(t of this.#y);return t}updateUI(t){this.#dt===t&&this.#H(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#H(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#y.has(t)){this.#y.delete(t),t.unselect(),this.#I({hasSelectedEditor:this.hasSelection});return}this.#y.add(t),t.select(),this.#H(t.propertiesToUpdate),this.#I({hasSelectedEditor:!0})}setSelected(t){this.updateToolbar({mode:t.mode,editId:t.id}),this.#l?.commitOrRemove();for(const e of this.#y)e!==t&&e.unselect();this.#y.clear(),this.#y.add(t),t.select(),this.#H(t.propertiesToUpdate),this.#I({hasSelectedEditor:!0})}isSelected(t){return this.#y.has(t)}get firstSelectedEditor(){return this.#y.values().next().value}unselect(t){t.unselect(),this.#y.delete(t),this.#I({hasSelectedEditor:this.hasSelection})}get hasSelection(){return this.#y.size!==0}get isEnterHandled(){return this.#y.size===1&&this.firstSelectedEditor.isEnterHandled}undo(){this.#a.undo(),this.#I({hasSomethingToUndo:this.#a.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#W()}),this._editorUndoBar?.hide()}redo(){this.#a.redo(),this.#I({hasSomethingToUndo:!0,hasSomethingToRedo:this.#a.hasSomethingToRedo(),isEmpty:this.#W()})}addCommands(t){this.#a.add(t),this.#I({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#W()})}cleanUndoStack(t){this.#a.cleanType(t)}#W(){if(this.#s.size===0)return!0;if(this.#s.size===1)for(const t of this.#s.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#y],s=()=>{this._editorUndoBar?.show(i,e.length===1?e[0].editorType:e.length);for(const n of e)n.remove()},i=()=>{for(const n of e)this.#tt(n)};this.addCommands({cmd:s,undo:i,mustExec:!0})}commitOrRemove(){this.#e?.commitOrRemove()}hasSomethingToControl(){return this.#e||this.hasSelection}#et(t){for(const e of this.#y)e.unselect();this.#y.clear();for(const e of t)e.isEmpty()||(this.#y.add(e),e.select());this.#I({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#y)t.commit();this.#et(this.#s.values())}unselectAll(){if(!(this.#e&&(this.#e.commitOrRemove(),this.#E!==D.NONE))&&!this.#l?.commitOrRemove()&&this.hasSelection){for(const t of this.#y)t.unselect();this.#y.clear(),this.#I({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;this.#G[0]+=t,this.#G[1]+=e;const[i,n]=this.#G,r=[...this.#y],a=1e3;this.#D&&clearTimeout(this.#D),this.#D=setTimeout(()=>{this.#D=null,this.#G[0]=this.#G[1]=0,this.addCommands({cmd:()=>{for(const o of r)this.#s.has(o.id)&&(o.translateInPage(i,n),o.translationDone())},undo:()=>{for(const o of r)this.#s.has(o.id)&&(o.translateInPage(-i,-n),o.translationDone())},mustExec:!1})},a);for(const o of r)o.translateInPage(t,e),o.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),this.#f=new Map;for(const t of this.#y)this.#f.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#f)return!1;this.disableUserSelect(!1);const t=this.#f;this.#f=null;let e=!1;for(const[{x:i,y:n,pageIndex:r},a]of t)a.newX=i,a.newY=n,a.newPageIndex=r,e||=i!==a.savedX||n!==a.savedY||r!==a.savedPageIndex;if(!e)return!1;const s=(i,n,r,a)=>{if(this.#s.has(i.id)){const o=this.#i.get(a);o?i._setParentAndPosition(o,n,r):(i.pageIndex=a,i.x=n,i.y=r)}};return this.addCommands({cmd:()=>{for(const[i,{newX:n,newY:r,newPageIndex:a}]of t)s(i,n,r,a)},undo:()=>{for(const[i,{savedX:n,savedY:r,savedPageIndex:a}]of t)s(i,n,r,a)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#f)for(const s of this.#f.keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||this.#y.size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#e===t}getActive(){return this.#e}getMode(){return this.#E}get imageManager(){return N(this,"imageManager",new us)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let l=0,h=e.rangeCount;l<h;l++)if(!t.contains(e.getRangeAt(l).commonAncestorContainer))return null;const{x:s,y:i,width:n,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(l,h,d,u)=>({x:(h-i)/r,y:1-(l+d-s)/n,width:u/r,height:d/n});break;case"180":a=(l,h,d,u)=>({x:1-(l+d-s)/n,y:1-(h+u-i)/r,width:d/n,height:u/r});break;case"270":a=(l,h,d,u)=>({x:1-(h+u-i)/r,y:(l-s)/n,width:u/r,height:d/n});break;default:a=(l,h,d,u)=>({x:(l-s)/n,y:(h-i)/r,width:d/n,height:u/r});break}const o=[];for(let l=0,h=e.rangeCount;l<h;l++){const d=e.getRangeAt(l);if(!d.collapsed)for(const{x:u,y:f,width:g,height:p}of d.getClientRects())g===0||p===0||o.push(a(u,f,g,p))}return o.length===0?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#o||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#o?.delete(t)}renderAnnotationElement(t){const e=this.#o?.get(t.data.id);if(!e)return;const s=this.#n.getRawValue(e);s&&(this.#E===D.NONE&&!s.hasBeenModified||s.renderAnnotationElement(t))}setMissingCanvas(t,e,s){const i=this.#L?.get(t);i&&(i.setCanvas(e,s),this.#L.delete(t))}addMissingCanvas(t,e){(this.#L||=new Map).set(t,e)}}class Ct{#t=null;#e=!1;#s=null;#i=null;#r=null;#n=null;#o=!1;#a=null;#d=null;#l=null;#u=null;#c=!1;static#f=null;static _l10n=null;constructor(t){this.#d=t,this.#c=t._uiManager.useNewAltTextFlow,Ct.#f||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){Ct._l10n??=t}async render(){const t=this.#s=document.createElement("button");t.className="altText",t.tabIndex="0";const e=this.#i=document.createElement("span");t.append(e),this.#c?(t.classList.add("new"),t.setAttribute("data-l10n-id",Ct.#f.missing),e.setAttribute("data-l10n-id",Ct.#f["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const s=this.#d._uiManager._signal;t.addEventListener("contextmenu",yt,{signal:s}),t.addEventListener("pointerdown",n=>n.stopPropagation(),{signal:s});const i=n=>{n.preventDefault(),this.#d._uiManager.editAltText(this.#d),this.#c&&this.#d._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#p}})};return t.addEventListener("click",i,{capture:!0,signal:s}),t.addEventListener("keydown",n=>{n.target===t&&n.key==="Enter"&&(this.#o=!0,i(n))},{signal:s}),await this.#g(),t}get#p(){return this.#t&&"added"||this.#t===null&&this.guessedText&&"review"||"missing"}finish(){this.#s&&(this.#s.focus({focusVisible:this.#o}),this.#o=!1)}isEmpty(){return this.#c?this.#t===null:!this.#t&&!this.#e}hasData(){return this.#c?this.#t!==null||!!this.#l:this.isEmpty()}get guessedText(){return this.#l}async setGuessedText(t){this.#t===null&&(this.#l=t,this.#u=await Ct._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t}),this.#g())}toggleAltTextBadge(t=!1){if(!this.#c||this.#t){this.#a?.remove(),this.#a=null;return}if(!this.#a){const e=this.#a=document.createElement("div");e.className="noAltTextBadge",this.#d.div.append(e)}this.#a.classList.toggle("hidden",!t)}serialize(t){let e=this.#t;return!t&&this.#l===e&&(e=this.#u),{altText:e,decorative:this.#e,guessedText:this.#l,textWithDisclaimer:this.#u}}get data(){return{altText:this.#t,decorative:this.#e}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:n=!1}){s&&(this.#l=s,this.#u=i),!(this.#t===t&&this.#e===e)&&(n||(this.#t=t,this.#e=e),this.#g())}toggle(t=!1){this.#s&&(!t&&this.#n&&(clearTimeout(this.#n),this.#n=null),this.#s.disabled=!t)}shown(){this.#d._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#p}})}destroy(){this.#s?.remove(),this.#s=null,this.#i=null,this.#r=null,this.#a?.remove(),this.#a=null}async#g(){const t=this.#s;if(!t)return;if(this.#c){if(t.classList.toggle("done",!!this.#t),t.setAttribute("data-l10n-id",Ct.#f[this.#p]),this.#i?.setAttribute("data-l10n-id",Ct.#f[`${this.#p}-label`]),!this.#t){this.#r?.remove();return}}else{if(!this.#t&&!this.#e){t.classList.remove("done"),this.#r?.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#r;if(!e){this.#r=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${this.#d.id}`;const i=100,n=this.#d._uiManager._signal;n.addEventListener("abort",()=>{clearTimeout(this.#n),this.#n=null},{once:!0}),t.addEventListener("mouseenter",()=>{this.#n=setTimeout(()=>{this.#n=null,this.#r.classList.add("show"),this.#d._reportTelemetry({action:"alt_text_tooltip"})},i)},{signal:n}),t.addEventListener("mouseleave",()=>{this.#n&&(clearTimeout(this.#n),this.#n=null),this.#r?.classList.remove("show")},{signal:n})}this.#e?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=this.#t),e.parentNode||t.append(e),this.#d.getElementForAltText()?.setAttribute("aria-describedby",e.id)}}class Be{#t;#e=!1;#s=null;#i;#r;#n;#o;#a=null;#d;#l=null;#u;#c=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:n=null,onPinchEnd:r=null,signal:a}){this.#t=t,this.#s=s,this.#i=e,this.#r=i,this.#n=n,this.#o=r,this.#u=new AbortController,this.#d=AbortSignal.any([a,this.#u.signal]),t.addEventListener("touchstart",this.#f.bind(this),{passive:!1,signal:this.#d})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/xt.pixelRatio}#f(t){if(this.#i?.())return;if(t.touches.length===1){if(this.#a)return;const i=this.#a=new AbortController,n=AbortSignal.any([this.#d,i.signal]),r=this.#t,a={capture:!0,signal:n,passive:!1},o=l=>{l.pointerType==="touch"&&(this.#a?.abort(),this.#a=null)};r.addEventListener("pointerdown",l=>{l.pointerType==="touch"&&(Q(l),o(l))},a),r.addEventListener("pointerup",o,a),r.addEventListener("pointercancel",o,a);return}if(!this.#c){this.#c=new AbortController;const i=AbortSignal.any([this.#d,this.#c.signal]),n=this.#t,r={signal:i,capture:!1,passive:!1};n.addEventListener("touchmove",this.#p.bind(this),r);const a=this.#g.bind(this);n.addEventListener("touchend",a,r),n.addEventListener("touchcancel",a,r),r.capture=!0,n.addEventListener("pointerdown",Q,r),n.addEventListener("pointermove",Q,r),n.addEventListener("pointercancel",Q,r),n.addEventListener("pointerup",Q,r),this.#r?.()}if(Q(t),t.touches.length!==2||this.#s?.()){this.#l=null;return}let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]),this.#l={touch0X:e.screenX,touch0Y:e.screenY,touch1X:s.screenX,touch1Y:s.screenY}}#p(t){if(!this.#l||t.touches.length!==2)return;Q(t);let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]);const{screenX:i,screenY:n}=e,{screenX:r,screenY:a}=s,o=this.#l,{touch0X:l,touch0Y:h,touch1X:d,touch1Y:u}=o,f=d-l,g=u-h,p=r-i,b=a-n,m=Math.hypot(p,b)||1,v=Math.hypot(f,g)||1;if(!this.#e&&Math.abs(v-m)<=Be.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(o.touch0X=i,o.touch0Y=n,o.touch1X=r,o.touch1Y=a,!this.#e){this.#e=!0;return}const A=[(i+r)/2,(n+a)/2];this.#n?.(A,v,m)}#g(t){t.touches.length>=2||(this.#c&&(this.#c.abort(),this.#c=null,this.#o?.()),this.#l&&(Q(t),this.#l=null,this.#e=!1))}destroy(){this.#u?.abort(),this.#u=null,this.#a?.abort(),this.#a=null}}class k{#t=null;#e=null;#s=null;#i=!1;#r=null;#n="";#o=!1;#a=null;#d=null;#l=null;#u=null;#c="";#f=!1;#p=null;#g=!1;#h=!1;#m=!1;#b=null;#A=0;#w=0;#v=null;#_=null;isSelected=!1;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#C=!1;#T=k._zIndex++;static _borderLineWidth=-1;static _colorManager=new fs;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=k.prototype._resizeWithKeyboard,e=Ot.TRANSLATE_SMALL,s=Ot.TRANSLATE_BIG;return N(this,"_resizerKeyboardManager",new be([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],k.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null,this.annotationElementId=t.annotationElementId||null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:n,pageY:r}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[n,r];const[a,o]=this.parentDimensions;this.x=t.x/a,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}get mode(){return Object.getPrototypeOf(this).constructor._editorType}static get isDrawer(){return!1}static get _defaultLineColor(){return N(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new Ji({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(k._l10n??=t,k._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"}),k._borderLineWidth!==-1)return;const s=getComputedStyle(document.documentElement);k._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){$("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#C}set _isDraggable(t){this.#C=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(t*2),this.y+=this.width*t/(e*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(t*2),this.y-=this.width*t/(e*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#T}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#z(),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#f?this.#f=!1:this.parent.setSelected(this))}focusout(t){!this._focusEventsAllowed||!this.isAttachedToDOM||t.relatedTarget?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.isInEditMode()&&this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[n,r]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/n,this.y=(e+i)/r,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[s,i]=this.parentDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),this._onTranslated()}#R([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()}translate(t,e){this.#R(this.parentDimensions,t,e)}translateInPage(t,e){this.#p||=[this.x,this.y,this.width,this.height],this.#R(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#p||=[this.x,this.y,this.width,this.height];const{div:s,parentDimensions:[i,n]}=this;if(this.x+=t/i,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:d,y:u}=this.div.getBoundingClientRect();this.parent.findNewParent(this,d,u)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:r,y:a}=this;const[o,l]=this.getBaseTranslation();r+=o,a+=l;const{style:h}=s;h.left=`${(100*r).toFixed(2)}%`,h.top=`${(100*a).toFixed(2)}%`,this._onTranslating(r,a),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#p&&(this.#p[0]!==this.x||this.#p[1]!==this.y)}get _hasBeenResized(){return!!this.#p&&(this.#p[2]!==this.width||this.#p[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=k,i=s/t,n=s/e;switch(this.rotation){case 90:return[-i,n];case 180:return[i,n];case 270:return[i,-n];default:return[-i,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:n,y:r,width:a,height:o}=this;if(a*=s,o*=i,n*=s,r*=i,this._mustFixPosition)switch(t){case 0:n=lt(n,0,s-a),r=lt(r,0,i-o);break;case 90:n=lt(n,0,s-o),r=lt(r,a,i);break;case 180:n=lt(n,a,s),r=lt(r,o,i);break;case 270:n=lt(n,o,s),r=lt(r,0,i-a);break}this.x=n/=s,this.y=r/=i;const[l,h]=this.getBaseTranslation();n+=l,r+=h,e.left=`${(100*n).toFixed(2)}%`,e.top=`${(100*r).toFixed(2)}%`,this.moveInDOM()}static#S(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return k.#S(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return k.#S(t,e,360-this.parentRotation)}#P(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/s).toFixed(2)}%`,this.#o||(n.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),n=!this.#o&&e.endsWith("%");if(i&&n)return;const[r,a]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/r).toFixed(2)}%`),!this.#o&&!n&&(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#x(){if(this.#a)return;this.#a=document.createElement("div"),this.#a.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const i=document.createElement("div");this.#a.append(i),i.classList.add("resizer",s),i.setAttribute("data-resizer-name",s),i.addEventListener("pointerdown",this.#k.bind(this,s),{signal:e}),i.addEventListener("contextmenu",yt,{signal:e}),i.tabIndex=-1}this.div.prepend(this.#a)}#k(t,e){e.preventDefault();const{isMac:s}=it.platform;if(e.button!==0||e.ctrlKey&&s)return;this.#s?.toggle(!1);const i=this._isDraggable;this._isDraggable=!1,this.#d=[e.screenX,e.screenY];const n=new AbortController,r=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",this.#$.bind(this,t),{passive:!0,capture:!0,signal:r}),window.addEventListener("touchmove",Q,{passive:!1,signal:r}),window.addEventListener("contextmenu",yt,{signal:r}),this.#l={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const a=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const l=()=>{n.abort(),this.parent.togglePointerEvents(!0),this.#s?.toggle(!0),this._isDraggable=i,this.parent.div.style.cursor=a,this.div.style.cursor=o,this.#L()};window.addEventListener("pointerup",l,{signal:r}),window.addEventListener("blur",l,{signal:r})}#M(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*s,r*i),this.fixAndSetPosition(),this._onResized()}_onResized(){}#L(){if(!this.#l)return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=this.#l;this.#l=null;const n=this.x,r=this.y,a=this.width,o=this.height;n===t&&r===e&&a===s&&o===i||this.addCommands({cmd:this.#M.bind(this,n,r,a,o),undo:this.#M.bind(this,t,e,s,i),mustExec:!0})}static _round(t){return Math.round(t*1e4)/1e4}#$(t,e){const[s,i]=this.parentDimensions,n=this.x,r=this.y,a=this.width,o=this.height,l=k.MIN_SIZE/s,h=k.MIN_SIZE/i,d=this.#P(this.rotation),u=(T,P)=>[d[0]*T+d[2]*P,d[1]*T+d[3]*P],f=this.#P(360-this.rotation),g=(T,P)=>[f[0]*T+f[2]*P,f[1]*T+f[3]*P];let p,b,m=!1,v=!1;switch(t){case"topLeft":m=!0,p=(T,P)=>[0,0],b=(T,P)=>[T,P];break;case"topMiddle":p=(T,P)=>[T/2,0],b=(T,P)=>[T/2,P];break;case"topRight":m=!0,p=(T,P)=>[T,0],b=(T,P)=>[0,P];break;case"middleRight":v=!0,p=(T,P)=>[T,P/2],b=(T,P)=>[0,P/2];break;case"bottomRight":m=!0,p=(T,P)=>[T,P],b=(T,P)=>[0,0];break;case"bottomMiddle":p=(T,P)=>[T/2,P],b=(T,P)=>[T/2,0];break;case"bottomLeft":m=!0,p=(T,P)=>[0,P],b=(T,P)=>[T,0];break;case"middleLeft":v=!0,p=(T,P)=>[0,P/2],b=(T,P)=>[T,P/2];break}const A=p(a,o),w=b(a,o);let y=u(...w);const _=k._round(n+y[0]),S=k._round(r+y[1]);let E=1,C=1,M,I;if(e.fromKeyboard)({deltaX:M,deltaY:I}=e);else{const{screenX:T,screenY:P}=e,[gt,Tt]=this.#d;[M,I]=this.screenToPageTranslation(T-gt,P-Tt),this.#d[0]=T,this.#d[1]=P}if([M,I]=g(M/s,I/i),m){const T=Math.hypot(a,o);E=C=Math.max(Math.min(Math.hypot(w[0]-A[0]-M,w[1]-A[1]-I)/T,1/a,1/o),l/a,h/o)}else v?E=lt(Math.abs(w[0]-A[0]-M),l,1)/a:C=lt(Math.abs(w[1]-A[1]-I),h,1)/o;const H=k._round(a*E),G=k._round(o*C);y=u(...b(H,G));const B=_-y[0],rt=S-y[1];this.#p||=[this.x,this.y,this.width,this.height],this.width=H,this.height=G,this.x=B,this.y=rt,this.setDims(s*H,i*G),this.fixAndSetPosition(),this._onResizing()}_onResizing(){}altTextFinish(){this.#s?.finish()}get toolbarButtons(){return null}async addEditToolbar(){if(this._editToolbar||this.#h)return this._editToolbar;this._editToolbar=new ce(this),this.div.append(this._editToolbar.render());const{toolbarButtons:t}=this;if(t)for(const[e,s]of t)await this._editToolbar.addButton(e,s);return this._editToolbar.addButton("delete"),this._editToolbar}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,this.#s?.destroy())}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}createAltText(){return this.#s||(Ct.initialize(k._l10n),this.#s=new Ct(this),this.#t&&(this.#s.data=this.#t,this.#t=null)),this.#s}get altTextData(){return this.#s?.data}set altTextData(t){this.#s&&(this.#s.data=t)}get guessedAltText(){return this.#s?.guessedText}async setGuessedAltText(t){await this.#s?.setGuessedText(t)}serializeAltText(t){return this.#s?.serialize(t)}hasAltText(){return!!this.#s&&!this.#s.isEmpty()}hasAltTextData(){return this.#s?.hasData()??!1}render(){const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=this.#i?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),this.#j();const[e,s]=this.parentDimensions;this.parentRotation%180!==0&&(t.style.maxWidth=`${(100*s/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/s).toFixed(2)}%`);const[i,n]=this.getInitialTranslation();return this.translate(i,n),ei(this,t,["keydown","pointerdown","dblclick"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#_||=new Be({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#E.bind(this),onPinching:this.#y.bind(this),onPinchEnd:this.#O.bind(this),signal:this._uiManager._signal})),this._uiManager._editorUndoBar?.hide(),t}#E(){this.#l={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height},this.#s?.toggle(!1),this.parent.togglePointerEvents(!1)}#y(t,e,s){let n=.7*(s/e)+1-.7;if(n===1)return;const r=this.#P(this.rotation),a=(_,S)=>[r[0]*_+r[2]*S,r[1]*_+r[3]*S],[o,l]=this.parentDimensions,h=this.x,d=this.y,u=this.width,f=this.height,g=k.MIN_SIZE/o,p=k.MIN_SIZE/l;n=Math.max(Math.min(n,1/u,1/f),g/u,p/f);const b=k._round(u*n),m=k._round(f*n);if(b===u&&m===f)return;this.#p||=[h,d,u,f];const v=a(u/2,f/2),A=k._round(h+v[0]),w=k._round(d+v[1]),y=a(b/2,m/2);this.x=A-y[0],this.y=w-y[1],this.width=b,this.height=m,this.setDims(o*b,l*m),this.fixAndSetPosition(),this._onResizing()}#O(){this.#s?.toggle(!0),this.parent.togglePointerEvents(!0),this.#L()}pointerdown(t){const{isMac:e}=it.platform;if(t.button!==0||t.ctrlKey&&e){t.preventDefault();return}if(this.#f=!0,this._isDraggable){this.#U(t);return}this.#B(t)}#B(t){const{isMac:e}=it.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#U(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,n=this._uiManager.combinedSignal(i),r={capture:!0,passive:!1,signal:n},a=l=>{i.abort(),this.#r=null,this.#f=!1,this._uiManager.endDragSession()||this.#B(l),s&&this._onStopDragging()};e&&(this.#A=t.clientX,this.#w=t.clientY,this.#r=t.pointerId,this.#n=t.pointerType,window.addEventListener("pointermove",l=>{s||(s=!0,this._onStartDragging());const{clientX:h,clientY:d,pointerId:u}=l;if(u!==this.#r){Q(l);return}const[f,g]=this.screenToPageTranslation(h-this.#A,d-this.#w);this.#A=h,this.#w=d,this._uiManager.dragSelectedEditors(f,g)},r),window.addEventListener("touchmove",Q,r),window.addEventListener("pointerdown",l=>{l.pointerType===this.#n&&(this.#_||l.isPrimary)&&a(l),Q(l)},r));const o=l=>{if(!this.#r||this.#r===l.pointerId){a(l);return}Q(l)};window.addEventListener("pointerup",o,{signal:n}),window.addEventListener("blur",o,{signal:n})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#b&&clearTimeout(this.#b),this.#b=setTimeout(()=>{this.#b=null,this.parent?.moveEditorInDOM(this)},0)}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[n,r]=this.pageDimensions,[a,o]=this.pageTranslation,l=t/i,h=e/i,d=this.x*n,u=this.y*r,f=this.width*n,g=this.height*r;switch(s){case 0:return[d+l+a,r-u-h-g+o,d+l+f+a,r-u-h+o];case 90:return[d+h+a,r-u+l+o,d+h+g+a,r-u+l+f+o];case 180:return[d-l-f+a,r-u+h+o,d-l+a,r-u+h+g+o];case 270:return[d-h-g+a,r-u-l-f+o,d-h+a,r-u-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,n,r]=t,a=n-s,o=r-i;switch(this.rotation){case 0:return[s,e-r,a,o];case 90:return[s,e-i,o,a];case 180:return[n,e-i,a,o];case 270:return[n,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){return this.isInEditMode()?!1:(this.parent.setEditingState(!1),this.#h=!0,!0)}disableEditMode(){return this.isInEditMode()?(this.parent.setEditingState(!0),this.#h=!1,!0):!1}isInEditMode(){return this.#h}shouldGetKeyboardEvents(){return this.#m}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:n,innerWidth:r}=window;return e<r&&i>0&&t<n&&s>0}#j(){if(this.#u||!this.div)return;this.#u=new AbortController;const t=this._uiManager.combinedSignal(this.#u);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#j()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){$("An editor must be serializable")}static async deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s,annotationElementId:t.annotationElementId});i.rotation=t.rotation,i.#t=t.accessibilityData,i._isCopy=t.isCopy||!1;const[n,r]=i.pageDimensions,[a,o,l,h]=i.getRectInCurrentCoords(t.rect,r);return i.x=a/n,i.y=o/r,i.width=l/n,i.height=h/r,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){if(this.#u?.abort(),this.#u=null,this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#b&&(clearTimeout(this.#b),this.#b=null),this.#z(),this.removeEditToolbar(),this.#v){for(const t of this.#v.values())clearTimeout(t);this.#v=null}this.parent=null,this.#_?.destroy(),this.#_=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#x(),this.#a.classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),this.#l={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#a.children;if(!this.#e){this.#e=Array.from(e);const r=this.#V.bind(this),a=this.#G.bind(this),o=this._uiManager._signal;for(const l of this.#e){const h=l.getAttribute("data-resizer-name");l.setAttribute("role","spinbutton"),l.addEventListener("keydown",r,{signal:o}),l.addEventListener("blur",a,{signal:o}),l.addEventListener("focus",this.#D.bind(this,h),{signal:o}),l.setAttribute("data-l10n-id",k._l10nResizer[h])}}const s=this.#e[0];let i=0;for(const r of e){if(r===s)break;i++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#e.length/4);if(n!==i){if(n<i)for(let a=0;a<i-n;a++)this.#a.append(this.#a.firstChild);else if(n>i)for(let a=0;a<n-i;a++)this.#a.firstChild.before(this.#a.lastChild);let r=0;for(const a of e){const l=this.#e[r++].getAttribute("data-resizer-name");a.setAttribute("data-l10n-id",k._l10nResizer[l])}}this.#F(0),this.#m=!0,this.#a.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#V(t){k._resizerKeyboardManager.exec(this,t)}#G(t){this.#m&&t.relatedTarget?.parentNode!==this.#a&&this.#z()}#D(t){this.#c=this.#m?t:""}#F(t){if(this.#e)for(const e of this.#e)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#m&&this.#$(this.#c,{deltaX:t,deltaY:e,fromKeyboard:!0})}#z(){this.#m=!1,this.#F(-1),this.#L()}_stopResizingWithKeyboard(){this.#z(),this.div.focus()}select(){if(!(this.isSelected&&this._editToolbar)){if(this.isSelected=!0,this.makeResizable(),this.div?.classList.add("selectedEditor"),!this._editToolbar){this.addEditToolbar().then(()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()});return}this._editToolbar?.show(),this.#s?.toggleAltTextBadge(!1)}}unselect(){this.isSelected&&(this.isSelected=!1,this.#a?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),this.#s?.toggleAltTextBadge(!0))}updateParams(t,e){}disableEditing(){}enableEditing(){}get canChangeContent(){return!1}enterInEditMode(){this.canChangeContent&&(this.enableEditMode(),this.div.focus())}dblclick(t){this.enterInEditMode(),this.parent.updateToolbar({mode:this.constructor._editorType,editId:this.id})}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#g}set isEditing(t){this.#g=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#o=!0;const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#v||=new Map;const{action:s}=t;let i=this.#v.get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),this.#v.delete(s),this.#v.size===0&&(this.#v=null)},k._telemetryTimeout),this.#v.set(s,i);return}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#i=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#i=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(!e)e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);else if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;e?.nodeName==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}}class Ji extends k{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const Is=3285377520,mt=4294901760,St=65535;class si{constructor(t){this.h1=t?t&4294967295:Is,this.h2=t?t&4294967295:Is}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(t.length*2),s=0;for(let p=0,b=t.length;p<b;p++){const m=t.charCodeAt(p);m<=255?e[s++]=m:(e[s++]=m>>>8,e[s++]=m&255)}}else if(ArrayBuffer.isView(t))e=t.slice(),s=e.byteLength;else throw new Error("Invalid data format, must be a string or TypedArray.");const i=s>>2,n=s-i*4,r=new Uint32Array(e.buffer,0,i);let a=0,o=0,l=this.h1,h=this.h2;const d=3432918353,u=461845907,f=d&St,g=u&St;for(let p=0;p<i;p++)p&1?(a=r[p],a=a*d&mt|a*f&St,a=a<<15|a>>>17,a=a*u&mt|a*g&St,l^=a,l=l<<13|l>>>19,l=l*5+3864292196):(o=r[p],o=o*d&mt|o*f&St,o=o<<15|o>>>17,o=o*u&mt|o*g&St,h^=o,h=h<<13|h>>>19,h=h*5+3864292196);switch(a=0,n){case 3:a^=e[i*4+2]<<16;case 2:a^=e[i*4+1]<<8;case 1:a^=e[i*4],a=a*d&mt|a*f&St,a=a<<15|a>>>17,a=a*u&mt|a*g&St,i&1?l^=a:h^=a}this.h1=l,this.h2=h}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=t*3981806797&mt|t*36045&St,e=e*4283543511&mt|((e<<16|t>>>16)*2950163797&mt)>>>16,t^=e>>>1,t=t*444984403&mt|t*60499&St,e=e*3301882366&mt|((e<<16|t>>>16)*3120437893&mt)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const es=Object.freeze({map:null,hash:"",transfer:void 0});class ps{#t=!1;#e=null;#s=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=this.#s.get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return this.#s.get(t)}remove(t){if(this.#s.delete(t),this.#s.size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of this.#s.values())if(e instanceof k)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=this.#s.get(t);let i=!1;if(s!==void 0)for(const[n,r]of Object.entries(e))s[n]!==r&&(i=!0,s[n]=r);else i=!0,this.#s.set(t,e);i&&this.#i(),e instanceof k&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#s.has(t)}get size(){return this.#s.size}#i(){this.#t||(this.#t=!0,typeof this.onSetModified=="function"&&this.onSetModified())}resetModified(){this.#t&&(this.#t=!1,typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new ii(this)}get serializable(){if(this.#s.size===0)return es;const t=new Map,e=new si,s=[],i=Object.create(null);let n=!1;for(const[r,a]of this.#s){const o=a instanceof k?a.serialize(!1,i):a;o&&(t.set(r,o),e.update(`${r}:${JSON.stringify(o)}`),n||=!!o.bitmap)}if(n)for(const r of t.values())r.bitmap&&s.push(r.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:es}get editorStats(){let t=null;const e=new Map;for(const s of this.#s.values()){if(!(s instanceof k))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:n}=i;e.has(n)||e.set(n,Object.getPrototypeOf(s).constructor),t||=Object.create(null);const r=t[n]||=new Map;for(const[a,o]of Object.entries(i)){if(a==="type")continue;let l=r.get(a);l||(l=new Map,r.set(a,l));const h=l.get(o)??0;l.set(o,h+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}resetModifiedIds(){this.#e=null}get modifiedIds(){if(this.#e)return this.#e;const t=[];for(const e of this.#s.values())!(e instanceof k)||!e.annotationElementId||!e.serialize()||t.push(e.annotationElementId);return this.#e={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#s.entries()}}class ii extends ps{#t;constructor(t){super();const{map:e,hash:s,transfer:i}=t.serializable,n=structuredClone(e,i?{transfer:i}:null);this.#t={map:n,hash:s,transfer:i}}get print(){$("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#t}get modifiedIds(){return N(this,"modifiedIds",{ids:new Set,hash:""})}}class Zi{#t=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#t.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:s}){if(!(!t||this.#t.has(t.loadedName))){if(J(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:i,src:n,style:r}=t,a=new FontFace(i,n,r);this.addNativeFontFace(a);try{await a.load(),this.#t.add(i),s?.(t)}catch{F(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}return}$("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{await s.loaded}catch(i){throw F(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return N(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){return N(this,"isSyncFontLoadingSupported",ot||it.platform.isFirefox)}_queueLoadingCallback(t){function e(){for(J(!i.done,"completeRequest() cannot be called twice."),i.done=!0;s.length>0&&s[0].done;){const n=s.shift();setTimeout(n.callback,0)}}const{loadingRequests:s}=this,i={done:!1,complete:e,callback:t};return s.push(i),i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return N(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function s(w,y){return w.charCodeAt(y)<<24|w.charCodeAt(y+1)<<16|w.charCodeAt(y+2)<<8|w.charCodeAt(y+3)&255}function i(w,y,_,S){const E=w.substring(0,y),C=w.substring(y+_);return E+S+C}let n,r;const a=this._document.createElement("canvas");a.width=1,a.height=1;const o=a.getContext("2d");let l=0;function h(w,y){if(++l>30){F("Load test font never loaded."),y();return}if(o.font="30px "+w,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0){y();return}setTimeout(h.bind(null,w,y))}const d=`lt${Date.now()}${this.loadTestFontId++}`;let u=this._loadTestFont;u=i(u,976,d.length,d);const g=16,p=1482184792;let b=s(u,g);for(n=0,r=d.length-3;n<r;n+=4)b=b-p+s(d,n)|0;n<d.length&&(b=b-p+s(d+"XXX",n)|0),u=i(u,g,4,Oi(b));const m=`url(data:font/opentype;base64,${btoa(u)});`,v=`@font-face {font-family:"${d}";src:${m}}`;this.insertRule(v);const A=this._document.createElement("div");A.style.visibility="hidden",A.style.width=A.style.height="10px",A.style.position="absolute",A.style.top=A.style.left="0px";for(const w of[t.loadedName,d]){const y=this._document.createElement("span");y.textContent="Hi",y.style.fontFamily=w,A.append(y)}this._document.body.append(A),h(d,()=>{A.remove(),e.complete()})}}class tn{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const s in t)this[s]=t[s];this._inspectFont=e}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(!this.cssFontInfo)t=new FontFace(this.loadedName,this.data,{});else{const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${Zs(this.data)});`;let e;if(!this.cssFontInfo)e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;else{let s=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(s+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${s}src:${t}}`}return this._inspectFont?.(this,t),e}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(r){F(`getPathGenerator - ignoring character: "${r}".`)}const n=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=n}}function en(c){if(c instanceof URL)return c.href;if(typeof c=="string"){if(ot)return c;const t=URL.parse(c,window.location);if(t)return t.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function sn(c){if(ot&&typeof Buffer<"u"&&c instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(c instanceof Uint8Array&&c.byteLength===c.buffer.byteLength)return c;if(typeof c=="string")return pe(c);if(c instanceof ArrayBuffer||ArrayBuffer.isView(c)||typeof c=="object"&&!isNaN(c?.length))return new Uint8Array(c);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function ve(c){if(typeof c!="string")return null;if(c.endsWith("/"))return c;throw new Error(`Invalid factory url: "${c}" must include trailing slash.`)}const ss=c=>typeof c=="object"&&Number.isInteger(c?.num)&&c.num>=0&&Number.isInteger(c?.gen)&&c.gen>=0,nn=c=>typeof c=="object"&&typeof c?.name=="string",rn=zi.bind(null,ss,nn);class an{#t=new Map;#e=Promise.resolve();postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};this.#e.then(()=>{for(const[i]of this.#t)i.call(this,s)})}addEventListener(t,e,s=null){let i=null;if(s?.signal instanceof AbortSignal){const{signal:n}=s;if(n.aborted){F("LoopbackPort - cannot use an `aborted` signal.");return}const r=()=>this.removeEventListener(t,e);i=()=>n.removeEventListener("abort",r),n.addEventListener("abort",r)}this.#t.set(e,i)}removeEventListener(t,e){this.#t.get(e)?.(),this.#t.delete(e)}terminate(){for(const[,t]of this.#t)t?.();this.#t.clear()}}const _e={DATA:1,ERROR:2},Y={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function ks(){}function dt(c){if(c instanceof Nt||c instanceof Ze||c instanceof Ts||c instanceof ke||c instanceof Ve)return c;switch(c instanceof Error||typeof c=="object"&&c!==null||$('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),c.name){case"AbortException":return new Nt(c.message);case"InvalidPDFException":return new Ze(c.message);case"PasswordException":return new Ts(c.message,c.code);case"ResponseException":return new ke(c.message,c.status,c.missing);case"UnknownErrorException":return new Ve(c.message,c.details)}return new Ve(c.message,c.toString())}class le{#t=new AbortController;constructor(t,e,s){this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",this.#e.bind(this),{signal:this.#t.signal})}#e({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){this.#i(t);return}if(t.callback){const s=t.callbackId,i=this.callbackCapabilities[s];if(!i)throw new Error(`Cannot resolve callback ${s}`);if(delete this.callbackCapabilities[s],t.callback===_e.DATA)i.resolve(t.data);else if(t.callback===_e.ERROR)i.reject(dt(t.reason));else throw new Error("Unexpected callback case");return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,n=this.comObj;Promise.try(e,t.data).then(function(r){n.postMessage({sourceName:s,targetName:i,callback:_e.DATA,callbackId:t.callbackId,data:r})},function(r){n.postMessage({sourceName:s,targetName:i,callback:_e.ERROR,callbackId:t.callbackId,reason:dt(r)})});return}if(t.streamId){this.#s(t);return}e(t.data)}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[i]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(r){n.reject(r)}return n.promise}sendWithStream(t,e,s,i){const n=this.streamId++,r=this.sourceName,a=this.targetName,o=this.comObj;return new ReadableStream({start:l=>{const h=Promise.withResolvers();return this.streamControllers[n]={controller:l,startCall:h,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:r,targetName:a,action:t,streamId:n,data:e,desiredSize:l.desiredSize},i),h.promise},pull:l=>{const h=Promise.withResolvers();return this.streamControllers[n].pullCall=h,o.postMessage({sourceName:r,targetName:a,stream:Y.PULL,streamId:n,desiredSize:l.desiredSize}),h.promise},cancel:l=>{J(l instanceof Error,"cancel must have a valid reason");const h=Promise.withResolvers();return this.streamControllers[n].cancelCall=h,this.streamControllers[n].isClosed=!0,o.postMessage({sourceName:r,targetName:a,stream:Y.CANCEL,streamId:n,reason:dt(l)}),h.promise}},s)}#s(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,r=this,a=this.actionHandler[t.action],o={enqueue(l,h=1,d){if(this.isCancelled)return;const u=this.desiredSize;this.desiredSize-=h,u>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:s,targetName:i,stream:Y.ENQUEUE,streamId:e,chunk:l},d)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:Y.CLOSE,streamId:e}),delete r.streamSinks[e])},error(l){J(l instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:Y.ERROR,streamId:e,reason:dt(l)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,Promise.try(a,t.data,o).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Y.START_COMPLETE,streamId:e,success:!0})},function(l){n.postMessage({sourceName:s,targetName:i,stream:Y.START_COMPLETE,streamId:e,reason:dt(l)})})}#i(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,r=this.streamControllers[e],a=this.streamSinks[e];switch(t.stream){case Y.START_COMPLETE:t.success?r.startCall.resolve():r.startCall.reject(dt(t.reason));break;case Y.PULL_COMPLETE:t.success?r.pullCall.resolve():r.pullCall.reject(dt(t.reason));break;case Y.PULL:if(!a){n.postMessage({sourceName:s,targetName:i,stream:Y.PULL_COMPLETE,streamId:e,success:!0});break}a.desiredSize<=0&&t.desiredSize>0&&a.sinkCapability.resolve(),a.desiredSize=t.desiredSize,Promise.try(a.onPull||ks).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Y.PULL_COMPLETE,streamId:e,success:!0})},function(l){n.postMessage({sourceName:s,targetName:i,stream:Y.PULL_COMPLETE,streamId:e,reason:dt(l)})});break;case Y.ENQUEUE:if(J(r,"enqueue should have stream controller"),r.isClosed)break;r.controller.enqueue(t.chunk);break;case Y.CLOSE:if(J(r,"close should have stream controller"),r.isClosed)break;r.isClosed=!0,r.controller.close(),this.#r(r,e);break;case Y.ERROR:J(r,"error should have stream controller"),r.controller.error(dt(t.reason)),this.#r(r,e);break;case Y.CANCEL_COMPLETE:t.success?r.cancelCall.resolve():r.cancelCall.reject(dt(t.reason)),this.#r(r,e);break;case Y.CANCEL:if(!a)break;const o=dt(t.reason);Promise.try(a.onCancel||ks,o).then(function(){n.postMessage({sourceName:s,targetName:i,stream:Y.CANCEL_COMPLETE,streamId:e,success:!0})},function(l){n.postMessage({sourceName:s,targetName:i,stream:Y.CANCEL_COMPLETE,streamId:e,reason:dt(l)})}),a.sinkCapability.reject(o),a.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#r(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.#t?.abort(),this.#t=null}}class ni{#t=!1;constructor({enableHWA:t=!1}){this.#t=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!this.#t})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){$("Abstract method `_createCanvas` called.")}}class on extends ni{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}class ri{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(s=>({cMapData:s,isCompressed:this.isCompressed})).catch(s=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){$("Abstract method `_fetch` called.")}}class Ms extends ri{async _fetch(t){const e=await ge(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):pe(e)}}class ai{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,n){return"none"}destroy(t=!1){}}class ln extends ai{#t;#e;#s;#i;#r;#n;#o=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super(),this.#i=t,this.#r=e}get#a(){return this.#e||=new Map}get#d(){return this.#n||=new Map}get#l(){if(!this.#s){const t=this.#r.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const s=this.#r.createElementNS(Rt,"svg");s.setAttribute("width",0),s.setAttribute("height",0),this.#s=this.#r.createElementNS(Rt,"defs"),t.append(s),s.append(this.#s),this.#r.body.append(t)}return this.#s}#u(t){if(t.length===1){const o=t[0],l=new Array(256);for(let d=0;d<256;d++)l[d]=o[d]/255;const h=l.join(",");return[h,h,h]}const[e,s,i]=t,n=new Array(256),r=new Array(256),a=new Array(256);for(let o=0;o<256;o++)n[o]=e[o]/255,r[o]=s[o]/255,a[o]=i[o]/255;return[n.join(","),r.join(","),a.join(",")]}#c(t){if(this.#t===void 0){this.#t="";const e=this.#r.URL;e!==this.#r.baseURI&&(Oe(e)?F('#createUrl: ignore "data:"-URL for performance reasons.'):this.#t=Ks(e,""))}return`url(${this.#t}#${t})`}addFilter(t){if(!t)return"none";let e=this.#a.get(t);if(e)return e;const[s,i,n]=this.#u(t),r=t.length===1?s:`${s}${i}${n}`;if(e=this.#a.get(r),e)return this.#a.set(t,e),e;const a=`g_${this.#i}_transfer_map_${this.#o++}`,o=this.#c(a);this.#a.set(t,o),this.#a.set(r,o);const l=this.#g(a);return this.#m(s,i,n,l),o}addHCMFilter(t,e){const s=`${t}-${e}`,i="base";let n=this.#d.get(i);if(n?.key===s||(n?(n.filter?.remove(),n.key=s,n.url="none",n.filter=null):(n={key:s,url:"none",filter:null},this.#d.set(i,n)),!t||!e))return n.url;const r=this.#A(t);t=R.makeHexColor(...r);const a=this.#A(e);if(e=R.makeHexColor(...a),this.#l.style.color="",t==="#000000"&&e==="#ffffff"||t===e)return n.url;const o=new Array(256);for(let f=0;f<=255;f++){const g=f/255;o[f]=g<=.03928?g/12.92:((g+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#i}_hcm_filter`,d=n.filter=this.#g(h);this.#m(l,l,l,d),this.#p(d);const u=(f,g)=>{const p=r[f]/255,b=a[f]/255,m=new Array(g+1);for(let v=0;v<=g;v++)m[v]=p+v/g*(b-p);return m.join(",")};return this.#m(u(0,5),u(1,5),u(2,5),d),n.url=this.#c(h),n.url}addAlphaFilter(t){let e=this.#a.get(t);if(e)return e;const[s]=this.#u([t]),i=`alpha_${s}`;if(e=this.#a.get(i),e)return this.#a.set(t,e),e;const n=`g_${this.#i}_alpha_map_${this.#o++}`,r=this.#c(n);this.#a.set(t,r),this.#a.set(i,r);const a=this.#g(n);return this.#b(s,a),r}addLuminosityFilter(t){let e=this.#a.get(t||"luminosity");if(e)return e;let s,i;if(t?([s]=this.#u([t]),i=`luminosity_${s}`):i="luminosity",e=this.#a.get(i),e)return this.#a.set(t,e),e;const n=`g_${this.#i}_luminosity_map_${this.#o++}`,r=this.#c(n);this.#a.set(t,r),this.#a.set(i,r);const a=this.#g(n);return this.#f(a),t&&this.#b(s,a),r}addHighlightHCMFilter(t,e,s,i,n){const r=`${e}-${s}-${i}-${n}`;let a=this.#d.get(t);if(a?.key===r||(a?(a.filter?.remove(),a.key=r,a.url="none",a.filter=null):(a={key:r,url:"none",filter:null},this.#d.set(t,a)),!e||!s))return a.url;const[o,l]=[e,s].map(this.#A.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[u,f]=[i,n].map(this.#A.bind(this));d<h&&([h,d,u,f]=[d,h,f,u]),this.#l.style.color="";const g=(m,v,A)=>{const w=new Array(256),y=(d-h)/A,_=m/255,S=(v-m)/(255*A);let E=0;for(let C=0;C<=A;C++){const M=Math.round(h+C*y),I=_+C*S;for(let H=E;H<=M;H++)w[H]=I;E=M+1}for(let C=E;C<256;C++)w[C]=w[E-1];return w.join(",")},p=`g_${this.#i}_hcm_${t}_filter`,b=a.filter=this.#g(p);return this.#p(b),this.#m(g(u[0],f[0],5),g(u[1],f[1],5),g(u[2],f[2],5),b),a.url=this.#c(p),a.url}destroy(t=!1){t&&this.#n?.size||(this.#s?.parentNode.parentNode.remove(),this.#s=null,this.#e?.clear(),this.#e=null,this.#n?.clear(),this.#n=null,this.#o=0)}#f(t){const e=this.#r.createElementNS(Rt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#p(t){const e=this.#r.createElementNS(Rt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#g(t){const e=this.#r.createElementNS(Rt,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#l.append(e),e}#h(t,e,s){const i=this.#r.createElementNS(Rt,e);i.setAttribute("type","discrete"),i.setAttribute("tableValues",s),t.append(i)}#m(t,e,s,i){const n=this.#r.createElementNS(Rt,"feComponentTransfer");i.append(n),this.#h(n,"feFuncR",t),this.#h(n,"feFuncG",e),this.#h(n,"feFuncB",s)}#b(t,e){const s=this.#r.createElementNS(Rt,"feComponentTransfer");e.append(s),this.#h(s,"feFuncA",t)}#A(t){return this.#l.style.color=t,ds(getComputedStyle(this.#l).getPropertyValue("color"))}}class oi{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load font data at: ${e}`)})}async _fetch(t){$("Abstract method `_fetch` called.")}}class Ls extends oi{async _fetch(t){const e=await ge(t,"arraybuffer");return new Uint8Array(e)}}class li{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load wasm data at: ${e}`)})}async _fetch(t){$("Abstract method `_fetch` called.")}}class Ds extends li{async _fetch(t){const e=await ge(t,"arraybuffer");return new Uint8Array(e)}}ot&&F("Please use the `legacy` build in Node.js environments.");async function gs(c){const e=await process.getBuiltinModule("fs").promises.readFile(c);return new Uint8Array(e)}class hn extends ai{}class cn extends ni{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class dn extends ri{async _fetch(t){return gs(t)}}class un extends oi{async _fetch(t){return gs(t)}}class fn extends li{async _fetch(t){return gs(t)}}const st={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function is(c,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),c.clip(i)}class ms{isModifyingCurrentTransform(){return!1}getPattern(){$("Abstract method `getPattern` called.")}}class pn extends ms{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let n;if(i===st.STROKE||i===st.FILL){const r=e.current.getClippedPathBoundingBox(i,j(t))||[0,0,0,0],a=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,l=e.cachedCanvases.getCanvas("pattern",a,o),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height),h.beginPath(),h.rect(0,0,h.canvas.width,h.canvas.height),h.translate(-r[0],-r[1]),s=R.transform(s,[1,0,0,1,r[0],r[1]]),h.transform(...e.baseTransform),this.matrix&&h.transform(...this.matrix),is(h,this._bbox),h.fillStyle=this._createGradient(h),h.fill(),n=t.createPattern(l.canvas,"no-repeat");const d=new DOMMatrix(s);n.setTransform(d)}else is(t,this._bbox),n=this._createGradient(t);return n}}function Xe(c,t,e,s,i,n,r,a){const o=t.coords,l=t.colors,h=c.data,d=c.width*4;let u;o[e+1]>o[s+1]&&(u=e,e=s,s=u,u=n,n=r,r=u),o[s+1]>o[i+1]&&(u=s,s=i,i=u,u=r,r=a,a=u),o[e+1]>o[s+1]&&(u=e,e=s,s=u,u=n,n=r,r=u);const f=(o[e]+t.offsetX)*t.scaleX,g=(o[e+1]+t.offsetY)*t.scaleY,p=(o[s]+t.offsetX)*t.scaleX,b=(o[s+1]+t.offsetY)*t.scaleY,m=(o[i]+t.offsetX)*t.scaleX,v=(o[i+1]+t.offsetY)*t.scaleY;if(g>=v)return;const A=l[n],w=l[n+1],y=l[n+2],_=l[r],S=l[r+1],E=l[r+2],C=l[a],M=l[a+1],I=l[a+2],H=Math.round(g),G=Math.round(v);let B,rt,T,P,gt,Tt,Bt,Pt;for(let tt=H;tt<=G;tt++){if(tt<b){const V=tt<g?0:(g-tt)/(g-b);B=f-(f-p)*V,rt=A-(A-_)*V,T=w-(w-S)*V,P=y-(y-E)*V}else{let V;tt>v?V=1:b===v?V=0:V=(b-tt)/(b-v),B=p-(p-m)*V,rt=_-(_-C)*V,T=S-(S-M)*V,P=E-(E-I)*V}let W;tt<g?W=0:tt>v?W=1:W=(g-tt)/(g-v),gt=f-(f-m)*W,Tt=A-(A-C)*W,Bt=w-(w-M)*W,Pt=y-(y-I)*W;const qt=Math.round(Math.min(B,gt)),Mt=Math.round(Math.max(B,gt));let Lt=d*tt+qt*4;for(let V=qt;V<=Mt;V++)W=(B-V)/(B-gt),W<0?W=0:W>1&&(W=1),h[Lt++]=rt-(rt-Tt)*W|0,h[Lt++]=T-(T-Bt)*W|0,h[Lt++]=P-(P-Pt)*W|0,h[Lt++]=255}}function gn(c,t,e){const s=t.coords,i=t.colors;let n,r;switch(t.type){case"lattice":const a=t.verticesPerRow,o=Math.floor(s.length/a)-1,l=a-1;for(n=0;n<o;n++){let h=n*a;for(let d=0;d<l;d++,h++)Xe(c,e,s[h],s[h+1],s[h+a],i[h],i[h+1],i[h+a]),Xe(c,e,s[h+a+1],s[h+1],s[h+a],i[h+a+1],i[h+1],i[h+a])}break;case"triangles":for(n=0,r=s.length;n<r;n+=3)Xe(c,e,s[n],s[n+1],s[n+2],i[n],i[n+1],i[n+2]);break;default:throw new Error("illegal figure")}}class mn extends ms{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,s){const a=Math.floor(this._bounds[0]),o=Math.floor(this._bounds[1]),l=Math.ceil(this._bounds[2])-a,h=Math.ceil(this._bounds[3])-o,d=Math.min(Math.ceil(Math.abs(l*t[0]*1.1)),3e3),u=Math.min(Math.ceil(Math.abs(h*t[1]*1.1)),3e3),f=l/d,g=h/u,p={coords:this._coords,colors:this._colors,offsetX:-a,offsetY:-o,scaleX:1/f,scaleY:1/g},b=d+4,m=u+4,v=s.getCanvas("mesh",b,m),A=v.context,w=A.createImageData(d,u);if(e){const _=w.data;for(let S=0,E=_.length;S<E;S+=4)_[S]=e[0],_[S+1]=e[1],_[S+2]=e[2],_[S+3]=255}for(const _ of this._figures)gn(w,_,p);return A.putImageData(w,2,2),{canvas:v.canvas,offsetX:a-2*f,offsetY:o-2*g,scaleX:f,scaleY:g}}isModifyingCurrentTransform(){return!0}getPattern(t,e,s,i){is(t,this._bbox);const n=new Float32Array(2);if(i===st.SHADING)R.singularValueDecompose2dScale(j(t),n);else if(this.matrix){R.singularValueDecompose2dScale(this.matrix,n);const[a,o]=n;R.singularValueDecompose2dScale(e.baseTransform,n),n[0]*=a,n[1]*=o}else R.singularValueDecompose2dScale(e.baseTransform,n);const r=this._createMeshCanvas(n,i===st.SHADING?null:this._background,e.cachedCanvases);return i!==st.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(r.offsetX,r.offsetY),t.scale(r.scaleX,r.scaleY),t.createPattern(r.canvas,"no-repeat")}}class bn extends ms{getPattern(){return"hotpink"}}function An(c){switch(c[0]){case"RadialAxial":return new pn(c);case"Mesh":return new mn(c);case"Dummy":return new bn}throw new Error(`Unknown IR type: ${c[0]}`)}const Fs={COLORED:1,UNCOLORED:2};class bs{static MAX_PATTERN_SIZE=3e3;constructor(t,e,s,i){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=s,this.baseTransform=i}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:n,color:r,canvasGraphicsFactory:a}=this;let{xstep:o,ystep:l}=this;o=Math.abs(o),l=Math.abs(l),Ne("TilingType: "+n);const h=e[0],d=e[1],u=e[2],f=e[3],g=u-h,p=f-d,b=new Float32Array(2);R.singularValueDecompose2dScale(this.matrix,b);const[m,v]=b;R.singularValueDecompose2dScale(this.baseTransform,b);const A=m*b[0],w=v*b[1];let y=g,_=p,S=!1,E=!1;const C=Math.ceil(o*A),M=Math.ceil(l*w),I=Math.ceil(g*A),H=Math.ceil(p*w);C>=I?y=o:S=!0,M>=H?_=l:E=!0;const G=this.getSizeAndScale(y,this.ctx.canvas.width,A),B=this.getSizeAndScale(_,this.ctx.canvas.height,w),rt=t.cachedCanvases.getCanvas("pattern",G.size,B.size),T=rt.context,P=a.createCanvasGraphics(T);if(P.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(P,i,r),T.translate(-G.scale*h,-B.scale*d),P.transform(G.scale,0,0,B.scale,0,0),T.save(),this.clipBbox(P,h,d,u,f),P.baseTransform=j(P.ctx),P.executeOperatorList(s),P.endDrawing(),T.restore(),S||E){const gt=rt.canvas;S&&(y=o),E&&(_=l);const Tt=this.getSizeAndScale(y,this.ctx.canvas.width,A),Bt=this.getSizeAndScale(_,this.ctx.canvas.height,w),Pt=Tt.size,tt=Bt.size,W=t.cachedCanvases.getCanvas("pattern-workaround",Pt,tt),qt=W.context,Mt=S?Math.floor(g/o):0,Lt=E?Math.floor(p/l):0;for(let V=0;V<=Mt;V++)for(let se=0;se<=Lt;se++)qt.drawImage(gt,Pt*V,tt*se,Pt,tt,0,0,Pt,tt);return{canvas:W.canvas,scaleX:Tt.scale,scaleY:Bt.scale,offsetX:h,offsetY:d}}return{canvas:rt.canvas,scaleX:G.scale,scaleY:B.scale,offsetX:h,offsetY:d}}getSizeAndScale(t,e,s){const i=Math.max(bs.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*s);return n>=i?n=i:s=n/t,{scale:s,size:n}}clipBbox(t,e,s,i,n){const r=i-e,a=n-s;t.ctx.rect(e,s,r,a),R.axialAlignedBoundingBox([e,s,i,n],j(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,n=t.current;switch(e){case Fs.COLORED:const{fillStyle:r,strokeStyle:a}=this.ctx;i.fillStyle=n.fillColor=r,i.strokeStyle=n.strokeColor=a;break;case Fs.UNCOLORED:i.fillStyle=i.strokeStyle=s,n.fillColor=n.strokeColor=s;break;default:throw new Ni(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,s,i){let n=s;i!==st.SHADING&&(n=R.transform(n,e.baseTransform),this.matrix&&(n=R.transform(n,this.matrix)));const r=this.createPatternCanvas(e);let a=new DOMMatrix(n);a=a.translate(r.offsetX,r.offsetY),a=a.scale(1/r.scaleX,1/r.scaleY);const o=t.createPattern(r.canvas,"repeat");return o.setTransform(a),o}}function yn({src:c,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:n=4294967295,inverseDecode:r=!1}){const a=it.isLittleEndian?4278190080:255,[o,l]=r?[n,a]:[a,n],h=s>>3,d=s&7,u=c.length;e=new Uint32Array(e.buffer);let f=0;for(let g=0;g<i;g++){for(const b=t+h;t<b;t++){const m=t<u?c[t]:255;e[f++]=m&128?l:o,e[f++]=m&64?l:o,e[f++]=m&32?l:o,e[f++]=m&16?l:o,e[f++]=m&8?l:o,e[f++]=m&4?l:o,e[f++]=m&2?l:o,e[f++]=m&1?l:o}if(d===0)continue;const p=t<u?c[t++]:255;for(let b=0;b<d;b++)e[f++]=p&1<<7-b?l:o}return{srcPos:t,destPos:f}}const Ns=16,Os=100,wn=15,Bs=10,ut=16,Ye=new DOMMatrix,pt=new Float32Array(2),Qt=new Float32Array([1/0,1/0,-1/0,-1/0]);function vn(c,t){if(c._removeMirroring)throw new Error("Context is already forwarding operations.");c.__originalSave=c.save,c.__originalRestore=c.restore,c.__originalRotate=c.rotate,c.__originalScale=c.scale,c.__originalTranslate=c.translate,c.__originalTransform=c.transform,c.__originalSetTransform=c.setTransform,c.__originalResetTransform=c.resetTransform,c.__originalClip=c.clip,c.__originalMoveTo=c.moveTo,c.__originalLineTo=c.lineTo,c.__originalBezierCurveTo=c.bezierCurveTo,c.__originalRect=c.rect,c.__originalClosePath=c.closePath,c.__originalBeginPath=c.beginPath,c._removeMirroring=()=>{c.save=c.__originalSave,c.restore=c.__originalRestore,c.rotate=c.__originalRotate,c.scale=c.__originalScale,c.translate=c.__originalTranslate,c.transform=c.__originalTransform,c.setTransform=c.__originalSetTransform,c.resetTransform=c.__originalResetTransform,c.clip=c.__originalClip,c.moveTo=c.__originalMoveTo,c.lineTo=c.__originalLineTo,c.bezierCurveTo=c.__originalBezierCurveTo,c.rect=c.__originalRect,c.closePath=c.__originalClosePath,c.beginPath=c.__originalBeginPath,delete c._removeMirroring},c.save=function(){t.save(),this.__originalSave()},c.restore=function(){t.restore(),this.__originalRestore()},c.translate=function(e,s){t.translate(e,s),this.__originalTranslate(e,s)},c.scale=function(e,s){t.scale(e,s),this.__originalScale(e,s)},c.transform=function(e,s,i,n,r,a){t.transform(e,s,i,n,r,a),this.__originalTransform(e,s,i,n,r,a)},c.setTransform=function(e,s,i,n,r,a){t.setTransform(e,s,i,n,r,a),this.__originalSetTransform(e,s,i,n,r,a)},c.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},c.rotate=function(e){t.rotate(e),this.__originalRotate(e)},c.clip=function(e){t.clip(e),this.__originalClip(e)},c.moveTo=function(e,s){t.moveTo(e,s),this.__originalMoveTo(e,s)},c.lineTo=function(e,s){t.lineTo(e,s),this.__originalLineTo(e,s)},c.bezierCurveTo=function(e,s,i,n,r,a){t.bezierCurveTo(e,s,i,n,r,a),this.__originalBezierCurveTo(e,s,i,n,r,a)},c.rect=function(e,s,i,n){t.rect(e,s,i,n),this.__originalRect(e,s,i,n)},c.closePath=function(){t.closePath(),this.__originalClosePath()},c.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}class _n{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function Se(c,t,e,s,i,n,r,a,o,l){const[h,d,u,f,g,p]=j(c);if(d===0&&u===0){const v=r*h+g,A=Math.round(v),w=a*f+p,y=Math.round(w),_=(r+o)*h+g,S=Math.abs(Math.round(_)-A)||1,E=(a+l)*f+p,C=Math.abs(Math.round(E)-y)||1;return c.setTransform(Math.sign(h),0,0,Math.sign(f),A,y),c.drawImage(t,e,s,i,n,0,0,S,C),c.setTransform(h,d,u,f,g,p),[S,C]}if(h===0&&f===0){const v=a*u+g,A=Math.round(v),w=r*d+p,y=Math.round(w),_=(a+l)*u+g,S=Math.abs(Math.round(_)-A)||1,E=(r+o)*d+p,C=Math.abs(Math.round(E)-y)||1;return c.setTransform(0,Math.sign(d),Math.sign(u),0,A,y),c.drawImage(t,e,s,i,n,0,0,C,S),c.setTransform(h,d,u,f,g,p),[C,S]}c.drawImage(t,e,s,i,n,r,a,o,l);const b=Math.hypot(h,d),m=Math.hypot(u,f);return[b*o,m*l]}class Hs{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=Je;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=nt.FILL;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]),this.minMax=Qt.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=st.FILL,e=null){const s=this.minMax.slice();if(t===st.STROKE){e||$("Stroke bounding box must include transform."),R.singularValueDecompose2dScale(e,pt);const i=pt[0]*this.lineWidth/2,n=pt[1]*this.lineWidth/2;s[0]-=i,s[1]-=n,s[2]+=i,s[3]+=n}return s}updateClipFromPath(){const t=R.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(Qt,0)}getClippedPathBoundingBox(t=st.FILL,e=null){return R.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function $s(c,t){if(t instanceof ImageData){c.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%ut,n=(e-i)/ut,r=i===0?n:n+1,a=c.createImageData(s,ut);let o=0,l;const h=t.data,d=a.data;let u,f,g,p;if(t.kind===Te.GRAYSCALE_1BPP){const b=h.byteLength,m=new Uint32Array(d.buffer,0,d.byteLength>>2),v=m.length,A=s+7>>3,w=4294967295,y=it.isLittleEndian?4278190080:255;for(u=0;u<r;u++){for(g=u<n?ut:i,l=0,f=0;f<g;f++){const _=b-o;let S=0;const E=_>A?s:_*8-7,C=E&-8;let M=0,I=0;for(;S<C;S+=8)I=h[o++],m[l++]=I&128?w:y,m[l++]=I&64?w:y,m[l++]=I&32?w:y,m[l++]=I&16?w:y,m[l++]=I&8?w:y,m[l++]=I&4?w:y,m[l++]=I&2?w:y,m[l++]=I&1?w:y;for(;S<E;S++)M===0&&(I=h[o++],M=128),m[l++]=I&M?w:y,M>>=1}for(;l<v;)m[l++]=0;c.putImageData(a,0,u*ut)}}else if(t.kind===Te.RGBA_32BPP){for(f=0,p=s*ut*4,u=0;u<n;u++)d.set(h.subarray(o,o+p)),o+=p,c.putImageData(a,0,f),f+=ut;u<r&&(p=s*i*4,d.set(h.subarray(o,o+p)),c.putImageData(a,0,f))}else if(t.kind===Te.RGB_24BPP)for(g=ut,p=s*g,u=0;u<r;u++){for(u>=n&&(g=i,p=s*g),l=0,f=p;f--;)d[l++]=h[o++],d[l++]=h[o++],d[l++]=h[o++],d[l++]=255;c.putImageData(a,0,u*ut)}else throw new Error(`bad image kind: ${t.kind}`)}function Gs(c,t){if(t.bitmap){c.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%ut,n=(e-i)/ut,r=i===0?n:n+1,a=c.createImageData(s,ut);let o=0;const l=t.data,h=a.data;for(let d=0;d<r;d++){const u=d<n?ut:i;({srcPos:o}=yn({src:l,srcPos:o,dest:h,width:s,height:u,nonBlackColor:0})),c.putImageData(a,0,d*ut)}}function ie(c,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)c[s]!==void 0&&(t[s]=c[s]);c.setLineDash!==void 0&&(t.setLineDash(c.getLineDash()),t.lineDashOffset=c.lineDashOffset)}function Ee(c){c.strokeStyle=c.fillStyle="#000000",c.fillRule="nonzero",c.globalAlpha=1,c.lineWidth=1,c.lineCap="butt",c.lineJoin="miter",c.miterLimit=10,c.globalCompositeOperation="source-over",c.font="10px sans-serif",c.setLineDash!==void 0&&(c.setLineDash([]),c.lineDashOffset=0);const{filter:t}=c;t!=="none"&&t!==""&&(c.filter="none")}function zs(c,t){if(t)return!0;R.singularValueDecompose2dScale(c,pt);const e=Math.fround(xt.pixelRatio*ee.PDF_TO_CSS_UNITS);return pt[0]<=e&&pt[1]<=e}const Sn=["butt","round","square"],En=["miter","round","bevel"],Cn={},Us={};class Zt{constructor(t,e,s,i,n,{optionalContentConfig:r,markedContentStack:a=null},o,l){this.ctx=t,this.current=new Hs(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=n,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=a||[],this.optionalContentConfig=r,this.cachedCanvases=new _n(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const n=this.ctx.canvas.width,r=this.ctx.canvas.height,a=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,n,r),this.ctx.fillStyle=a,s){const o=this.cachedCanvases.getCanvas("transparent",n,r);this.compositeCtx=this.ctx,this.transparentCanvas=o.canvas,this.ctx=o.context,this.ctx.save(),this.ctx.transform(...j(this.compositeCtx))}this.ctx.save(),Ee(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=j(this.ctx)}executeOperatorList(t,e,s,i){const n=t.argsArray,r=t.fnArray;let a=e||0;const o=n.length;if(o===a)return a;const l=o-a>Bs&&typeof s=="function",h=l?Date.now()+wn:0;let d=0;const u=this.commonObjs,f=this.objs;let g;for(;;){if(i!==void 0&&a===i.nextBreakPoint)return i.breakIt(a,s),a;if(g=r[a],g!==Ie.dependency)this[g].apply(this,n[a]);else for(const p of n[a]){const b=p.startsWith("g_")?u:f;if(!b.has(p))return b.get(p,s),a}if(a++,a===o)return a;if(l&&++d>Bs){if(Date.now()>h)return s(),a;d=0}}}#t(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){this.#t(),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement<"u"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#e()}#e(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){const s=t.width??t.displayWidth,i=t.height??t.displayHeight;let n=Math.max(Math.hypot(e[0],e[1]),1),r=Math.max(Math.hypot(e[2],e[3]),1),a=s,o=i,l="prescale1",h,d;for(;n>2&&a>1||r>2&&o>1;){let u=a,f=o;n>2&&a>1&&(u=a>=16384?Math.floor(a/2)-1||1:Math.ceil(a/2),n/=a/u),r>2&&o>1&&(f=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o)/2,r/=o/f),h=this.cachedCanvases.getCanvas(l,u,f),d=h.context,d.clearRect(0,0,u,f),d.drawImage(t,0,0,a,o,0,0,u,f),t=h.canvas,a=u,o=f,l=l==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:a,paintHeight:o}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,n=this.current.fillColor,r=this.current.patternFill,a=j(e);let o,l,h,d;if((t.bitmap||t.data)&&t.count>1){const C=t.bitmap||t.data.buffer;l=JSON.stringify(r?a:[a.slice(0,4),n]),o=this._cachedBitmapsMap.get(C),o||(o=new Map,this._cachedBitmapsMap.set(C,o));const M=o.get(l);if(M&&!r){const I=Math.round(Math.min(a[0],a[2])+a[4]),H=Math.round(Math.min(a[1],a[3])+a[5]);return{canvas:M,offsetX:I,offsetY:H}}h=M}h||(d=this.cachedCanvases.getCanvas("maskCanvas",s,i),Gs(d.context,t));let u=R.transform(a,[1/s,0,0,-1/i,0,0]);u=R.transform(u,[1,0,0,1,0,-i]);const f=Qt.slice();R.axialAlignedBoundingBox([0,0,s,i],u,f);const[g,p,b,m]=f,v=Math.round(b-g)||1,A=Math.round(m-p)||1,w=this.cachedCanvases.getCanvas("fillCanvas",v,A),y=w.context,_=g,S=p;y.translate(-_,-S),y.transform(...u),h||(h=this._scaleImage(d.canvas,_t(y)),h=h.img,o&&r&&o.set(l,h)),y.imageSmoothingEnabled=zs(j(y),t.interpolate),Se(y,h,0,0,h.width,h.height,0,0,s,i),y.globalCompositeOperation="source-in";const E=R.transform(_t(y),[1,0,0,1,-_,-S]);return y.fillStyle=r?n.getPattern(e,this,E,st.FILL):n,y.fillRect(0,0,s,i),o&&!r&&(this.cachedCanvases.delete("fillCanvas"),o.set(l,w.canvas)),{canvas:w.canvas,offsetX:Math.round(_),offsetY:Math.round(S)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=Sn[t]}setLineJoin(t){this.ctx.lineJoin=En[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=i.context;n.setTransform(this.suspendedCtx.getTransform()),ie(this.suspendedCtx,n),vn(n,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),ie(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const n=i[0],r=i[1],a=i[2]-n,o=i[3]-r;a===0||o===0||(this.genericComposeSMask(e.context,s,a,o,e.subtype,e.backdrop,e.transferMap,n,r,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,n,r,a,o,l,h,d){let u=t.canvas,f=o-h,g=l-d;if(r)if(f<0||g<0||f+s>u.width||g+i>u.height){const b=this.cachedCanvases.getCanvas("maskExtension",s,i),m=b.context;m.drawImage(u,-f,-g),m.globalCompositeOperation="destination-atop",m.fillStyle=r,m.fillRect(0,0,s,i),m.globalCompositeOperation="source-over",u=b.canvas,f=g=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const b=new Path2D;b.rect(f,g,s,i),t.clip(b),t.globalCompositeOperation="destination-atop",t.fillStyle=r,t.fillRect(f,g,s,i),t.restore()}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),n==="Alpha"&&a?e.filter=this.filterFactory.addAlphaFilter(a):n==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(a));const p=new Path2D;p.rect(o,l,s,i),e.clip(p),e.globalCompositeOperation="destination-in",e.drawImage(u,f,g,s,i,o,l,s,i),e.restore()}save(){this.inSMaskMode&&ie(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){if(this.stateStack.length===0){this.inSMaskMode&&this.endSMaskMode();return}this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&ie(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}transform(t,e,s,i,n,r){this.ctx.transform(t,e,s,i,n,r),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){let[i]=e;if(!s){i||=e[0]=new Path2D,this[t](i);return}if(!(i instanceof Path2D)){const n=e[0]=new Path2D;for(let r=0,a=i.length;r<a;)switch(i[r++]){case we.moveTo:n.moveTo(i[r++],i[r++]);break;case we.lineTo:n.lineTo(i[r++],i[r++]);break;case we.curveTo:n.bezierCurveTo(i[r++],i[r++],i[r++],i[r++],i[r++],i[r++]);break;case we.closePath:n.closePath();break;default:F(`Unrecognized drawing path operator: ${i[r-1]}`);break}i=n}R.axialAlignedBoundingBox(s,j(this.ctx),this.current.minMax),this[t](i)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const s=this.ctx,i=this.current.strokeColor;if(s.globalAlpha=this.current.strokeAlpha,this.contentVisible)if(typeof i=="object"&&i?.getPattern){const n=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.strokeStyle=i.getPattern(s,this,_t(s),st.STROKE),n){const r=new Path2D;r.addPath(t,s.getTransform().invertSelf().multiplySelf(n)),t=r}this.rescaleAndStroke(t,!1),s.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(st.STROKE,j(this.ctx))),s.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const s=this.ctx,i=this.current.fillColor,n=this.current.patternFill;let r=!1;if(n){const o=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.fillStyle=i.getPattern(s,this,_t(s),st.FILL),o){const l=new Path2D;l.addPath(t,s.getTransform().invertSelf().multiplySelf(o)),t=l}r=!0}const a=this.current.getClippedPathBoundingBox();this.contentVisible&&a!==null&&(this.pendingEOFill?(s.fill(t,"evenodd"),this.pendingEOFill=!1):s.fill(t)),r&&s.restore(),e&&this.consumePath(t,a)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=Cn}eoClip(){this.pendingClip=Us}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0)return;const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:n,x:r,y:a,fontSize:o,path:l}of t)s.addPath(l,new DOMMatrix(n).preMultiplySelf(i).translate(r,a).scale(o,-o));e.clip(s),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||Je,(i.fontMatrix[0]===0||i.fontMatrix[3]===0)&&F("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const n=s.loadedName||"sans-serif",r=s.systemFontInfo?.css||`"${n}", ${s.fallbackName}`;let a="normal";s.black?a="900":s.bold&&(a="bold");const o=s.italic?"italic":"normal";let l=e;e<Ns?l=Ns:e>Os&&(l=Os),this.current.fontSizeScale=e/l,this.ctx.font=`${o} ${a} ${l}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#s(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i}paintChar(t,e,s,i,n){const r=this.ctx,a=this.current,o=a.font,l=a.textRenderingMode,h=a.fontSize/a.fontSizeScale,d=l&nt.FILL_STROKE_MASK,u=!!(l&nt.ADD_TO_PATH_FLAG),f=a.patternFill&&!o.missingFile,g=a.patternStroke&&!o.missingFile;let p;if((o.disableFontFace||u||f||g)&&(p=o.getPathGenerator(this.commonObjs,t)),o.disableFontFace||f||g){r.save(),r.translate(e,s),r.scale(h,-h);let b;if((d===nt.FILL||d===nt.FILL_STROKE)&&(i?(b=r.getTransform(),r.setTransform(...i),r.fill(this.#s(p,b,i))):r.fill(p)),d===nt.STROKE||d===nt.FILL_STROKE)if(n){b||=r.getTransform(),r.setTransform(...n);const{a:m,b:v,c:A,d:w}=b,y=R.inverseTransform(n),_=R.transform([m,v,A,w,0,0],y);R.singularValueDecompose2dScale(_,pt),r.lineWidth*=Math.max(pt[0],pt[1])/h,r.stroke(this.#s(p,b,n))}else r.lineWidth/=h,r.stroke(p);r.restore()}else(d===nt.FILL||d===nt.FILL_STROKE)&&r.fillText(t,e,s),(d===nt.STROKE||d===nt.FILL_STROKE)&&r.strokeText(t,e,s);u&&(this.pendingTextPaths||=[]).push({transform:j(r),x:e,y:s,fontSize:h,path:p})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return N(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const n=this.ctx,r=e.fontSizeScale,a=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,d=t.length,u=s.vertical,f=u?1:-1,g=s.defaultVMetrics,p=i*e.fontMatrix[0],b=e.textRenderingMode===nt.FILL&&!s.disableFontFace&&!e.patternFill;n.save(),e.textMatrix&&n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),l>0?n.scale(h,-1):n.scale(h,1);let m,v;if(e.patternFill){n.save();const S=e.fillColor.getPattern(n,this,_t(n),st.FILL);m=j(n),n.restore(),n.fillStyle=S}if(e.patternStroke){n.save();const S=e.strokeColor.getPattern(n,this,_t(n),st.STROKE);v=j(n),n.restore(),n.strokeStyle=S}let A=e.lineWidth;const w=e.textMatrixScale;if(w===0||A===0){const S=e.textRenderingMode&nt.FILL_STROKE_MASK;(S===nt.STROKE||S===nt.FILL_STROKE)&&(A=this.getSinglePixelWidth())}else A/=w;if(r!==1&&(n.scale(r,r),A/=r),n.lineWidth=A,s.isInvalidPDFjsFont){const S=[];let E=0;for(const C of t)S.push(C.unicode),E+=C.width;n.fillText(S.join(""),0,0),e.x+=E*p*h,n.restore(),this.compose();return}let y=0,_;for(_=0;_<d;++_){const S=t[_];if(typeof S=="number"){y+=f*S*i/1e3;continue}let E=!1;const C=(S.isSpace?o:0)+a,M=S.fontChar,I=S.accent;let H,G,B=S.width;if(u){const T=S.vmetric||g,P=-(S.vmetric?T[1]:B*.5)*p,gt=T[2]*p;B=T?-T[0]:B,H=P/r,G=(y+gt)/r}else H=y/r,G=0;if(s.remeasure&&B>0){const T=n.measureText(M).width*1e3/i*r;if(B<T&&this.isFontSubpixelAAEnabled){const P=B/T;E=!0,n.save(),n.scale(P,1),H/=P}else B!==T&&(H+=(B-T)/2e3*i/r)}if(this.contentVisible&&(S.isInFont||s.missingFile)){if(b&&!I)n.fillText(M,H,G);else if(this.paintChar(M,H,G,m,v),I){const T=H+i*I.offset.x/r,P=G-i*I.offset.y/r;this.paintChar(I.fontChar,T,P,m,v)}}const rt=u?B*p-C*l:B*p+C*l;y+=rt,E&&n.restore()}u?e.y-=y:e.x+=y*h,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,n=s.fontSize,r=s.fontDirection,a=i.vertical?1:-1,o=s.charSpacing,l=s.wordSpacing,h=s.textHScale*r,d=s.fontMatrix||Je,u=t.length,f=s.textRenderingMode===nt.INVISIBLE;let g,p,b,m;if(!(f||n===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),s.textMatrix&&e.transform(...s.textMatrix),e.translate(s.x,s.y+s.textRise),e.scale(h,r),g=0;g<u;++g){if(p=t[g],typeof p=="number"){m=a*p*n/1e3,this.ctx.translate(m,0),s.x+=m*h;continue}const v=(p.isSpace?l:0)+o,A=i.charProcOperatorList[p.operatorListId];A?this.contentVisible&&(this.save(),e.scale(n,n),e.transform(...d),this.executeOperatorList(A),this.restore()):F(`Type3 character "${p.operatorListId}" is not available.`);const w=[p.width,0];R.applyTransform(w,d),b=w[0]*n+v,e.translate(b,0),s.x+=b*h}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,n,r){const a=new Path2D;a.rect(s,i,n-s,r-i),this.ctx.clip(a),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=this.baseTransform||j(this.ctx),i={createCanvasGraphics:n=>new Zt(n,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new bs(t,this.ctx,i,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t){this.ctx.strokeStyle=this.current.strokeColor=t,this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t){this.ctx.fillStyle=this.current.fillColor=t,this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=An(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,_t(e),st.SHADING);const i=_t(e);if(i){const{width:n,height:r}=e.canvas,a=Qt.slice();R.axialAlignedBoundingBox([0,0,n,r],i,a);const[o,l,h,d]=a;this.ctx.fillRect(o,l,h-o,d-l)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){$("Should not call beginInlineImage")}beginImageData(){$("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=j(this.ctx),e)){R.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[s,i,n,r]=e,a=new Path2D;a.rect(s,i,n-s,r-i),this.ctx.clip(a),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||Ne("TODO: Support non-isolated groups."),t.knockout&&F("Knockout groups not supported.");const s=j(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=Qt.slice();R.axialAlignedBoundingBox(t.bbox,j(e),i);const n=[0,0,e.canvas.width,e.canvas.height];i=R.intersect(i,n)||[0,0,0,0];const r=Math.floor(i[0]),a=Math.floor(i[1]),o=Math.max(Math.ceil(i[2])-r,1),l=Math.max(Math.ceil(i[3])-a,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(h,o,l),u=d.context;u.translate(-r,-a),u.transform(...s);let f=new Path2D;const[g,p,b,m]=t.bbox;if(f.rect(g,p,b-g,m-p),t.matrix){const v=new Path2D;v.addPath(f,new DOMMatrix(t.matrix)),f=v}u.clip(f),t.smask?this.smaskStack.push({canvas:d.canvas,context:u,offsetX:r,offsetY:a,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(r,a),e.save()),ie(e,u),this.ctx=u,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=j(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const n=Qt.slice();R.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i,n),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(n)}}beginAnnotation(t,e,s,i,n){if(this.#t(),Ee(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const r=e[2]-e[0],a=e[3]-e[1];if(n&&this.annotationCanvasMap){s=s.slice(),s[4]-=e[0],s[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=r,e[3]=a,R.singularValueDecompose2dScale(j(this.ctx),pt);const{viewportScale:o}=this,l=Math.ceil(r*this.outputScaleX*o),h=Math.ceil(a*this.outputScaleY*o);this.annotationCanvas=this.canvasFactory.create(l,h);const{canvas:d,context:u}=this.annotationCanvas;this.annotationCanvasMap.set(t,d),this.annotationCanvas.savedCtx=this.ctx,this.ctx=u,this.ctx.save(),this.ctx.setTransform(pt[0],0,0,-pt[1],0,a*pt[1]),Ee(this.ctx)}else{Ee(this.ctx),this.endPath();const o=new Path2D;o.rect(e[0],e[1],r,a),this.ctx.clip(o)}}this.current=new Hs(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#e(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const s=this.ctx,i=this._createMaskCanvas(t),n=i.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(n,i.offsetX,i.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,n,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);const a=this.ctx;a.save();const o=j(a);a.transform(e,s,i,n,0,0);const l=this._createMaskCanvas(t);a.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let h=0,d=r.length;h<d;h+=2){const u=R.transform(o,[e,s,i,n,r[h],r[h+1]]);a.drawImage(l.canvas,u[4],u[5])}a.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const n of t){const{data:r,width:a,height:o,transform:l}=n,h=this.cachedCanvases.getCanvas("maskCanvas",a,o),d=h.context;d.save();const u=this.getObject(r,n);Gs(d,u),d.globalCompositeOperation="source-in",d.fillStyle=i?s.getPattern(d,this,_t(e),st.FILL):s,d.fillRect(0,0,a,o),d.restore(),e.save(),e.transform(...l),e.scale(1,-1),Se(e,h.canvas,0,0,a,o,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);if(!e){F("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){F("Dependent image isn't ready yet");return}const r=n.width,a=n.height,o=[];for(let l=0,h=i.length;l<h;l+=2)o.push({transform:[e,0,0,s,i[l],i[l+1]],x:0,y:0,w:r,h:a});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,n=this.cachedCanvases.getCanvas("inlineImage",s,i),r=n.context;return r.filter=this.current.transferMaps,r.drawImage(e,0,0),r.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;this.save();const{filter:n}=i;n!=="none"&&n!==""&&(i.filter="none"),i.scale(1/e,-1/s);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)r=t;else{const l=this.cachedCanvases.getCanvas("inlineImage",e,s).context;$s(l,t),r=this.applyTransferMapsToCanvas(l)}const a=this._scaleImage(r,_t(i));i.imageSmoothingEnabled=zs(j(i),t.interpolate),Se(i,a.img,0,0,a.paintWidth,a.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const n=t.width,r=t.height,o=this.cachedCanvases.getCanvas("inlineImage",n,r).context;$s(o,t),i=this.applyTransferMapsToCanvas(o)}for(const n of e)s.save(),s.transform(...n.transform),s.scale(1,-1),Se(s,i,n.x,n.y,n.w,n.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const s=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const i=this.ctx;this.pendingClip&&(s||(this.pendingClip===Us?i.clip(t,"evenodd"):i.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=j(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:n}=this.ctx.getTransform();let r,a;if(s===0&&i===0){const o=Math.abs(e),l=Math.abs(n);if(o===l)if(t===0)r=a=1/o;else{const h=o*t;r=a=h<1?1/h:1}else if(t===0)r=1/o,a=1/l;else{const h=o*t,d=l*t;r=h<1?1/h:1,a=d<1?1/d:1}}else{const o=Math.abs(e*n-s*i),l=Math.hypot(e,s),h=Math.hypot(i,n);if(t===0)r=h/o,a=l/o;else{const d=t*o;r=h>d?h/d:1,a=l>d?l/d:1}}this._cachedScaleForStroking[0]=r,this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:s,current:{lineWidth:i}}=this,[n,r]=this.getScaleForStroking();if(n===r){s.lineWidth=(i||1)*n,s.stroke(t);return}const a=s.getLineDash();e&&s.save(),s.scale(n,r),Ye.a=1/n,Ye.d=1/r;const o=new Path2D;if(o.addPath(t,Ye),a.length>0){const l=Math.max(n,r);s.setLineDash(a.map(h=>h/l)),s.lineDashOffset/=l}s.lineWidth=i||1,s.stroke(o),e&&s.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const c in Ie)Zt.prototype[c]!==void 0&&(Zt.prototype[Ie[c]]=Zt.prototype[c]);class de{static#t=null;static#e="";static get workerPort(){return this.#t}static set workerPort(t){if(!(typeof Worker<"u"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");this.#t=t}static get workerSrc(){return this.#e}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");this.#e=t}}class xn{#t;#e;constructor({parsedData:t,rawData:e}){this.#t=t,this.#e=e}getRaw(){return this.#e}get(t){return this.#t.get(t)??null}[Symbol.iterator](){return this.#t.entries()}}const Yt=Symbol("INTERNAL");class Tn{#t=!1;#e=!1;#s=!1;#i=!0;constructor(t,{name:e,intent:s,usage:i,rbGroups:n}){this.#t=!!(t&ft.DISPLAY),this.#e=!!(t&ft.PRINT),this.name=e,this.intent=s,this.usage=i,this.rbGroups=n}get visible(){if(this.#s)return this.#i;if(!this.#i)return!1;const{print:t,view:e}=this.usage;return this.#t?e?.viewState!=="OFF":this.#e?t?.printState!=="OFF":!0}_setVisible(t,e,s=!1){t!==Yt&&$("Internal method `_setVisible` called."),this.#s=s,this.#i=e}}class Pn{#t=null;#e=new Map;#s=null;#i=null;constructor(t,e=ft.DISPLAY){if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,this.#i=t.order;for(const s of t.groups)this.#e.set(s.id,new Tn(e,s));if(t.baseState==="OFF")for(const s of this.#e.values())s._setVisible(Yt,!1);for(const s of t.on)this.#e.get(s)._setVisible(Yt,!0);for(const s of t.off)this.#e.get(s)._setVisible(Yt,!1);this.#s=this.getHash()}}#r(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const n=t[i];let r;if(Array.isArray(n))r=this.#r(n);else if(this.#e.has(n))r=this.#e.get(n).visible;else return F(`Optional content group not found: ${n}`),!0;switch(s){case"And":if(!r)return!1;break;case"Or":if(r)return!0;break;case"Not":return!r;default:return!0}}return s==="And"}isVisible(t){if(this.#e.size===0)return!0;if(!t)return Ne("Optional content group not defined."),!0;if(t.type==="OCG")return this.#e.has(t.id)?this.#e.get(t.id).visible:(F(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return this.#r(t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!this.#e.has(e))return F(`Optional content group not found: ${e}`),!0;if(this.#e.get(e).visible)return!0}return!1}else if(t.policy==="AllOn"){for(const e of t.ids){if(!this.#e.has(e))return F(`Optional content group not found: ${e}`),!0;if(!this.#e.get(e).visible)return!1}return!0}else if(t.policy==="AnyOff"){for(const e of t.ids){if(!this.#e.has(e))return F(`Optional content group not found: ${e}`),!0;if(!this.#e.get(e).visible)return!0}return!1}else if(t.policy==="AllOff"){for(const e of t.ids){if(!this.#e.has(e))return F(`Optional content group not found: ${e}`),!0;if(this.#e.get(e).visible)return!1}return!0}return F(`Unknown optional content policy ${t.policy}.`),!0}return F(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){const i=this.#e.get(t);if(!i){F(`Optional content group not found: ${t}`);return}if(s&&e&&i.rbGroups.length)for(const n of i.rbGroups)for(const r of n)r!==t&&this.#e.get(r)?._setVisible(Yt,!1,!0);i._setVisible(Yt,!!e,!0),this.#t=null}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const n=this.#e.get(i);if(n)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!n.visible,e);break}}this.#t=null}get hasInitialVisibility(){return this.#s===null||this.getHash()===this.#s}getOrder(){return this.#e.size?this.#i?this.#i.slice():[...this.#e.keys()]:null}getGroup(t){return this.#e.get(t)||null}getHash(){if(this.#t!==null)return this.#t;const t=new si;for(const[e,s]of this.#e)t.update(`${e}:${s.visible}`);return this.#t=t.hexdigest()}[Symbol.iterator](){return this.#e.entries()}}class Rn{constructor(t,{disableRange:e=!1,disableStream:s=!1}){J(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:n,progressiveDone:r,contentDispositionFilename:a}=t;if(this._queuedChunks=[],this._progressiveDone=r,this._contentDispositionFilename=a,n?.length>0){const o=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(o)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((o,l)=>{this._onReceiveData({begin:o,chunk:l})}),t.addProgressListener((o,l)=>{this._onProgress({loaded:o,total:l})}),t.addProgressiveReadListener(o=>{this._onReceiveData({chunk:o})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(t===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const i=this._rangeReaders.some(function(n){return n._begin!==t?!1:(n._enqueue(s),!0)});J(i,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){t.total===void 0?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){J(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new In(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new kn(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class In{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=cs(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const n of this._queuedChunks)this._loaded+=n.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class kn{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function Mn(c){let t=!0,e=s("filename\\*","i").exec(c);if(e){e=e[1];let h=a(e);return h=unescape(h),h=o(h),h=l(h),n(h)}if(e=r(c),e){const h=l(e);return n(h)}if(e=s("filename","i").exec(c),e){e=e[1];let h=a(e);return h=l(h),n(h)}function s(h,d){return new RegExp("(?:^|;)\\s*"+h+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',d)}function i(h,d){if(h){if(!/^[\x00-\xFF]+$/.test(d))return d;try{const u=new TextDecoder(h,{fatal:!0}),f=pe(d);d=u.decode(f),t=!1}catch{}}return d}function n(h){return t&&/[\x80-\xff]/.test(h)&&(h=i("utf-8",h),t&&(h=i("iso-8859-1",h))),h}function r(h){const d=[];let u;const f=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(u=f.exec(h))!==null;){let[,p,b,m]=u;if(p=parseInt(p,10),p in d){if(p===0)break;continue}d[p]=[b,m]}const g=[];for(let p=0;p<d.length&&p in d;++p){let[b,m]=d[p];m=a(m),b&&(m=unescape(m),p===0&&(m=o(m))),g.push(m)}return g.join("")}function a(h){if(h.startsWith('"')){const d=h.slice(1).split('\\"');for(let u=0;u<d.length;++u){const f=d[u].indexOf('"');f!==-1&&(d[u]=d[u].slice(0,f),d.length=u+1),d[u]=d[u].replaceAll(/\\(.)/g,"$1")}h=d.join('"')}return h}function o(h){const d=h.indexOf("'");if(d===-1)return h;const u=h.slice(0,d),g=h.slice(d+1).replace(/^[^']*'/,"");return i(u,g)}function l(h){return!h.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(h)?h:h.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(d,u,f,g){if(f==="q"||f==="Q")return g=g.replaceAll("_"," "),g=g.replaceAll(/=([0-9a-fA-F]{2})/g,function(p,b){return String.fromCharCode(parseInt(b,16))}),i(u,g);try{g=atob(g)}catch{}return i(u,g)})}return""}function hi(c,t){const e=new Headers;if(!c||!t||typeof t!="object")return e;for(const s in t){const i=t[s];i!==void 0&&e.append(s,i)}return e}function He(c){return URL.parse(c)?.origin??null}function ci({responseHeaders:c,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},n=parseInt(c.get("Content-Length"),10);return!Number.isInteger(n)||(i.suggestedLength=n,n<=2*e)||s||!t||c.get("Accept-Ranges")!=="bytes"||(c.get("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function di(c){const t=c.get("Content-Disposition");if(t){let e=Mn(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch{}if(cs(e))return e}return null}function Ae(c,t){return new ke(`Unexpected server response (${c}) while retrieving PDF "${t}".`,c,c===404||c===0&&t.startsWith("file:"))}function ui(c){return c===200||c===206}function fi(c,t,e){return{method:"GET",headers:c,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function pi(c){return c instanceof Uint8Array?c.buffer:c instanceof ArrayBuffer?c:(F(`getArrayBuffer - unexpected data format: ${c}`),new Uint8Array(c).buffer)}class Ln{_responseOrigin=null;constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=hi(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return J(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new Dn(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Fn(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Dn{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,fi(s,this._withCredentials,this._abortController)).then(n=>{if(t._responseOrigin=He(n.url),!ui(n.status))throw Ae(n.status,i);this._reader=n.body.getReader(),this._headersCapability.resolve();const r=n.headers,{allowRangeRequests:a,suggestedLength:o}=ci({responseHeaders:r,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=a,this._contentLength=o||this._contentLength,this._filename=di(r),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new Nt("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:pi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class Fn{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${s-1}`);const r=i.url;fetch(r,fi(n,this._withCredentials,this._abortController)).then(a=>{const o=He(a.url);if(o!==t._responseOrigin)throw new Error(`Expected range response-origin "${o}" to match "${t._responseOrigin}".`);if(!ui(a.status))throw Ae(a.status,r);this._readCapability.resolve(),this._reader=a.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:pi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}const Ke=200,Qe=206;function Nn(c){const t=c.response;return typeof t!="string"?t:pe(t).buffer}class On{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:s}){this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=hi(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[n,r]of this.headers)e.setRequestHeader(n,r);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=Qe):i.expectedStatus=Ke,e.responseType="arraybuffer",J(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){const s=this.pendingRequests[t];s&&s.onProgress?.(e)}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){s.onError(i.status);return}const n=i.status||Ke;if(!(n===Ke&&s.expectedStatus===Qe)&&n!==s.expectedStatus){s.onError(i.status);return}const a=Nn(i);if(n===Qe){const o=i.getResponseHeader("Content-Range"),l=/bytes (\d+)-(\d+)\/(\d+)/.exec(o);l?s.onDone({begin:parseInt(l[1],10),chunk:a}):(F('Missing or invalid "Content-Range" header.'),s.onError(0))}else a?s.onDone({begin:0,chunk:a}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Bn{constructor(t){this._source=t,this._manager=new On(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return J(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Hn(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new $n(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Hn{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=He(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(a=>{const[o,...l]=a.split(": ");return[o,l.join(": ")]}):[]),{allowRangeRequests:n,suggestedLength:r}=ci({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=r||this._contentLength,this._filename=di(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=Ae(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class $n{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){const t=He(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??=Ae(t,this._url);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const Gn=/^[a-z][a-z0-9\-+.]+:/i;function zn(c){if(Gn.test(c))return new URL(c);const t=process.getBuiltinModule("url");return new URL(t.pathToFileURL(c))}class Un{constructor(t){this.source=t,this.url=zn(t.url),J(this.url.protocol==="file:","PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return J(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new jn(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Vn(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class jn{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=Ae(0,this._url.href)),this._storedError=i,this._headersCapability.reject(i)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new Nt("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class Vn{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const ne=Symbol("INITIAL_DATA");class gi{#t=Object.create(null);#e(t){return this.#t[t]||={...Promise.withResolvers(),data:ne}}get(t,e=null){if(e){const i=this.#e(t);return i.promise.then(()=>e(i.data)),null}const s=this.#t[t];if(!s||s.data===ne)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=this.#t[t];return!!e&&e.data!==ne}delete(t){const e=this.#t[t];return!e||e.data===ne?!1:(delete this.#t[t],!0)}resolve(t,e=null){const s=this.#e(t);s.data=e,s.resolve()}clear(){for(const t in this.#t){const{data:e}=this.#t[t];e?.bitmap?.close()}this.#t=Object.create(null)}*[Symbol.iterator](){for(const t in this.#t){const{data:e}=this.#t[t];e!==ne&&(yield[t,e])}}}const Wn=1e5,js=30;class at{#t=Promise.withResolvers();#e=null;#s=!1;#i=!!globalThis.FontInspector?.enabled;#r=null;#n=null;#o=0;#a=0;#d=null;#l=null;#u=0;#c=0;#f=Object.create(null);#p=[];#g=null;#h=[];#m=new WeakMap;#b=null;static#A=new Map;static#w=new Map;static#v=new WeakMap;static#_=null;static#C=new Set;constructor({textContentSource:t,container:e,viewport:s}){if(t instanceof ReadableStream)this.#g=t;else if(typeof t=="object")this.#g=new ReadableStream({start(o){o.enqueue(t),o.close()}});else throw new Error('No "textContentSource" parameter specified.');this.#e=this.#l=e,this.#c=s.scale*xt.pixelRatio,this.#u=s.rotation,this.#n={div:null,properties:null,ctx:null};const{pageWidth:i,pageHeight:n,pageX:r,pageY:a}=s.rawDims;this.#b=[1,0,0,-1,-r,a+n],this.#a=i,this.#o=n,at.#k(),Ut(e,s),this.#t.promise.finally(()=>{at.#C.delete(this),this.#n=null,this.#f=null}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=it.platform;return N(this,"fontFamilyMap",new Map([["sans-serif",`${t&&e?"Calibri, ":""}sans-serif`],["monospace",`${t&&e?"Lucida Console, ":""}monospace`]]))}render(){const t=()=>{this.#d.read().then(({value:e,done:s})=>{if(s){this.#t.resolve();return}this.#r??=e.lang,Object.assign(this.#f,e.styles),this.#T(e.items),t()},this.#t.reject)};return this.#d=this.#g.getReader(),at.#C.add(this),t(),this.#t.promise}update({viewport:t,onBefore:e=null}){const s=t.scale*xt.pixelRatio,i=t.rotation;if(i!==this.#u&&(e?.(),this.#u=i,Ut(this.#l,{rotation:i})),s!==this.#c){e?.(),this.#c=s;const n={div:null,properties:null,ctx:at.#P(this.#r)};for(const r of this.#h)n.properties=this.#m.get(r),n.div=r,this.#S(n)}}cancel(){const t=new Nt("TextLayer task cancelled.");this.#d?.cancel(t).catch(()=>{}),this.#d=null,this.#t.reject(t)}get textDivs(){return this.#h}get textContentItemsStr(){return this.#p}#T(t){if(this.#s)return;this.#n.ctx??=at.#P(this.#r);const e=this.#h,s=this.#p;for(const i of t){if(e.length>Wn){F("Ignoring additional textDivs for performance reasons."),this.#s=!0;return}if(i.str===void 0){if(i.type==="beginMarkedContentProps"||i.type==="beginMarkedContent"){const n=this.#e;this.#e=document.createElement("span"),this.#e.classList.add("markedContent"),i.id!==null&&this.#e.setAttribute("id",`${i.id}`),n.append(this.#e)}else i.type==="endMarkedContent"&&(this.#e=this.#e.parentNode);continue}s.push(i.str),this.#R(i)}}#R(t){const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};this.#h.push(e);const i=R.transform(this.#b,t.transform);let n=Math.atan2(i[1],i[0]);const r=this.#f[t.fontName];r.vertical&&(n+=Math.PI/2);let a=this.#i&&r.fontSubstitution||r.fontFamily;a=at.fontFamilyMap.get(a)||a;const o=Math.hypot(i[2],i[3]),l=o*at.#M(a,r,this.#r);let h,d;n===0?(h=i[4],d=i[5]-l):(h=i[4]+l*Math.sin(n),d=i[5]-l*Math.cos(n));const u="calc(var(--total-scale-factor) *",f=e.style;this.#e===this.#l?(f.left=`${(100*h/this.#a).toFixed(2)}%`,f.top=`${(100*d/this.#o).toFixed(2)}%`):(f.left=`${u}${h.toFixed(2)}px)`,f.top=`${u}${d.toFixed(2)}px)`),f.fontSize=`${u}${(at.#_*o).toFixed(2)}px)`,f.fontFamily=a,s.fontSize=o,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,this.#i&&(e.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName),n!==0&&(s.angle=n*(180/Math.PI));let g=!1;if(t.str.length>1)g=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const p=Math.abs(t.transform[0]),b=Math.abs(t.transform[3]);p!==b&&Math.max(p,b)/Math.min(p,b)>1.5&&(g=!0)}if(g&&(s.canvasWidth=r.vertical?t.height:t.width),this.#m.set(e,s),this.#n.div=e,this.#n.properties=s,this.#S(this.#n),s.hasText&&this.#e.append(e),s.hasEOL){const p=document.createElement("br");p.setAttribute("role","presentation"),this.#e.append(p)}}#S(t){const{div:e,properties:s,ctx:i}=t,{style:n}=e;let r="";if(at.#_>1&&(r=`scale(${1/at.#_})`),s.canvasWidth!==0&&s.hasText){const{fontFamily:a}=n,{canvasWidth:o,fontSize:l}=s;at.#x(i,l*this.#c,a);const{width:h}=i.measureText(e.textContent);h>0&&(r=`scaleX(${o*this.#c/h}) ${r}`)}s.angle!==0&&(r=`rotate(${s.angle}deg) ${r}`),r.length>0&&(n.transform=r)}static cleanup(){if(!(this.#C.size>0)){this.#A.clear();for(const{canvas:t}of this.#w.values())t.remove();this.#w.clear()}}static#P(t=null){let e=this.#w.get(t||="");if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),this.#w.set(t,e),this.#v.set(e,{size:0,family:""})}return e}static#x(t,e,s){const i=this.#v.get(t);e===i.size&&s===i.family||(t.font=`${e}px ${s}`,i.size=e,i.family=s)}static#k(){if(this.#_!==null)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),this.#_=t.getBoundingClientRect().height,t.remove()}static#M(t,e,s){const i=this.#A.get(t);if(i)return i;const n=this.#P(s);n.canvas.width=n.canvas.height=js,this.#x(n,js,t);const r=n.measureText(""),a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let l=.8;return a?l=a/(a+o):(it.platform.isFirefox&&F("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?l=e.ascent:e.descent&&(l=1+e.descent)),this.#A.set(t,l),l}}class ue{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};function i(n){if(!n)return;let r=null;const a=n.name;if(a==="#text")r=n.value;else if(ue.shouldBuildText(a))n?.attributes?.textContent?r=n.attributes.textContent:n.value&&(r=n.value);else return;if(r!==null&&e.push({str:r}),!!n.children)for(const o of n.children)i(o)}return i(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const qn=100;function Xn(c={}){typeof c=="string"||c instanceof URL?c={url:c}:(c instanceof ArrayBuffer||ArrayBuffer.isView(c))&&(c={data:c});const t=new As,{docId:e}=t,s=c.url?en(c.url):null,i=c.data?sn(c.data):null,n=c.httpHeaders||null,r=c.withCredentials===!0,a=c.password??null,o=c.range instanceof mi?c.range:null,l=Number.isInteger(c.rangeChunkSize)&&c.rangeChunkSize>0?c.rangeChunkSize:2**16;let h=c.worker instanceof fe?c.worker:null;const d=c.verbosity,u=typeof c.docBaseUrl=="string"&&!Oe(c.docBaseUrl)?c.docBaseUrl:null,f=ve(c.cMapUrl),g=c.cMapPacked!==!1,p=c.CMapReaderFactory||(ot?dn:Ms),b=ve(c.iccUrl),m=ve(c.standardFontDataUrl),v=c.StandardFontDataFactory||(ot?un:Ls),A=ve(c.wasmUrl),w=c.WasmFactory||(ot?fn:Ds),y=c.stopAtErrors!==!0,_=Number.isInteger(c.maxImageSize)&&c.maxImageSize>-1?c.maxImageSize:-1,S=c.isEvalSupported!==!1,E=typeof c.isOffscreenCanvasSupported=="boolean"?c.isOffscreenCanvasSupported:!ot,C=typeof c.isImageDecoderSupported=="boolean"?c.isImageDecoderSupported:!ot&&(it.platform.isFirefox||!globalThis.chrome),M=Number.isInteger(c.canvasMaxAreaInBytes)?c.canvasMaxAreaInBytes:-1,I=typeof c.disableFontFace=="boolean"?c.disableFontFace:ot,H=c.fontExtraProperties===!0,G=c.enableXfa===!0,B=c.ownerDocument||globalThis.document,rt=c.disableRange===!0,T=c.disableStream===!0,P=c.disableAutoFetch===!0,gt=c.pdfBug===!0,Tt=c.CanvasFactory||(ot?cn:on),Bt=c.FilterFactory||(ot?hn:ln),Pt=c.enableHWA===!0,tt=c.useWasm!==!1,W=o?o.length:c.length??NaN,qt=typeof c.useSystemFonts=="boolean"?c.useSystemFonts:!ot&&!I,Mt=typeof c.useWorkerFetch=="boolean"?c.useWorkerFetch:!!(p===Ms&&v===Ls&&w===Ds&&f&&m&&A&&oe(f,document.baseURI)&&oe(m,document.baseURI)&&oe(A,document.baseURI)),Lt=null;Li(d);const V={canvasFactory:new Tt({ownerDocument:B,enableHWA:Pt}),filterFactory:new Bt({docId:e,ownerDocument:B}),cMapReaderFactory:Mt?null:new p({baseUrl:f,isCompressed:g}),standardFontDataFactory:Mt?null:new v({baseUrl:m}),wasmFactory:Mt?null:new w({baseUrl:A})};h||(h=fe.create({verbosity:d,port:de.workerPort}),t._worker=h);const se={docId:e,apiVersion:"5.3.93",data:i,password:a,disableAutoFetch:P,rangeChunkSize:l,length:W,docBaseUrl:u,enableXfa:G,evaluatorOptions:{maxImageSize:_,disableFontFace:I,ignoreErrors:y,isEvalSupported:S,isOffscreenCanvasSupported:E,isImageDecoderSupported:C,canvasMaxAreaInBytes:M,fontExtraProperties:H,useSystemFonts:qt,useWasm:tt,useWorkerFetch:Mt,cMapUrl:f,iccUrl:b,standardFontDataUrl:m,wasmUrl:A}},Pi={ownerDocument:B,pdfBug:gt,styleElement:Lt,loadingParams:{disableAutoFetch:P,enableXfa:G}};return h.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(h.destroyed)throw new Error("Worker was destroyed");const Ri=h.messageHandler.sendWithPromise("GetDocRequest",se,i?[i.buffer]:null);let Ge;if(o)Ge=new Rn(o,{disableRange:rt,disableStream:T});else if(!i){if(!s)throw new Error("getDocument - no `url` parameter provided.");const ze=oe(s)?Ln:ot?Un:Bn;Ge=new ze({url:s,length:W,httpHeaders:n,withCredentials:r,rangeChunkSize:l,disableRange:rt,disableStream:T})}return Ri.then(ze=>{if(t.destroyed)throw new Error("Loading aborted");if(h.destroyed)throw new Error("Worker was destroyed");const Es=new le(e,ze,h.port),Ii=new Qn(Es,t,Ge,Pi,V);t._transport=Ii,Es.send("Ready",null)})}).catch(t._capability.reject),t}class As{static#t=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId=`d${As.#t++}`;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await this._transport?.destroy()}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}async getData(){return this._transport.getData()}}class mi{#t=Promise.withResolvers();#e=[];#s=[];#i=[];#r=[];constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i}addRangeListener(t){this.#r.push(t)}addProgressListener(t){this.#i.push(t)}addProgressiveReadListener(t){this.#s.push(t)}addProgressiveDoneListener(t){this.#e.push(t)}onDataRange(t,e){for(const s of this.#r)s(t,e)}onDataProgress(t,e){this.#t.promise.then(()=>{for(const s of this.#i)s(t,e)})}onDataProgressiveRead(t){this.#t.promise.then(()=>{for(const e of this.#s)e(t)})}onDataProgressiveDone(){this.#t.promise.then(()=>{for(const t of this.#e)t()})}transportReady(){this.#t.resolve()}requestDataRange(t,e){$("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class Yn{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return N(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class Kn{#t=!1;constructor(t,e,s,i=!1){this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new Rs:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new gi,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:n=!1}={}){return new me({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return N(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=Dt.ENABLE,transform:n=null,background:r=null,optionalContentConfigPromise:a=null,annotationCanvasMap:o=null,pageColors:l=null,printAnnotationStorage:h=null,isEditing:d=!1}){this._stats?.time("Overall");const u=this._transport.getRenderingIntent(s,i,h,d),{renderingIntent:f,cacheKey:g}=u;this.#t=!1,a||=this._transport.getOptionalContentConfig(f);let p=this._intentStates.get(g);p||(p=Object.create(null),this._intentStates.set(g,p)),p.streamReaderCancelTimeout&&(clearTimeout(p.streamReaderCancelTimeout),p.streamReaderCancelTimeout=null);const b=!!(f&ft.PRINT);p.displayReadyCapability||(p.displayReadyCapability=Promise.withResolvers(),p.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(u));const m=w=>{p.renderTasks.delete(v),b&&(this.#t=!0),this.#e(),w?(v.capability.reject(w),this._abortOperatorList({intentState:p,reason:w instanceof Error?w:new Error(w)})):v.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},v=new Jt({callback:m,params:{canvasContext:t,viewport:e,transform:n,background:r},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:o,operatorList:p.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!b,pdfBug:this._pdfBug,pageColors:l});(p.renderTasks||=new Set).add(v);const A=v.task;return Promise.all([p.displayReadyCapability.promise,a]).then(([w,y])=>{if(this.destroyed){m();return}if(this._stats?.time("Rendering"),!(y.renderingIntent&f))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");v.initializeGraphics({transparency:w,optionalContentConfig:y}),v.operatorListChanged()}).catch(m),A}getOperatorList({intent:t="display",annotationMode:e=Dt.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){function n(){a.operatorList.lastChunk&&(a.opListReadCapability.resolve(a.operatorList),a.renderTasks.delete(o))}const r=this._transport.getRenderingIntent(t,e,s,i,!0);let a=this._intentStates.get(r.cacheKey);a||(a=Object.create(null),this._intentStates.set(r.cacheKey,a));let o;return a.opListReadCapability||(o=Object.create(null),o.operatorListChanged=n,a.opListReadCapability=Promise.withResolvers(),(a.renderTasks||=new Set).add(o),a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(r)),a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size(i){return i.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>ue.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){function n(){r.read().then(function({value:o,done:l}){if(l){s(a);return}a.lang??=o.lang,Object.assign(a.styles,o.styles),a.items.push(...o.items),n()},i)}const r=e.getReader(),a={items:[],styles:Object.create(null),lang:null};n()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),this.#t=!1,Promise.all(t)}cleanup(t=!1){this.#t=!0;const e=this.#e();return t&&e&&(this._stats&&=new Rs),e}#e(){if(!this.#t||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#t=!1,!0}_startRenderPage(t,e){const s=this._intentStates.get(e);s&&(this._stats?.timeEnd("Page Request"),s.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&this.#e()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:n,transfer:r}=s,o=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:i},r).getReader(),l=this._intentStates.get(e);l.streamReader=o;const h=()=>{o.read().then(({value:d,done:u})=>{if(u){l.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(d,l),h())},d=>{if(l.streamReader=null,!this._transport.destroyed){if(l.operatorList){l.operatorList.lastChunk=!0;for(const u of l.renderTasks)u.operatorListChanged();this.#e()}if(l.displayReadyCapability)l.displayReadyCapability.reject(d);else if(l.opListReadCapability)l.opListReadCapability.reject(d);else throw d}})};h()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof hs){let i=qn;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new Nt(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,n]of this._intentStates)if(n===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}var Ft,At,kt,$t,Le,Gt,zt,ht,Pe,bi,Ai,he,te,Re;const U=class U{constructor({name:t=null,port:e=null,verbosity:s=Di()}={}){wt(this,ht);wt(this,Ft,Promise.withResolvers());wt(this,At,null);wt(this,kt,null);wt(this,$t,null);if(this.name=t,this.destroyed=!1,this.verbosity=s,e){if(z(U,zt).has(e))throw new Error("Cannot use more than one PDFWorker per port.");z(U,zt).set(e,this),vt(this,ht,bi).call(this,e)}else vt(this,ht,Ai).call(this)}get promise(){return z(this,Ft).promise}get port(){return z(this,kt)}get messageHandler(){return z(this,At)}destroy(){this.destroyed=!0,z(this,$t)?.terminate(),ct(this,$t,null),z(U,zt).delete(z(this,kt)),ct(this,kt,null),z(this,At)?.destroy(),ct(this,At,null)}static create(t){const e=z(this,zt).get(t?.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.create - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new U(t)}static get workerSrc(){if(de.workerSrc)return de.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return N(this,"_setupFakeWorkerGlobal",(async()=>z(this,te,Re)?z(this,te,Re):(await import(this.workerSrc)).WorkerMessageHandler)())}};Ft=new WeakMap,At=new WeakMap,kt=new WeakMap,$t=new WeakMap,Le=new WeakMap,Gt=new WeakMap,zt=new WeakMap,ht=new WeakSet,Pe=function(){z(this,Ft).resolve(),z(this,At).send("configure",{verbosity:this.verbosity})},bi=function(t){ct(this,kt,t),ct(this,At,new le("main","worker",t)),z(this,At).on("ready",()=>{}),vt(this,ht,Pe).call(this)},Ai=function(){if(z(U,Gt)||z(U,te,Re)){vt(this,ht,he).call(this);return}let{workerSrc:t}=U;try{U._isSameOrigin(window.location,t)||(t=U._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new le("main","worker",e),i=()=>{n.abort(),s.destroy(),e.terminate(),this.destroyed?z(this,Ft).reject(new Error("Worker was destroyed")):vt(this,ht,he).call(this)},n=new AbortController;e.addEventListener("error",()=>{z(this,$t)||i()},{signal:n.signal}),s.on("test",a=>{if(n.abort(),this.destroyed||!a){i();return}ct(this,At,s),ct(this,kt,e),ct(this,$t,e),vt(this,ht,Pe).call(this)}),s.on("ready",a=>{if(n.abort(),this.destroyed){i();return}try{r()}catch{vt(this,ht,he).call(this)}});const r=()=>{const a=new Uint8Array;s.send("test",a,[a.buffer])};r();return}catch{Ne("The worker has been disabled.")}vt(this,ht,he).call(this)},he=function(){z(U,Gt)||(F("Setting up fake worker."),ct(U,Gt,!0)),U._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){z(this,Ft).reject(new Error("Worker was destroyed"));return}const e=new an;ct(this,kt,e);const s=`fake${xs(U,Le)._++}`,i=new le(s+"_worker",s,e);t.setup(i,e),ct(this,At,new le(s,s+"_worker",e)),vt(this,ht,Pe).call(this)}).catch(t=>{z(this,Ft).reject(new Error(`Setting up fake worker failed: "${t.message}".`))})},te=new WeakSet,Re=function(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}},wt(U,te),wt(U,Le,0),wt(U,Gt,!1),wt(U,zt,new WeakMap),ot&&(ct(U,Gt,!0),de.workerSrc||="./pdf.worker.mjs"),U._isSameOrigin=(t,e)=>{const s=URL.parse(t);if(!s?.origin||s.origin==="null")return!1;const i=new URL(e,s);return s.origin===i.origin},U._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))},U.fromPort=t=>{if(Wi("`PDFWorker.fromPort` - please use `PDFWorker.create` instead."),!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return U.create(t)};let fe=U;class Qn{#t=new Map;#e=new Map;#s=new Map;#i=new Map;#r=null;constructor(t,e,s,i,n){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new gi,this.fontLoader=new Zi({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.wasmFactory=n.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}#n(t,e=null){const s=this.#t.get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return this.#t.set(t,i),i}get annotationStorage(){return N(this,"annotationStorage",new ps)}getRenderingIntent(t,e=Dt.ENABLE,s=null,i=!1,n=!1){let r=ft.DISPLAY,a=es;switch(t){case"any":r=ft.ANY;break;case"display":break;case"print":r=ft.PRINT;break;default:F(`getRenderingIntent - invalid intent: ${t}`)}const o=r&ft.PRINT&&s instanceof ii?s:this.annotationStorage;switch(e){case Dt.DISABLE:r+=ft.ANNOTATIONS_DISABLE;break;case Dt.ENABLE:break;case Dt.ENABLE_FORMS:r+=ft.ANNOTATIONS_FORMS;break;case Dt.ENABLE_STORAGE:r+=ft.ANNOTATIONS_STORAGE,a=o.serializable;break;default:F(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(r+=ft.IS_EDITING),n&&(r+=ft.OPLIST);const{ids:l,hash:h}=o.modifiedIds,d=[r,a.hash,h];return{renderingIntent:r,cacheKey:d.join("_"),annotationStorageSerializable:a,modifiedIds:l}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#r?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const s of this.#e.values())t.push(s._destroy());this.#e.clear(),this.#s.clear(),this.#i.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#t.clear(),this.filterFactory.destroy(),at.cleanup(),this._networkStream?.cancelAllRequests(new Nt("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{J(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=n=>{this._lastProgress={loaded:n.loaded,total:n.total}},i.onPull=()=>{this._fullReader.read().then(function({value:n,done:r}){if(r){i.close();return}J(n instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(n),1,[n])}).catch(n=>{i.error(n)})},i.onCancel=n=>{this._fullReader.cancel(n),i.ready.catch(r=>{if(!this.destroyed)throw r})}}),t.on("ReaderHeadersReady",async s=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:n,contentLength:r}=this._fullReader;return(!i||!n)&&(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=a=>{e.onProgress?.({loaded:a.loaded,total:a.total})}),{isStreamingSupported:i,isRangeSupported:n,contentLength:r}}),t.on("GetRangeReader",(s,i)=>{J(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const n=this._networkStream.getRangeReader(s.begin,s.end);if(!n){i.close();return}i.onPull=()=>{n.read().then(function({value:r,done:a}){if(a){i.close();return}J(r instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(r),1,[r])}).catch(r=>{i.error(r)})},i.onCancel=r=>{n.cancel(r),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new Yn(s,this))}),t.on("DocException",s=>{e._capability.reject(dt(s))}),t.on("PasswordRequest",s=>{this.#r=Promise.withResolvers();try{if(!e.onPassword)throw dt(s);const i=n=>{n instanceof Error?this.#r.reject(n):this.#r.resolve({password:n})};e.onPassword(i,s.code)}catch(i){this.#r.reject(i)}return this.#r.promise}),t.on("DataLoaded",s=>{e.onProgress?.({loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{if(this.destroyed)return;this.#e.get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,n])=>{if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":if("error"in n){const l=n.error;F(`Error during font loading: ${l}`),this.commonObjs.resolve(s,l);break}const r=this._params.pdfBug&&globalThis.FontInspector?.enabled?(l,h)=>globalThis.FontInspector.fontAdded(l,h):null,a=new tn(n,r);this.fontLoader.bind(a).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!a.fontExtraProperties&&a.data&&(a.data=null),this.commonObjs.resolve(s,a)});break;case"CopyLocalImage":const{imageRef:o}=n;J(o,"The imageRef must be defined.");for(const l of this.#e.values())for(const[,h]of l.objs)if(h?.ref===o)return h.dataLen?(this.commonObjs.resolve(s,structuredClone(h)),h.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,n);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,n,r])=>{if(this.destroyed)return;const a=this.#e.get(i);if(!a.objs.has(s)){if(a._intentStates.size===0){r?.bitmap?.close();return}switch(n){case"Image":case"Pattern":a.objs.resolve(s,r);break;default:throw new Error(`Got unknown object type ${n}`)}}}),t.on("DocProgress",s=>{this.destroyed||e.onProgress?.({loaded:s.loaded,total:s.total})}),t.on("FetchBinaryData",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");const i=this[s.type];if(!i)throw new Error(`${s.type} not initialized, see the \`useWorkerFetch\` parameter.`);return i.fetch(s)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&F("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=this.#s.get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(n=>{if(this.destroyed)throw new Error("Transport destroyed");n.refStr&&this.#i.set(n.refStr,t);const r=new Kn(e,n,this,this._params.pdfBug);return this.#e.set(e,r),r});return this.#s.set(e,i),i}getPageIndex(t){return ss(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#n("GetFieldObjects")}hasJSActions(){return this.#n("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#n("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#n("GetOptionalContentConfig").then(e=>new Pn(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#t.get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>({info:i[0],metadata:i[1]?new xn(i[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null}));return this.#t.set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const e of this.#e.values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),this.#t.clear(),this.filterFactory.destroy(!0),at.cleanup()}}cachedPageNumber(t){if(!ss(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return this.#i.get(e)??null}}class Jn{#t=null;onContinue=null;onError=null;constructor(t){this.#t=t}get promise(){return this.#t.capability.promise}cancel(t=0){this.#t.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#t.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#t;return t.form||t.canvas&&e?.size>0}}class Jt{#t=null;static#e=new WeakSet;constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:n,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:u=null}){this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=r,this._pageIndex=a,this.canvasFactory=o,this.filterFactory=l,this._pdfBug=d,this.pageColors=u,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=h===!0&&typeof window<"u",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new Jn(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(Jt.#e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");Jt.#e.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:s,viewport:i,transform:n,background:r}=this.params;this.gfx=new Zt(s,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:n,viewport:i,transparency:t,background:r}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),this.#t&&(window.cancelAnimationFrame(this.#t),this.#t=null),Jt.#e.delete(this._canvas),t||=new hs(`Rendering cancelled, page ${this._pageIndex+1}`,e),this.callback(t),this.task.onError?.(t)}operatorListChanged(){if(!this.graphicsReady){this.graphicsReadyCallback||=this._continueBound;return}this.stepper?.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#t=window.requestAnimationFrame(()=>{this.#t=null,this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),Jt.#e.delete(this._canvas),this.callback())))}}const Zn="5.3.93",tr="cbeef3233";function Vs(c){return Math.floor(Math.max(0,Math.min(1,c))*255).toString(16).padStart(2,"0")}function re(c){return Math.max(0,Math.min(255,255*c))}class Ws{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return t=re(t),[t,t,t]}static G_HTML([t]){const e=Vs(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(re)}static RGB_HTML(t){return`#${t.map(Vs).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[re(1-Math.min(1,t+i)),re(1-Math.min(1,s+i)),re(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,n=1-e,r=1-s,a=Math.min(i,n,r);return["CMYK",i,n,r,a]}}class er{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){$("Abstract method `_createSVG` called.")}}class Me extends er{_createSVG(t){return document.createElementNS(Rt,t)}}class yi{static setupStorage(t,e,s,i,n){const r=i.getValue(e,{value:null});switch(s.name){case"textarea":if(r.value!==null&&(t.textContent=r.value),n==="print")break;t.addEventListener("input",a=>{i.setValue(e,{value:a.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(r.value===s.attributes.xfaOn?t.setAttribute("checked",!0):r.value===s.attributes.xfaOff&&t.removeAttribute("checked"),n==="print")break;t.addEventListener("change",a=>{i.setValue(e,{value:a.target.checked?a.target.getAttribute("xfaOn"):a.target.getAttribute("xfaOff")})})}else{if(r.value!==null&&t.setAttribute("value",r.value),n==="print")break;t.addEventListener("input",a=>{i.setValue(e,{value:a.target.value})})}break;case"select":if(r.value!==null){t.setAttribute("value",r.value);for(const a of s.children)a.attributes.value===r.value?a.attributes.selected=!0:a.attributes.hasOwnProperty("selected")&&delete a.attributes.selected}t.addEventListener("input",a=>{const o=a.target.options,l=o.selectedIndex===-1?"":o[o.selectedIndex].value;i.setValue(e,{value:l})});break}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:n}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;r.type==="radio"&&(r.name=`${r.name}-${i}`);for(const[o,l]of Object.entries(r))if(l!=null)switch(o){case"class":l.length&&t.setAttribute(o,l.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",l);break;case"style":Object.assign(t.style,l);break;case"textContent":t.textContent=l;break;default:(!a||o!=="href"&&o!=="newWindow")&&t.setAttribute(o,l)}a&&n.addLinkAttributes(t,r.href,r.newWindow),s&&r.dataId&&this.setupStorage(t,r.dataId,e,s)}static render(t){const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,n=t.intent||"display",r=document.createElement(i.name);i.attributes&&this.setAttributes({html:r,element:i,intent:n,linkService:s});const a=n!=="richText",o=t.div;if(o.append(r),t.viewport){const d=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=d}a&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(i.children.length===0){if(i.value){const d=document.createTextNode(i.value);r.append(d),a&&ue.shouldBuildText(i.name)&&l.push(d)}return{textDivs:l}}const h=[[i,-1,r]];for(;h.length>0;){const[d,u,f]=h.at(-1);if(u+1===d.children.length){h.pop();continue}const g=d.children[++h.at(-1)[1]];if(g===null)continue;const{name:p}=g;if(p==="#text"){const m=document.createTextNode(g.value);l.push(m),f.append(m);continue}const b=g?.attributes?.xmlns?document.createElementNS(g.attributes.xmlns,p):document.createElement(p);if(f.append(b),g.attributes&&this.setAttributes({html:b,element:g,storage:e,intent:n,linkService:s}),g.children?.length>0)h.push([g,-1,b]);else if(g.value){const m=document.createTextNode(g.value);a&&ue.shouldBuildText(p)&&l.push(m),b.append(m)}}for(const d of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))d.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const sr=9,jt=new WeakSet;class qs{static create(t){switch(t.data.annotationType){case K.LINK:return new wi(t);case K.TEXT:return new ir(t);case K.WIDGET:switch(t.data.fieldType){case"Tx":return new nr(t);case"Btn":return t.data.radioButton?new vi(t):t.data.checkBox?new ar(t):new or(t);case"Ch":return new lr(t);case"Sig":return new rr(t)}return new Wt(t);case K.POPUP:return new ns(t);case K.FREETEXT:return new _i(t);case K.LINE:return new cr(t);case K.SQUARE:return new dr(t);case K.CIRCLE:return new ur(t);case K.POLYLINE:return new Si(t);case K.CARET:return new pr(t);case K.INK:return new ys(t);case K.POLYGON:return new fr(t);case K.HIGHLIGHT:return new Ei(t);case K.UNDERLINE:return new gr(t);case K.SQUIGGLY:return new mr(t);case K.STRIKEOUT:return new br(t);case K.STAMP:return new Ci(t);case K.FILEATTACHMENT:return new Ar(t);default:return new X(t)}}}class X{#t=null;#e=!1;#s=null;constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({contentsObj:t,richText:e}){return!!(t?.str||e?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return X._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#t||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#i(e),this.#s?.popup.updateEdited(t)}resetEdited(){this.#t&&(this.#i(this.#t.rect),this.#s?.popup.resetEdited(),this.#t=null)}#i(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:r,pageX:a,pageY:o}}}}=this;s?.splice(0,4,...t),e.left=`${100*(t[0]-a)/n}%`,e.top=`${100*(r-t[3]+o)/r}%`,i===0?(e.width=`${100*(t[2]-t[0])/n}%`,e.height=`${100*(t[3]-t[1])/r}%`):this.setRotation(i)}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),this instanceof Wt||(n.tabIndex=0);const{style:r}=n;if(r.zIndex=this.parent.zIndex++,e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof ns){const{rotation:p}=e;return!e.hasOwnCanvas&&p!==0&&this.setRotation(p,n),n}const{width:a,height:o}=this;if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;const p=e.borderStyle.horizontalCornerRadius,b=e.borderStyle.verticalCornerRadius;if(p>0||b>0){const v=`calc(${p}px * var(--total-scale-factor)) / calc(${b}px * var(--total-scale-factor))`;r.borderRadius=v}else if(this instanceof vi){const v=`calc(${a}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;r.borderRadius=v}switch(e.borderStyle.style){case Xt.SOLID:r.borderStyle="solid";break;case Xt.DASHED:r.borderStyle="dashed";break;case Xt.BEVELED:F("Unimplemented border style: beveled");break;case Xt.INSET:F("Unimplemented border style: inset");break;case Xt.UNDERLINE:r.borderBottomStyle="solid";break}const m=e.borderColor||null;m?(this.#e=!0,r.borderColor=R.makeHexColor(m[0]|0,m[1]|0,m[2]|0)):r.borderWidth=0}const l=R.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:h,pageHeight:d,pageX:u,pageY:f}=i.rawDims;r.left=`${100*(l[0]-u)/h}%`,r.top=`${100*(l[1]-f)/d}%`;const{rotation:g}=e;return e.hasOwnCanvas||g===0?(r.width=`${100*a/h}%`,r.height=`${100*o/d}%`):this.setRotation(g,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims;let{width:n,height:r}=this;t%180!==0&&([n,r]=[r,n]),e.style.width=`${100*n/s}%`,e.style.height=`${100*r/i}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const n=i.detail[e],r=n[0],a=n.slice(1);i.target.style[s]=Ws[`${r}_HTML`](a),this.annotationStorage.setValue(this.data.id,{[s]:Ws[`${r}_rgb`](a)})};return N(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail))(t[i]||s[i])?.(e)}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,n]of Object.entries(e)){const r=s[i];if(r){const a={detail:{[i]:n},target:t};r(a),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,n]=this.data.rect.map(p=>Math.fround(p));if(t.length===8){const[p,b,m,v]=t.subarray(2,6);if(i===p&&n===b&&e===m&&s===v)return}const{style:r}=this.container;let a;if(this.#e){const{borderColor:p,borderWidth:b}=r;r.borderWidth=0,a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${p}" stroke-width="${b}">`],this.container.classList.add("hasBorder")}const o=i-e,l=n-s,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0),d.role="none";const u=h.createElement("defs");d.append(u);const f=h.createElement("clipPath"),g=`clippath_${this.data.id}`;f.setAttribute("id",g),f.setAttribute("clipPathUnits","objectBoundingBox"),u.append(f);for(let p=2,b=t.length;p<b;p+=8){const m=t[p],v=t[p+1],A=t[p+2],w=t[p+3],y=h.createElement("rect"),_=(A-e)/o,S=(n-v)/l,E=(m-A)/o,C=(v-w)/l;y.setAttribute("x",_),y.setAttribute("y",S),y.setAttribute("width",E),y.setAttribute("height",C),f.append(y),a?.push(`<rect vector-effect="non-scaling-stroke" x="${_}" y="${S}" width="${E}" height="${C}"/>`)}this.#e&&(a.push("</g></svg>')"),r.backgroundImage=a.join("")),this.container.append(d),this.container.style.clipPath=`url(#${g})`}_createPopup(){const{data:t}=this,e=this.#s=new ns({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation,noRotate:!0},parent:this.parent,elements:[this]});this.parent.div.append(e.render())}render(){$("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:n,id:r,exportValues:a}of i){if(n===-1||r===e)continue;const o=typeof a=="string"?a:null,l=document.querySelector(`[data-element-id="${r}"]`);if(l&&!jt.has(l)){F(`_getElementsByName - element not allowed: ${r}`);continue}s.push({id:r,exportValue:o,domElement:l})}return s}for(const i of document.getElementsByName(t)){const{exportValue:n}=i,r=i.getAttribute("data-element-id");r!==e&&jt.has(i)&&s.push({id:r,exportValue:n,domElement:i})}return s}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e,mustEnterInEditMode:!0})})}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class wi extends X{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,s=document.createElement("a");s.setAttribute("data-element-id",t.id);let i=!1;return t.url?(e.addLinkAttributes(s,t.url,t.newWindow),i=!0):t.action?(this._bindNamedAction(s,t.action),i=!0):t.attachment?(this.#e(s,t.attachment,t.attachmentDest),i=!0):t.setOCGState?(this.#s(s,t.setOCGState),i=!0):t.dest?(this._bindLink(s,t.dest),i=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(s,t),i=!0),t.resetForm?(this._bindResetFormAction(s,t.resetForm),i=!0):this.isTooltipOnly&&!i&&(this._bindLink(s,""),i=!0)),this.container.classList.add("linkAnnotation"),i&&this.container.append(s),this.container}#t(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||e==="")&&this.#t()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#t()}#e(t,e,s=null){t.href=this.linkService.getAnchorUrl(""),e.description&&(t.title=e.description),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,s),!1),this.#t()}#s(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#t()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const s=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const i of Object.keys(e.actions)){const n=s.get(i);n&&(t[n]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:i}}),!1))}t.onclick||(t.onclick=()=>!1),this.#t()}_bindResetFormAction(t,e){const s=t.onclick;if(s||(t.href=this.linkService.getAnchorUrl("")),this.#t(),!this._fieldObjects){F('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),s||(t.onclick=()=>!1);return}t.onclick=()=>{s?.();const{fields:i,refs:n,include:r}=e,a=[];if(i.length!==0||n.length!==0){const h=new Set(n);for(const d of i){const u=this._fieldObjects[d]||[];for(const{id:f}of u)h.add(f)}for(const d of Object.values(this._fieldObjects))for(const u of d)h.has(u.id)===r&&a.push(u)}else for(const h of Object.values(this._fieldObjects))a.push(...h);const o=this.annotationStorage,l=[];for(const h of a){const{id:d}=h;switch(l.push(d),h.type){case"text":{const f=h.defaultValue||"";o.setValue(d,{value:f});break}case"checkbox":case"radiobutton":{const f=h.defaultValue===h.exportValues;o.setValue(d,{value:f});break}case"combobox":case"listbox":{const f=h.defaultValue||"";o.setValue(d,{value:f});break}default:continue}const u=document.querySelector(`[data-element-id="${d}"]`);if(u){if(!jt.has(u)){F(`_bindResetFormAction - element not allowed: ${d}`);continue}}else continue;u.dispatchEvent(new Event("resetform"))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}}),!1}}}class ir extends X{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class Wt extends X{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&(t.previousSibling?.nodeName==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return it.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,n){s.includes("mouse")?t.addEventListener(s,r=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(r),shift:r.shiftKey,modifier:this._getKeyModifier(r)}})}):t.addEventListener(s,r=>{if(s==="blur"){if(!e.focused||!r.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(r)}})})}_setEventListeners(t,e,s,i){for(const[n,r]of s)(r==="Action"||this.data.actions?.[r])&&((r==="Focus"||r==="Blur")&&(e||={focused:!1}),this._setEventListener(t,e,n,r,i),r==="Focus"&&!this.data.actions?.Blur?this._setEventListener(t,e,"blur","Blur",null):r==="Blur"&&!this.data.actions?.Focus&&this._setEventListener(t,e,"focus","Focus",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":R.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||sr,n=t.style;let r;const a=2,o=l=>Math.round(10*l)/10;if(this.data.multiLine){const l=Math.abs(this.data.rect[3]-this.data.rect[1]-a),h=Math.round(l/(je*i))||1,d=l/h;r=Math.min(i,o(d/je))}else{const l=Math.abs(this.data.rect[3]-this.data.rect[1]-a);r=Math.min(i,o(l/je))}n.fontSize=`calc(${r}px * var(--total-scale-factor))`,n.color=R.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class nr extends Wt{constructor(t){const e=t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,s,i){const n=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id))r.domElement&&(r.domElement[e]=s),n.setValue(r.id,{[i]:s})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const i=t.getValue(e,{value:this.data.fieldValue});let n=i.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&n.length>r&&(n=n.slice(0,r));let a=i.formattedValue||this.data.textContent?.join(`
`)||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=a??n,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type=this.data.password?"password":"text",s.setAttribute("value",a??n),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),jt.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=0;const l=this.data.dateFormat||this.data.timeFormat;l&&(s.title=l),this._setRequired(s,this.data.required),r&&(s.maxLength=r),s.addEventListener("input",d=>{t.setValue(e,{value:d.target.value}),this.setPropertyOnSiblings(s,"value",d.target.value,"value"),o.formattedValue=null}),s.addEventListener("resetform",d=>{const u=this.data.defaultFieldValue??"";s.value=o.userValue=u,o.formattedValue=null});let h=d=>{const{formattedValue:u}=o;u!=null&&(d.target.value=u),d.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",u=>{if(o.focused)return;const{target:f}=u;o.userValue&&(f.value=o.userValue),o.lastCommittedValue=f.value,o.commitKey=1,this.data.actions?.Focus||(o.focused=!0)}),s.addEventListener("updatefromsandbox",u=>{this.showElementAndHideCanvas(u.target);const f={value(g){o.userValue=g.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),g.target.value=o.userValue},formattedValue(g){const{formattedValue:p}=g.detail;o.formattedValue=p,p!=null&&g.target!==document.activeElement&&(g.target.value=p),t.setValue(e,{formattedValue:p})},selRange(g){g.target.setSelectionRange(...g.detail.selRange)},charLimit:g=>{const{charLimit:p}=g.detail,{target:b}=g;if(p===0){b.removeAttribute("maxLength");return}b.setAttribute("maxLength",p);let m=o.userValue;!m||m.length<=p||(m=m.slice(0,p),b.value=o.userValue=m,t.setValue(e,{value:m}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:m,willCommit:!0,commitKey:1,selStart:b.selectionStart,selEnd:b.selectionEnd}}))}};this._dispatchEventFromSandbox(f,u)}),s.addEventListener("keydown",u=>{o.commitKey=1;let f=-1;if(u.key==="Escape"?f=0:u.key==="Enter"&&!this.data.multiLine?f=2:u.key==="Tab"&&(o.commitKey=3),f===-1)return;const{value:g}=u.target;o.lastCommittedValue!==g&&(o.lastCommittedValue=g,o.userValue=g,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:g,willCommit:!0,commitKey:f,selStart:u.target.selectionStart,selEnd:u.target.selectionEnd}}))});const d=h;h=null,s.addEventListener("blur",u=>{if(!o.focused||!u.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:f}=u.target;o.userValue=f,o.lastCommittedValue!==f&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:f,willCommit:!0,commitKey:o.commitKey,selStart:u.target.selectionStart,selEnd:u.target.selectionEnd}}),d(u)}),this.data.actions?.Keystroke&&s.addEventListener("beforeinput",u=>{o.lastCommittedValue=null;const{data:f,target:g}=u,{value:p,selectionStart:b,selectionEnd:m}=g;let v=b,A=m;switch(u.inputType){case"deleteWordBackward":{const w=p.substring(0,b).match(/\w*[^\w]*$/);w&&(v-=w[0].length);break}case"deleteWordForward":{const w=p.substring(b).match(/^[^\w]*\w*/);w&&(A+=w[0].length);break}case"deleteContentBackward":b===m&&(v-=1);break;case"deleteContentForward":b===m&&(A+=1);break}u.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:p,change:f||"",willCommit:!1,selStart:v,selEnd:A}})}),this._setEventListeners(s,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],u=>u.target.value)}if(h&&s.addEventListener("blur",h),this.data.comb){const u=(this.data.rect[2]-this.data.rect[0])/r;s.classList.add("comb"),s.style.letterSpacing=`calc(${u}px * var(--total-scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class rr extends Wt{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class ar extends Wt{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return jt.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=0,n.addEventListener("change",r=>{const{name:a,checked:o}=r.target;for(const l of this._getElementsByName(a,s)){const h=o&&l.exportValue===e.exportValue;l.domElement&&(l.domElement.checked=h),t.setValue(l.id,{value:h})}t.setValue(s,{value:o})}),n.addEventListener("resetform",r=>{const a=e.defaultFieldValue||"Off";r.target.checked=a===e.exportValue}),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",r=>{const a={value(o){o.target.checked=o.detail.value!=="Off",t.setValue(s,{value:o.target.checked})}};this._dispatchEventFromSandbox(a,r)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],r=>r.target.checked)),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class vi extends Wt{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const r of this._getElementsByName(e.fieldName,s))t.setValue(r.id,{value:!1});const n=document.createElement("input");if(jt.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.tabIndex=0,n.addEventListener("change",r=>{const{name:a,checked:o}=r.target;for(const l of this._getElementsByName(a,s))t.setValue(l.id,{value:!1});t.setValue(s,{value:o})}),n.addEventListener("resetform",r=>{const a=e.defaultFieldValue;r.target.checked=a!=null&&a===e.buttonValue}),this.enableScripting&&this.hasJSActions){const r=e.buttonValue;n.addEventListener("updatefromsandbox",a=>{const o={value:l=>{const h=r===l.detail.value;for(const d of this._getElementsByName(l.target.name)){const u=h&&d.id===s;d.domElement&&(d.domElement.checked=u),t.setValue(d.id,{value:u})}}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class or extends wi{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class lr extends Wt{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");jt.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=0;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",h=>{const d=this.data.defaultFieldValue;for(const u of i.options)u.selected=u.value===d});for(const h of this.data.options){const d=document.createElement("option");d.textContent=h.displayValue,d.value=h.exportValue,s.value.includes(h.exportValue)&&(d.setAttribute("selected",!0),n=!1),i.append(d)}let r=null;if(n){const h=document.createElement("option");h.value=" ",h.setAttribute("hidden",!0),h.setAttribute("selected",!0),i.prepend(h),r=()=>{h.remove(),i.removeEventListener("input",r),r=null},i.addEventListener("input",r)}const a=h=>{const d=h?"value":"textContent",{options:u,multiple:f}=i;return f?Array.prototype.filter.call(u,g=>g.selected).map(g=>g[d]):u.selectedIndex===-1?null:u[u.selectedIndex][d]};let o=a(!1);const l=h=>{const d=h.target.options;return Array.prototype.map.call(d,u=>({displayValue:u.textContent,exportValue:u.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",h=>{const d={value(u){r?.();const f=u.detail.value,g=new Set(Array.isArray(f)?f:[f]);for(const p of i.options)p.selected=g.has(p.value);t.setValue(e,{value:a(!0)}),o=a(!1)},multipleSelection(u){i.multiple=!0},remove(u){const f=i.options,g=u.detail.remove;f[g].selected=!1,i.remove(g),f.length>0&&Array.prototype.findIndex.call(f,b=>b.selected)===-1&&(f[0].selected=!0),t.setValue(e,{value:a(!0),items:l(u)}),o=a(!1)},clear(u){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),o=a(!1)},insert(u){const{index:f,displayValue:g,exportValue:p}=u.detail.insert,b=i.children[f],m=document.createElement("option");m.textContent=g,m.value=p,b?b.before(m):i.append(m),t.setValue(e,{value:a(!0),items:l(u)}),o=a(!1)},items(u){const{items:f}=u.detail;for(;i.length!==0;)i.remove(0);for(const g of f){const{displayValue:p,exportValue:b}=g,m=document.createElement("option");m.textContent=p,m.value=b,i.append(m)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:a(!0),items:l(u)}),o=a(!1)},indices(u){const f=new Set(u.detail.indices);for(const g of u.target.options)g.selected=f.has(g.index);t.setValue(e,{value:a(!0)}),o=a(!1)},editable(u){u.target.disabled=!u.detail.editable}};this._dispatchEventFromSandbox(d,h)}),i.addEventListener("input",h=>{const d=a(!0),u=a(!1);t.setValue(e,{value:d}),h.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:u,changeEx:d,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],h=>h.target.value)):i.addEventListener("input",function(h){t.setValue(e,{value:a(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class ns extends X{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:X._hasPopupData(e)}),this.elements=s,this.popup=null}render(){const{container:t}=this;t.classList.add("popupAnnotation"),t.role="comment";const e=this.popup=new hr({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),s=[];for(const i of this.elements)i.popup=e,i.container.ariaHasPopup="dialog",s.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",s.map(i=>`${ls}${i}`).join(",")),this.container}}class hr{#t=this.#T.bind(this);#e=this.#x.bind(this);#s=this.#P.bind(this);#i=this.#S.bind(this);#r=null;#n=null;#o=null;#a=null;#d=null;#l=null;#u=null;#c=!1;#f=null;#p=null;#g=null;#h=null;#m=null;#b=null;#A=!1;constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:n,contentsObj:r,richText:a,parent:o,rect:l,parentRect:h,open:d}){this.#n=t,this.#m=i,this.#o=r,this.#h=a,this.#l=o,this.#r=e,this.#g=l,this.#u=h,this.#d=s,this.#a=ti.toDateObject(n),this.trigger=s.flatMap(u=>u.getElementsToTriggerPopup());for(const u of this.trigger)u.addEventListener("click",this.#i),u.addEventListener("mouseenter",this.#s),u.addEventListener("mouseleave",this.#e),u.classList.add("popupTriggerArea");for(const u of s)u.container?.addEventListener("keydown",this.#t);this.#n.hidden=!0,d&&this.#S()}render(){if(this.#f)return;const t=this.#f=document.createElement("div");if(t.className="popup",this.#r){const n=t.style.outlineColor=R.makeHexColor(...this.#r);t.style.backgroundColor=`color-mix(in srgb, ${n} 30%, white)`}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),{dir:s.dir,str:s.textContent}=this.#m,t.append(e),this.#a){const n=document.createElement("time");n.classList.add("popupDate"),n.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),n.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#a.valueOf()})),n.dateTime=this.#a.toISOString(),e.append(n)}const i=this.#w;if(i)yi.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const n=this._formatContents(this.#o);t.append(n)}this.#n.append(t)}get#w(){const t=this.#h,e=this.#o;return t?.str&&(!e?.str||e.str===t.str)&&this.#h.html||null}get#v(){return this.#w?.attributes?.style?.fontSize||0}get#_(){return this.#w?.attributes?.style?.color||null}#C(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:this.#_,fontSize:this.#v?`calc(${this.#v}px * var(--total-scale-factor))`:""}};for(const n of t.split(`
`))e.push({name:"span",value:n,attributes:i});return s}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let n=0,r=i.length;n<r;++n){const a=i[n];s.append(document.createTextNode(a)),n<r-1&&s.append(document.createElement("br"))}return s}#T(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&this.#c)&&this.#S()}updateEdited({rect:t,popupContent:e}){this.#b||={contentsObj:this.#o,richText:this.#h},t&&(this.#p=null),e&&(this.#h=this.#C(e),this.#o=null),this.#f?.remove(),this.#f=null}resetEdited(){this.#b&&({contentsObj:this.#o,richText:this.#h}=this.#b,this.#b=null,this.#f?.remove(),this.#f=null,this.#p=null)}#R(){if(this.#p!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:n}}}=this.#l;let r=!!this.#u,a=r?this.#u:this.#g;for(const g of this.#d)if(!a||R.intersect(g.data.rect,a)!==null){a=g.data.rect,r=!0;break}const o=R.normalizeRect([a[0],t[3]-a[1]+t[1],a[2],t[3]-a[3]+t[1]]),h=r?a[2]-a[0]+5:0,d=o[0]+h,u=o[1];this.#p=[100*(d-i)/e,100*(u-n)/s];const{style:f}=this.#n;f.left=`${this.#p[0]}%`,f.top=`${this.#p[1]}%`}#S(){this.#c=!this.#c,this.#c?(this.#P(),this.#n.addEventListener("click",this.#i),this.#n.addEventListener("keydown",this.#t)):(this.#x(),this.#n.removeEventListener("click",this.#i),this.#n.removeEventListener("keydown",this.#t))}#P(){this.#f||this.render(),this.isVisible?this.#c&&this.#n.classList.add("focused"):(this.#R(),this.#n.hidden=!1,this.#n.style.zIndex=parseInt(this.#n.style.zIndex)+1e3)}#x(){this.#n.classList.remove("focused"),!(this.#c||!this.isVisible)&&(this.#n.hidden=!0,this.#n.style.zIndex=parseInt(this.#n.style.zIndex)-1e3)}forceHide(){this.#A=this.isVisible,this.#A&&(this.#n.hidden=!0)}maybeShow(){this.#A&&(this.#f||this.#P(),this.#A=!1,this.#n.hidden=!1)}get isVisible(){return this.#n.hidden===!1}}class _i extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=D.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class cr extends X{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=this.#t=this.svgFactory.createElement("svg:line");return n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class dr extends X{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=t.borderStyle.width,r=this.#t=this.svgFactory.createElement("svg:rect");return r.setAttribute("x",n/2),r.setAttribute("y",n/2),r.setAttribute("width",e-n),r.setAttribute("height",s-n),r.setAttribute("stroke-width",n||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),i.append(r),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class ur extends X{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=t.borderStyle.width,r=this.#t=this.svgFactory.createElement("svg:ellipse");return r.setAttribute("cx",e/2),r.setAttribute("cy",s/2),r.setAttribute("rx",e/2-n/2),r.setAttribute("ry",s/2-n/2),r.setAttribute("stroke-width",n||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),i.append(r),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class Si extends X{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:s,popupRef:i},width:n,height:r}=this;if(!e)return this.container;const a=this.svgFactory.create(n,r,!0);let o=[];for(let h=0,d=e.length;h<d;h+=2){const u=e[h]-t[0],f=t[3]-e[h+1];o.push(`${u},${f}`)}o=o.join(" ");const l=this.#t=this.svgFactory.createElement(this.svgElementName);return l.setAttribute("points",o),l.setAttribute("stroke-width",s.width||1),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),a.append(l),this.container.append(a),!i&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class fr extends Si{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class pr extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class ys extends X{#t=null;#e=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=this.data.it==="InkHighlight"?D.HIGHLIGHT:D.INK}#s(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:s,borderStyle:i,popupRef:n}}=this,{transform:r,width:a,height:o}=this.#s(e,t),l=this.svgFactory.create(a,o,!0),h=this.#t=this.svgFactory.createElement("svg:g");l.append(h),h.setAttribute("stroke-width",i.width||1),h.setAttribute("stroke-linecap","round"),h.setAttribute("stroke-linejoin","round"),h.setAttribute("stroke-miterlimit",10),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),h.setAttribute("transform",r);for(let d=0,u=s.length;d<u;d++){const f=this.svgFactory.createElement(this.svgElementName);this.#e.push(f),f.setAttribute("points",s[d].join(",")),h.append(f)}return!n&&this.hasPopupData&&this._createPopup(),this.container.append(l),this._editOnDoubleClick(),this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:s,rect:i}=t,n=this.#t;if(e>=0&&n.setAttribute("stroke-width",e||1),s)for(let r=0,a=this.#e.length;r<a;r++)this.#e[r].setAttribute("points",s[r].join(","));if(i){const{transform:r,width:a,height:o}=this.#s(this.data.rotation,i);n.parentElement.setAttribute("viewBox",`0 0 ${a} ${o}`),n.setAttribute("transform",r)}}getElementsToTriggerPopup(){return this.#e}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ei extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=D.HIGHLIGHT}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),t){const s=document.createElement("mark");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class gr extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),t){const s=document.createElement("u");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class mr extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),t){const s=document.createElement("u");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class br extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),t){const s=document.createElement("s");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class Ci extends X{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=D.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class Ar extends X{#t=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let s;e.hasAppearance||e.fillAlpha===0?s=document.createElement("div"):(s=document.createElement("img"),s.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(s.style=`filter: opacity(${Math.round(e.fillAlpha*100)}%);`)),s.addEventListener("dblclick",this.#e.bind(this)),this.#t=s;const{isMac:i}=it.platform;return t.addEventListener("keydown",n=>{n.key==="Enter"&&(i?n.metaKey:n.ctrlKey)&&this.#e()}),!e.popupRef&&this.hasPopupData?this._createPopup():s.classList.add("popupTriggerArea"),t.append(s),t}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}#e(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class ws{#t=null;#e=null;#s=new Map;#i=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:n,viewport:r,structTreeLayer:a}){this.div=t,this.#t=e,this.#e=s,this.#i=a||null,this.page=n,this.viewport=r,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return this.#s.size>0}async#r(t,e,s){const i=t.firstChild||t,n=i.id=`${ls}${e}`,r=await this.#i?.getAriaAttributes(n);if(r)for(const[a,o]of r)i.setAttribute(a,o);s?s.at(-1).container.after(t):(this.div.append(t),this.#t?.moveElementInDOM(this.div,t,i,!1))}async render(t){const{annotations:e}=t,s=this.div;Ut(s,this.viewport);const i=new Map,n={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new Me,annotationStorage:t.annotationStorage||new ps,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const r of e){if(r.noHTML)continue;const a=r.annotationType===K.POPUP;if(a){const h=i.get(r.id);if(!h)continue;n.elements=h}else if(r.rect[2]===r.rect[0]||r.rect[3]===r.rect[1])continue;n.data=r;const o=qs.create(n);if(!o.isRenderable)continue;if(!a&&r.popupRef){const h=i.get(r.popupRef);h?h.push(o):i.set(r.popupRef,[o])}const l=o.render();r.hidden&&(l.style.visibility="hidden"),await this.#r(l,r.id,n.elements),o._isEditable&&(this.#s.set(o.data.id,o),this._annotationEditorUIManager?.renderAnnotationElement(o))}this.#n()}async addLinkAnnotations(t,e){const s={data:null,layer:this.div,linkService:e,svgFactory:new Me,parent:this};for(const i of t){i.borderStyle||=ws._defaultBorderStyle,s.data=i;const n=qs.create(s);if(!n.isRenderable)continue;const r=n.render();await this.#r(r,i.id,null)}}update({viewport:t}){const e=this.div;this.viewport=t,Ut(e,{rotation:t.rotation}),this.#n(),e.hidden=!1}#n(){if(!this.#e)return;const t=this.div;for(const[e,s]of this.#e){const i=t.querySelector(`[data-annotation-id="${e}"]`);if(!i)continue;s.className="annotationContent";const{firstChild:n}=i;n?n.nodeName==="CANVAS"?n.replaceWith(s):n.classList.contains("annotationContent")?n.after(s):n.before(s):i.append(s);const r=this.#s.get(e);r&&(r._hasNoCanvas?(this._annotationEditorUIManager?.setMissingCanvas(e,i.id,s),r._hasNoCanvas=!1):r.canvas=s)}this.#e.clear()}getEditableAnnotations(){return Array.from(this.#s.values())}getEditableAnnotation(t){return this.#s.get(t)}static get _defaultBorderStyle(){return N(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:Xt.SOLID,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}const Ce=/\r\n?|\n/g;class Z extends k{#t;#e="";#s=`${this.id}-editor`;#i=null;#r;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=Z.prototype,e=n=>n.isEmpty(),s=Ot.TRANSLATE_SMALL,i=Ot.TRANSLATE_BIG;return N(this,"_keyboardManager",new be([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}]]))}static _type="freetext";static _editorType=D.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#t=t.color||Z._defaultColor||k._defaultLineColor,this.#r=t.fontSize||Z._defaultFontSize,this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-freetext-added-alert")}static initialize(t,e){k.initialize(t,e);const s=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(s.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case O.FREETEXT_SIZE:Z._defaultFontSize=e;break;case O.FREETEXT_COLOR:Z._defaultColor=e;break}}updateParams(t,e){switch(t){case O.FREETEXT_SIZE:this.#n(e);break;case O.FREETEXT_COLOR:this.#o(e);break}}static get defaultPropertiesToUpdate(){return[[O.FREETEXT_SIZE,Z._defaultFontSize],[O.FREETEXT_COLOR,Z._defaultColor||k._defaultLineColor]]}get propertiesToUpdate(){return[[O.FREETEXT_SIZE,this.#r],[O.FREETEXT_COLOR,this.#t]]}#n(t){const e=i=>{this.editorDiv.style.fontSize=`calc(${i}px * var(--total-scale-factor))`,this.translate(0,-(i-this.#r)*this.parentScale),this.#r=i,this.#d()},s=this.#r;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:O.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#o(t){const e=i=>{this.#t=this.editorDiv.style.color=i},s=this.#t;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:O.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-Z._internalPadding*t,-(Z._internalPadding+this.#r)*t]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(!super.enableEditMode())return!1;this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.#i=new AbortController;const t=this._uiManager.combinedSignal(this.#i);return this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t}),!0}disableEditMode(){return super.disableEditMode()?(this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#s),this._isDraggable=!0,this.#i?.abort(),this.#i=null,this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"),!0):!1}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(t){this.width||(this.enableEditMode(),t&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#a(){const t=[];this.editorDiv.normalize();let e=null;for(const s of this.editorDiv.childNodes)e?.nodeType===Node.TEXT_NODE&&s.nodeName==="BR"||(t.push(Z.#l(s)),e=s);return t.join(`
`)}#d(){const[t,e]=this.parentDimensions;let s;if(this.isAttachedToDOM)s=this.div.getBoundingClientRect();else{const{currentLayer:i,div:n}=this,r=n.style.display,a=n.classList.contains("hidden");n.classList.remove("hidden"),n.style.display="hidden",i.div.append(this.div),s=n.getBoundingClientRect(),n.remove(),n.style.display=r,n.classList.toggle("hidden",a)}this.rotation%180===this.parentRotation%180?(this.width=s.width/t,this.height=s.height/e):(this.width=s.height/t,this.height=s.width/e),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=this.#e,e=this.#e=this.#a().trimEnd();if(t===e)return;const s=i=>{if(this.#e=i,!i){this.remove();return}this.#u(),this._uiManager.rebuild(this),this.#d()};this.addCommands({cmd:()=>{s(e)},undo:()=>{s(t)},mustExec:!1}),this.#d()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}keydown(t){t.target===this.div&&t.key==="Enter"&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){Z._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}get canChangeContent(){return!0}render(){if(this.div)return this.div;let t,e;(this._isCopy||this.annotationElementId)&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#s),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:s}=this.editorDiv;if(s.fontSize=`calc(${this.#r}px * var(--total-scale-factor))`,s.color=this.#t,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),this._isCopy||this.annotationElementId){const[i,n]=this.parentDimensions;if(this.annotationElementId){const{position:r}=this._initialData;let[a,o]=this.getInitialTranslation();[a,o]=this.pageTranslationToScreen(a,o);const[l,h]=this.pageDimensions,[d,u]=this.pageTranslation;let f,g;switch(this.rotation){case 0:f=t+(r[0]-d)/l,g=e+this.height-(r[1]-u)/h;break;case 90:f=t+(r[0]-d)/l,g=e-(r[1]-u)/h,[a,o]=[o,-a];break;case 180:f=t-this.width+(r[0]-d)/l,g=e-(r[1]-u)/h,[a,o]=[-a,-o];break;case 270:f=t+(r[0]-d-this.height*h)/l,g=e+(r[1]-u-this.width*l)/h,[a,o]=[-o,a];break}this.setAt(f*i,g*n,a,o)}else this._moveAfterPaste(t,e);this.#u(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static#l(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Ce,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:s}=e;if(s.length===1&&s[0]==="text/plain")return;t.preventDefault();const i=Z.#f(e.getData("text")||"").replaceAll(Ce,`
`);if(!i)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize(),n.deleteFromDocument();const r=n.getRangeAt(0);if(!i.includes(`
`)){r.insertNode(document.createTextNode(i)),this.editorDiv.normalize(),n.collapseToStart();return}const{startContainer:a,startOffset:o}=r,l=[],h=[];if(a.nodeType===Node.TEXT_NODE){const f=a.parentElement;if(h.push(a.nodeValue.slice(o).replaceAll(Ce,"")),f!==this.editorDiv){let g=l;for(const p of this.editorDiv.childNodes){if(p===f){g=h;continue}g.push(Z.#l(p))}}l.push(a.nodeValue.slice(0,o).replaceAll(Ce,""))}else if(a===this.editorDiv){let f=l,g=0;for(const p of this.editorDiv.childNodes)g++===o&&(f=h),f.push(Z.#l(p))}this.#e=`${l.join(`
`)}${i}${h.join(`
`)}`,this.#u();const d=new Range;let u=Math.sumPrecise(l.map(f=>f.length));for(const{firstChild:f}of this.editorDiv.childNodes)if(f.nodeType===Node.TEXT_NODE){const g=f.nodeValue.length;if(u<=g){d.setStart(f,u),d.setEnd(f,u);break}u-=g}n.removeAllRanges(),n.addRange(d)}#u(){if(this.editorDiv.replaceChildren(),!!this.#e)for(const t of this.#e.split(`
`)){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#c(){return this.#e.replaceAll(" "," ")}static#f(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,s){let i=null;if(t instanceof _i){const{data:{defaultAppearanceData:{fontSize:r,fontColor:a},rect:o,rotation:l,id:h,popupRef:d},textContent:u,textPosition:f,parent:{page:{pageNumber:g}}}=t;if(!u||u.length===0)return null;i=t={annotationType:D.FREETEXT,color:Array.from(a),fontSize:r,value:u.join(`
`),position:f,pageIndex:g-1,rect:o.slice(0),rotation:l,annotationElementId:h,id:h,deleted:!1,popupRef:d}}const n=await super.deserialize(t,e,s);return n.#r=t.fontSize,n.#t=R.makeHexColor(...t.color),n.#e=Z.#f(t.value),n._initialData=i,n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=Z._internalPadding*this.parentScale,s=this.getRect(e,e),i=k._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#t),n={annotationType:D.FREETEXT,color:i,fontSize:this.#r,value:this.#c(),pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(n.isCopy=!0,n):this.annotationElementId&&!this.#p(n)?null:(n.id=this.annotationElementId,n)}#p(t){const{value:e,fontSize:s,color:i,pageIndex:n}=this._initialData;return this._hasBeenMoved||t.value!==e||t.fontSize!==s||t.color.some((r,a)=>r!==i[a])||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:s}=e;s.fontSize=`calc(${this.#r}px * var(--total-scale-factor))`,s.color=this.#t,e.replaceChildren();for(const n of this.#e.split(`
`)){const r=document.createElement("div");r.append(n?document.createTextNode(n):document.createElement("br")),e.append(r)}const i=Z._internalPadding*this.parentScale;return t.updateEdited({rect:this.getRect(i,i),popupContent:this.#e}),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class x{static PRECISION=1e-4;toSVGPath(){$("Abstract method `toSVGPath` must be implemented.")}get box(){$("Abstract getter `box` must be implemented.")}serialize(t,e){$("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2)r[a]=e+t[a]*i,r[a+1]=s+t[a+1]*n;return r}static _rescaleAndSwap(t,e,s,i,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2)r[a]=e+t[a+1]*i,r[a+1]=s+t[a]*n;return r}static _translate(t,e,s,i){i||=new Float32Array(t.length);for(let n=0,r=t.length;n<r;n+=2)i[n]=e+t[n],i[n+1]=s+t[n+1];return i}static svgRound(t){return Math.round(t*1e4)}static _normalizePoint(t,e,s,i,n){switch(n){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,n,r){return[(t+5*s)/6,(e+5*i)/6,(5*s+n)/6,(5*i+r)/6,(s+n)/2,(i+r)/2]}}class Ht{#t;#e=[];#s;#i;#r=[];#n=new Float32Array(18);#o;#a;#d;#l;#u;#c;#f=[];static#p=8;static#g=2;static#h=Ht.#p+Ht.#g;constructor({x:t,y:e},s,i,n,r,a=0){this.#t=s,this.#c=n*i,this.#i=r,this.#n.set([NaN,NaN,NaN,NaN,t,e],6),this.#s=a,this.#l=Ht.#p*i,this.#d=Ht.#h*i,this.#u=i,this.#f.push(t,e)}isEmpty(){return isNaN(this.#n[8])}#m(){const t=this.#n.subarray(4,6),e=this.#n.subarray(16,18),[s,i,n,r]=this.#t;return[(this.#o+(t[0]-e[0])/2-s)/n,(this.#a+(t[1]-e[1])/2-i)/r,(this.#o+(e[0]-t[0])/2-s)/n,(this.#a+(e[1]-t[1])/2-i)/r]}add({x:t,y:e}){this.#o=t,this.#a=e;const[s,i,n,r]=this.#t;let[a,o,l,h]=this.#n.subarray(8,12);const d=t-l,u=e-h,f=Math.hypot(d,u);if(f<this.#d)return!1;const g=f-this.#l,p=g/f,b=p*d,m=p*u;let v=a,A=o;a=l,o=h,l+=b,h+=m,this.#f?.push(t,e);const w=-m/g,y=b/g,_=w*this.#c,S=y*this.#c;return this.#n.set(this.#n.subarray(2,8),0),this.#n.set([l+_,h+S],4),this.#n.set(this.#n.subarray(14,18),12),this.#n.set([l-_,h-S],16),isNaN(this.#n[6])?(this.#r.length===0&&(this.#n.set([a+_,o+S],2),this.#r.push(NaN,NaN,NaN,NaN,(a+_-s)/n,(o+S-i)/r),this.#n.set([a-_,o-S],14),this.#e.push(NaN,NaN,NaN,NaN,(a-_-s)/n,(o-S-i)/r)),this.#n.set([v,A,a,o,l,h],6),!this.isEmpty()):(this.#n.set([v,A,a,o,l,h],6),Math.abs(Math.atan2(A-o,v-a)-Math.atan2(m,b))<Math.PI/2?([a,o,l,h]=this.#n.subarray(2,6),this.#r.push(NaN,NaN,NaN,NaN,((a+l)/2-s)/n,((o+h)/2-i)/r),[a,o,v,A]=this.#n.subarray(14,18),this.#e.push(NaN,NaN,NaN,NaN,((v+a)/2-s)/n,((A+o)/2-i)/r),!0):([v,A,a,o,l,h]=this.#n.subarray(0,6),this.#r.push(((v+5*a)/6-s)/n,((A+5*o)/6-i)/r,((5*a+l)/6-s)/n,((5*o+h)/6-i)/r,((a+l)/2-s)/n,((o+h)/2-i)/r),[l,h,a,o,v,A]=this.#n.subarray(12,18),this.#e.push(((v+5*a)/6-s)/n,((A+5*o)/6-i)/r,((5*a+l)/6-s)/n,((5*o+h)/6-i)/r,((a+l)/2-s)/n,((o+h)/2-i)/r),!0))}toSVGPath(){if(this.isEmpty())return"";const t=this.#r,e=this.#e;if(isNaN(this.#n[6])&&!this.isEmpty())return this.#b();const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);this.#w(s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return this.#A(s),s.join(" ")}#b(){const[t,e,s,i]=this.#t,[n,r,a,o]=this.#m();return`M${(this.#n[2]-t)/s} ${(this.#n[3]-e)/i} L${(this.#n[4]-t)/s} ${(this.#n[5]-e)/i} L${n} ${r} L${a} ${o} L${(this.#n[16]-t)/s} ${(this.#n[17]-e)/i} L${(this.#n[14]-t)/s} ${(this.#n[15]-e)/i} Z`}#A(t){const e=this.#e;t.push(`L${e[4]} ${e[5]} Z`)}#w(t){const[e,s,i,n]=this.#t,r=this.#n.subarray(4,6),a=this.#n.subarray(16,18),[o,l,h,d]=this.#m();t.push(`L${(r[0]-e)/i} ${(r[1]-s)/n} L${o} ${l} L${h} ${d} L${(a[0]-e)/i} ${(a[1]-s)/n}`)}newFreeDrawOutline(t,e,s,i,n,r){return new xi(t,e,s,i,n,r)}getOutlines(){const t=this.#r,e=this.#e,s=this.#n,[i,n,r,a]=this.#t,o=new Float32Array((this.#f?.length??0)+2);for(let d=0,u=o.length-2;d<u;d+=2)o[d]=(this.#f[d]-i)/r,o[d+1]=(this.#f[d+1]-n)/a;if(o[o.length-2]=(this.#o-i)/r,o[o.length-1]=(this.#a-n)/a,isNaN(s[6])&&!this.isEmpty())return this.#v(o);const l=new Float32Array(this.#r.length+24+this.#e.length);let h=t.length;for(let d=0;d<h;d+=2){if(isNaN(t[d])){l[d]=l[d+1]=NaN;continue}l[d]=t[d],l[d+1]=t[d+1]}h=this.#C(l,h);for(let d=e.length-6;d>=6;d-=6)for(let u=0;u<6;u+=2){if(isNaN(e[d+u])){l[h]=l[h+1]=NaN,h+=2;continue}l[h]=e[d+u],l[h+1]=e[d+u+1],h+=2}return this.#_(l,h),this.newFreeDrawOutline(l,o,this.#t,this.#u,this.#s,this.#i)}#v(t){const e=this.#n,[s,i,n,r]=this.#t,[a,o,l,h]=this.#m(),d=new Float32Array(36);return d.set([NaN,NaN,NaN,NaN,(e[2]-s)/n,(e[3]-i)/r,NaN,NaN,NaN,NaN,(e[4]-s)/n,(e[5]-i)/r,NaN,NaN,NaN,NaN,a,o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,(e[16]-s)/n,(e[17]-i)/r,NaN,NaN,NaN,NaN,(e[14]-s)/n,(e[15]-i)/r],0),this.newFreeDrawOutline(d,t,this.#t,this.#u,this.#s,this.#i)}#_(t,e){const s=this.#e;return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+=6}#C(t,e){const s=this.#n.subarray(4,6),i=this.#n.subarray(16,18),[n,r,a,o]=this.#t,[l,h,d,u]=this.#m();return t.set([NaN,NaN,NaN,NaN,(s[0]-n)/a,(s[1]-r)/o,NaN,NaN,NaN,NaN,l,h,NaN,NaN,NaN,NaN,d,u,NaN,NaN,NaN,NaN,(i[0]-n)/a,(i[1]-r)/o],e),e+=24}}class xi extends x{#t;#e=new Float32Array(4);#s;#i;#r;#n;#o;constructor(t,e,s,i,n,r){super(),this.#o=t,this.#r=e,this.#t=s,this.#n=i,this.#s=n,this.#i=r,this.lastPoint=[NaN,NaN],this.#a(r);const[a,o,l,h]=this.#e;for(let d=0,u=t.length;d<u;d+=2)t[d]=(t[d]-a)/l,t[d+1]=(t[d+1]-o)/h;for(let d=0,u=e.length;d<u;d+=2)e[d]=(e[d]-a)/l,e[d+1]=(e[d+1]-o)/h}toSVGPath(){const t=[`M${this.#o[4]} ${this.#o[5]}`];for(let e=6,s=this.#o.length;e<s;e+=6){if(isNaN(this.#o[e])){t.push(`L${this.#o[e+4]} ${this.#o[e+5]}`);continue}t.push(`C${this.#o[e]} ${this.#o[e+1]} ${this.#o[e+2]} ${this.#o[e+3]} ${this.#o[e+4]} ${this.#o[e+5]}`)}return t.push("Z"),t.join(" ")}serialize([t,e,s,i],n){const r=s-t,a=i-e;let o,l;switch(n){case 0:o=x._rescale(this.#o,t,i,r,-a),l=x._rescale(this.#r,t,i,r,-a);break;case 90:o=x._rescaleAndSwap(this.#o,t,e,r,a),l=x._rescaleAndSwap(this.#r,t,e,r,a);break;case 180:o=x._rescale(this.#o,s,e,-r,a),l=x._rescale(this.#r,s,e,-r,a);break;case 270:o=x._rescaleAndSwap(this.#o,s,i,-r,-a),l=x._rescaleAndSwap(this.#r,s,i,-r,-a);break}return{outline:Array.from(o),points:[Array.from(l)]}}#a(t){const e=this.#o;let s=e[4],i=e[5];const n=[s,i,s,i];let r=s,a=i;const o=t?Math.max:Math.min;for(let h=6,d=e.length;h<d;h+=6){const u=e[h+4],f=e[h+5];if(isNaN(e[h]))R.pointBoundingBox(u,f,n),a<f?(r=u,a=f):a===f&&(r=o(r,u));else{const g=[1/0,1/0,-1/0,-1/0];R.bezierBoundingBox(s,i,...e.slice(h,h+6),g),R.rectBoundingBox(...g,n),a<g[3]?(r=g[2],a=g[3]):a===g[3]&&(r=o(r,g[2]))}s=u,i=f}const l=this.#e;l[0]=n[0]-this.#s,l[1]=n[1]-this.#s,l[2]=n[2]-n[0]+2*this.#s,l[3]=n[3]-n[1]+2*this.#s,this.lastPoint=[r,a]}get box(){return this.#e}newOutliner(t,e,s,i,n,r=0){return new Ht(t,e,s,i,n,r)}getNewOutline(t,e){const[s,i,n,r]=this.#e,[a,o,l,h]=this.#t,d=n*l,u=r*h,f=s*l+a,g=i*h+o,p=this.newOutliner({x:this.#r[0]*d+f,y:this.#r[1]*u+g},this.#t,this.#n,t,this.#i,e??this.#s);for(let b=2;b<this.#r.length;b+=2)p.add({x:this.#r[b]*d+f,y:this.#r[b+1]*u+g});return p.getOutlines()}}class rs{#t;#e;#s=[];#i=[];constructor(t,e=0,s=0,i=!0){const n=[1/0,1/0,-1/0,-1/0],r=10**-4;for(const{x:f,y:g,width:p,height:b}of t){const m=Math.floor((f-e)/r)*r,v=Math.ceil((f+p+e)/r)*r,A=Math.floor((g-e)/r)*r,w=Math.ceil((g+b+e)/r)*r,y=[m,A,w,!0],_=[v,A,w,!1];this.#s.push(y,_),R.rectBoundingBox(m,A,v,w,n)}const a=n[2]-n[0]+2*s,o=n[3]-n[1]+2*s,l=n[0]-s,h=n[1]-s,d=this.#s.at(i?-1:-2),u=[d[0],d[2]];for(const f of this.#s){const[g,p,b]=f;f[0]=(g-l)/a,f[1]=(p-h)/o,f[2]=(b-h)/o}this.#t=new Float32Array([l,h,a,o]),this.#e=u}getOutlines(){this.#s.sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of this.#s)e[3]?(t.push(...this.#d(e)),this.#o(e)):(this.#a(e),t.push(...this.#d(e)));return this.#r(t)}#r(t){const e=[],s=new Set;for(const r of t){const[a,o,l]=r;e.push([a,o,r],[a,l,r])}e.sort((r,a)=>r[1]-a[1]||r[0]-a[0]);for(let r=0,a=e.length;r<a;r+=2){const o=e[r][2],l=e[r+1][2];o.push(l),l.push(o),s.add(o),s.add(l)}const i=[];let n;for(;s.size>0;){const r=s.values().next().value;let[a,o,l,h,d]=r;s.delete(r);let u=a,f=o;for(n=[a,l],i.push(n);;){let g;if(s.has(h))g=h;else if(s.has(d))g=d;else break;s.delete(g),[a,o,l,h,d]=g,u!==a&&(n.push(u,f,a,f===o?o:l),u=a),f=f===o?l:o}n.push(u,f)}return new yr(i,this.#t,this.#e)}#n(t){const e=this.#i;let s=0,i=e.length-1;for(;s<=i;){const n=s+i>>1,r=e[n][0];if(r===t)return n;r<t?s=n+1:i=n-1}return i+1}#o([,t,e]){const s=this.#n(t);this.#i.splice(s,0,[t,e])}#a([,t,e]){const s=this.#n(t);for(let i=s;i<this.#i.length;i++){const[n,r]=this.#i[i];if(n!==t)break;if(n===t&&r===e){this.#i.splice(i,1);return}}for(let i=s-1;i>=0;i--){const[n,r]=this.#i[i];if(n!==t)break;if(n===t&&r===e){this.#i.splice(i,1);return}}}#d(t){const[e,s,i]=t,n=[[e,s,i]],r=this.#n(i);for(let a=0;a<r;a++){const[o,l]=this.#i[a];for(let h=0,d=n.length;h<d;h++){const[,u,f]=n[h];if(!(l<=u||f<=o)){if(u>=o){if(f>l)n[h][1]=l;else{if(d===1)return[];n.splice(h,1),h--,d--}continue}n[h][2]=o,f>l&&n.push([e,l,f])}}}return n}}class yr extends x{#t;#e;constructor(t,e,s){super(),this.#e=t,this.#t=e,this.lastPoint=s}toSVGPath(){const t=[];for(const e of this.#e){let[s,i]=e;t.push(`M${s} ${i}`);for(let n=2;n<e.length;n+=2){const r=e[n],a=e[n+1];r===s?(t.push(`V${a}`),i=a):a===i&&(t.push(`H${r}`),s=r)}t.push("Z")}return t.join(" ")}serialize([t,e,s,i],n){const r=[],a=s-t,o=i-e;for(const l of this.#e){const h=new Array(l.length);for(let d=0;d<l.length;d+=2)h[d]=t+l[d]*a,h[d+1]=i-l[d+1]*o;r.push(h)}return r}get box(){return this.#t}get classNamesForOutlining(){return["highlightOutline"]}}class as extends Ht{newFreeDrawOutline(t,e,s,i,n,r){return new wr(t,e,s,i,n,r)}}class wr extends xi{newOutliner(t,e,s,i,n,r=0){return new as(t,e,s,i,n,r)}}class bt{#t=null;#e=null;#s;#i=null;#r=!1;#n=!1;#o=null;#a;#d=null;#l=null;#u;static#c=null;static get _keyboardManager(){return N(this,"_keyboardManager",new be([[["Escape","mac+Escape"],bt.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],bt.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],bt.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],bt.prototype._moveToPrevious],[["Home","mac+Home"],bt.prototype._moveToBeginning],[["End","mac+End"],bt.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#n=!1,this.#u=O.HIGHLIGHT_COLOR,this.#o=t):(this.#n=!0,this.#u=O.HIGHLIGHT_DEFAULT_COLOR),this.#l=t?._uiManager||e,this.#a=this.#l._eventBus,this.#s=t?.color?.toUpperCase()||this.#l?.highlightColors.values().next().value||"#FFFF98",bt.#c||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#t=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.ariaHasPopup="true",this.#o&&(t.ariaControls=`${this.#o.id}_colorpicker_dropdown`);const e=this.#l._signal;t.addEventListener("click",this.#h.bind(this),{signal:e}),t.addEventListener("keydown",this.#g.bind(this),{signal:e});const s=this.#e=document.createElement("span");return s.className="swatch",s.ariaHidden="true",s.style.backgroundColor=this.#s,t.append(s),t}renderMainDropdown(){const t=this.#i=this.#f();return t.ariaOrientation="horizontal",t.ariaLabelledBy="highlightColorPickerLabel",t}#f(){const t=document.createElement("div"),e=this.#l._signal;t.addEventListener("contextmenu",yt,{signal:e}),t.className="dropdown",t.role="listbox",t.ariaMultiSelectable="false",t.ariaOrientation="vertical",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown"),this.#o&&(t.id=`${this.#o.id}_colorpicker_dropdown`);for(const[s,i]of this.#l.highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",i),n.title=s,n.setAttribute("data-l10n-id",bt.#c[s]);const r=document.createElement("span");n.append(r),r.className="swatch",r.style.backgroundColor=i,n.ariaSelected=i===this.#s,n.addEventListener("click",this.#p.bind(this,i),{signal:e}),t.append(n)}return t.addEventListener("keydown",this.#g.bind(this),{signal:e}),t}#p(t,e){e.stopPropagation(),this.#a.dispatch("switchannotationeditorparams",{source:this,type:this.#u,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#t){this.#h(t);return}const e=t.target.getAttribute("data-color");e&&this.#p(e,t)}_moveToNext(t){if(!this.#b){this.#h(t);return}if(t.target===this.#t){this.#i.firstChild?.focus();return}t.target.nextSibling?.focus()}_moveToPrevious(t){if(t.target===this.#i?.firstChild||t.target===this.#t){this.#b&&this._hideDropdownFromKeyboard();return}this.#b||this.#h(t),t.target.previousSibling?.focus()}_moveToBeginning(t){if(!this.#b){this.#h(t);return}this.#i.firstChild?.focus()}_moveToEnd(t){if(!this.#b){this.#h(t);return}this.#i.lastChild?.focus()}#g(t){bt._keyboardManager.exec(this,t)}#h(t){if(this.#b){this.hideDropdown();return}if(this.#r=t.detail===0,this.#d||(this.#d=new AbortController,window.addEventListener("pointerdown",this.#m.bind(this),{signal:this.#l.combinedSignal(this.#d)})),this.#t.ariaExpanded="true",this.#i){this.#i.classList.remove("hidden");return}const e=this.#i=this.#f();this.#t.append(e)}#m(t){this.#i?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#i?.classList.add("hidden"),this.#t.ariaExpanded="false",this.#d?.abort(),this.#d=null}get#b(){return this.#i&&!this.#i.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#n){if(!this.#b){this.#o?.unselect();return}this.hideDropdown(),this.#t.focus({preventScroll:!0,focusVisible:this.#r})}}updateColor(t){if(this.#e&&(this.#e.style.backgroundColor=t),!this.#i)return;const e=this.#l.highlightColors.values();for(const s of this.#i.children)s.ariaSelected=e.next().value===t.toUpperCase()}destroy(){this.#t?.remove(),this.#t=null,this.#e=null,this.#i?.remove(),this.#i=null}}class q extends k{#t=null;#e=0;#s;#i=null;#r=null;#n=null;#o=null;#a=0;#d=null;#l=null;#u=null;#c=!1;#f=null;#p;#g=null;#h="";#m;#b="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=D.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=q.prototype;return N(this,"_keyboardManager",new be([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||q._defaultColor,this.#m=t.thickness||q._defaultThickness,this.#p=t.opacity||q._defaultOpacity,this.#s=t.boxes||null,this.#b=t.methodOfCreation||"",this.#h=t.text||"",this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",t.highlightId>-1?(this.#c=!0,this.#w(t),this.#R()):this.#s&&(this.#t=t.anchorNode,this.#e=t.anchorOffset,this.#o=t.focusNode,this.#a=t.focusOffset,this.#A(),this.#R(),this.rotate(this.rotation)),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-highlight-added-alert")}get telemetryInitialData(){return{action:"added",type:this.#c?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#m,methodOfCreation:this.#b}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#A(){const t=new rs(this.#s,.001);this.#l=t.getOutlines(),[this.x,this.y,this.width,this.height]=this.#l.box;const e=new rs(this.#s,.0025,.001,this._uiManager.direction==="ltr");this.#n=e.getOutlines();const{lastPoint:s}=this.#n;this.#f=[(s[0]-this.x)/this.width,(s[1]-this.y)/this.height]}#w({highlightOutlines:t,highlightId:e,clipPathId:s}){this.#l=t;const i=1.5;if(this.#n=t.getNewOutline(this.#m/2+i,.0025),e>=0)this.#u=e,this.#i=s,this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}}),this.#g=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#n.box,path:{d:this.#n.toSVGPath()}},!0);else if(this.parent){const h=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#u,{bbox:q.#S(this.#l.box,(h-this.rotation+360)%360),path:{d:t.toSVGPath()}}),this.parent.drawLayer.updateProperties(this.#g,{bbox:q.#S(this.#n.box,h),path:{d:this.#n.toSVGPath()}})}const[n,r,a,o]=t.box;switch(this.rotation){case 0:this.x=n,this.y=r,this.width=a,this.height=o;break;case 90:{const[h,d]=this.parentDimensions;this.x=r,this.y=1-n,this.width=a*d/h,this.height=o*h/d;break}case 180:this.x=1-n,this.y=1-r,this.width=a,this.height=o;break;case 270:{const[h,d]=this.parentDimensions;this.x=1-r,this.y=n,this.width=a*d/h,this.height=o*h/d;break}}const{lastPoint:l}=this.#n;this.#f=[(l[0]-n)/a,(l[1]-r)/o]}static initialize(t,e){k.initialize(t,e),q._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case O.HIGHLIGHT_DEFAULT_COLOR:q._defaultColor=e;break;case O.HIGHLIGHT_THICKNESS:q._defaultThickness=e;break}}translateInPage(t,e){}get toolbarPosition(){return this.#f}updateParams(t,e){switch(t){case O.HIGHLIGHT_COLOR:this.#v(e);break;case O.HIGHLIGHT_THICKNESS:this.#_(e);break}}static get defaultPropertiesToUpdate(){return[[O.HIGHLIGHT_DEFAULT_COLOR,q._defaultColor],[O.HIGHLIGHT_THICKNESS,q._defaultThickness]]}get propertiesToUpdate(){return[[O.HIGHLIGHT_COLOR,this.color||q._defaultColor],[O.HIGHLIGHT_THICKNESS,this.#m||q._defaultThickness],[O.HIGHLIGHT_FREE,this.#c]]}#v(t){const e=(n,r)=>{this.color=n,this.#p=r,this.parent?.drawLayer.updateProperties(this.#u,{root:{fill:n,"fill-opacity":r}}),this.#r?.updateColor(n)},s=this.color,i=this.#p;this.addCommands({cmd:e.bind(this,t,q._defaultOpacity),undo:e.bind(this,s,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:O.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#_(t){const e=this.#m,s=i=>{this.#m=i,this.#C(i)};this.addCommands({cmd:s.bind(this,t),undo:s.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:O.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}get toolbarButtons(){return this._uiManager.highlightColors?[["colorPicker",this.#r=new bt({editor:this})]]:super.toolbarButtons}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#k())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#k())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),t&&this.div.focus()}remove(){this.#T(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.#R(),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?this.#T():t&&(this.#R(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#C(t){if(!this.#c)return;this.#w({highlightOutlines:this.#l.getNewOutline(t/2)}),this.fixAndSetPosition();const[e,s]=this.parentDimensions;this.setDims(this.width*e,this.height*s)}#T(){this.#u===null||!this.parent||(this.parent.drawLayer.remove(this.#u),this.#u=null,this.parent.drawLayer.remove(this.#g),this.#g=null)}#R(t=this.parent){this.#u===null&&({id:this.#u,clipPathId:this.#i}=t.drawLayer.draw({bbox:this.#l.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#p},rootClass:{highlight:!0,free:this.#c},path:{d:this.#l.toSVGPath()}},!1,!0),this.#g=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#c},bbox:this.#n.box,path:{d:this.#n.toSVGPath()}},this.#c),this.#d&&(this.#d.style.clipPath=this.#i))}static#S([t,e,s,i],n){switch(n){case 90:return[1-e-i,t,i,s];case 180:return[1-t-s,1-e-i,s,i];case 270:return[e,1-t-s,i,s]}return[t,e,s,i]}rotate(t){const{drawLayer:e}=this.parent;let s;this.#c?(t=(t-this.rotation+360)%360,s=q.#S(this.#l.box,t)):s=q.#S([this.x,this.y,this.width,this.height],t),e.updateProperties(this.#u,{bbox:s,root:{"data-main-rotation":t}}),e.updateProperties(this.#g,{bbox:q.#S(this.#n.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();this.#h&&(t.setAttribute("aria-label",this.#h),t.setAttribute("role","mark")),this.#c?t.classList.add("free"):this.div.addEventListener("keydown",this.#P.bind(this),{signal:this._uiManager._signal});const e=this.#d=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#i;const[s,i]=this.parentDimensions;return this.setDims(this.width*s,this.height*i),ei(this,this.#d,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#g,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#g,{rootClass:{hovered:!1}})}#P(t){q._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#x(!0);break;case 1:case 3:this.#x(!1);break}}#x(t){if(!this.#t)return;const e=window.getSelection();t?e.setPosition(this.#t,this.#e):e.setPosition(this.#o,this.#a)}select(){super.select(),this.#g&&this.parent?.drawLayer.updateProperties(this.#g,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),this.#g&&(this.parent?.drawLayer.updateProperties(this.#g,{rootClass:{selected:!1}}),this.#c||this.#x(!1))}get _mustFixPosition(){return!this.#c}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.updateProperties(this.#u,{rootClass:{hidden:!t}}),this.parent.drawLayer.updateProperties(this.#g,{rootClass:{hidden:!t}}))}#k(){return this.#c?this.rotation:0}#M(){if(this.#c)return null;const[t,e]=this.pageDimensions,[s,i]=this.pageTranslation,n=this.#s,r=new Float32Array(n.length*8);let a=0;for(const{x:o,y:l,width:h,height:d}of n){const u=o*t+s,f=(1-l)*e+i;r[a]=r[a+4]=u,r[a+1]=r[a+3]=f,r[a+2]=r[a+6]=u+h*t,r[a+5]=r[a+7]=f-d*e,a+=8}return r}#L(t){return this.#l.serialize(t,this.#k())}static startHighlighting(t,e,{target:s,x:i,y:n}){const{x:r,y:a,width:o,height:l}=s.getBoundingClientRect(),h=new AbortController,d=t.combinedSignal(h),u=f=>{h.abort(),this.#E(t,f)};window.addEventListener("blur",u,{signal:d}),window.addEventListener("pointerup",u,{signal:d}),window.addEventListener("pointerdown",Q,{capture:!0,passive:!1,signal:d}),window.addEventListener("contextmenu",yt,{signal:d}),s.addEventListener("pointermove",this.#$.bind(this,t),{signal:d}),this._freeHighlight=new as({x:i,y:n},[r,a,o,l],t.scale,this._defaultThickness/2,e,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static#$(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#E(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static async deserialize(t,e,s){let i=null;if(t instanceof Ei){const{data:{quadPoints:g,rect:p,rotation:b,id:m,color:v,opacity:A,popupRef:w},parent:{page:{pageNumber:y}}}=t;i=t={annotationType:D.HIGHLIGHT,color:Array.from(v),opacity:A,quadPoints:g,boxes:null,pageIndex:y-1,rect:p.slice(0),rotation:b,annotationElementId:m,id:m,deleted:!1,popupRef:w}}else if(t instanceof ys){const{data:{inkLists:g,rect:p,rotation:b,id:m,color:v,borderStyle:{rawWidth:A},popupRef:w},parent:{page:{pageNumber:y}}}=t;i=t={annotationType:D.HIGHLIGHT,color:Array.from(v),thickness:A,inkLists:g,boxes:null,pageIndex:y-1,rect:p.slice(0),rotation:b,annotationElementId:m,id:m,deleted:!1,popupRef:w}}const{color:n,quadPoints:r,inkLists:a,opacity:o}=t,l=await super.deserialize(t,e,s);l.color=R.makeHexColor(...n),l.#p=o||1,a&&(l.#m=t.thickness),l._initialData=i;const[h,d]=l.pageDimensions,[u,f]=l.pageTranslation;if(r){const g=l.#s=[];for(let p=0;p<r.length;p+=8)g.push({x:(r[p]-u)/h,y:1-(r[p+1]-f)/d,width:(r[p+2]-r[p])/h,height:(r[p+1]-r[p+5])/d});l.#A(),l.#R(),l.rotate(l.rotation)}else if(a){l.#c=!0;const g=a[0],p={x:g[0]-u,y:d-(g[1]-f)},b=new as(p,[0,0,h,d],1,l.#m/2,!0,.001);for(let A=0,w=g.length;A<w;A+=2)p.x=g[A]-u,p.y=d-(g[A+1]-f),b.add(p);const{id:m,clipPathId:v}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:l.color,"fill-opacity":l._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:b.toSVGPath()}},!0,!0);l.#w({highlightOutlines:b.getOutlines(),highlightId:m,clipPathId:v}),l.#R(),l.rotate(l.parentRotation)}return l}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),s=k._colorManager.convert(this.color),i={annotationType:D.HIGHLIGHT,color:s,opacity:this.#p,thickness:this.#m,quadPoints:this.#M(),outlines:this.#L(e),pageIndex:this.pageIndex,rect:e,rotation:this.#k(),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!this.#y(i)?null:(i.id=this.annotationElementId,i)}#y(t){const{color:e}=this._initialData;return t.color.some((s,i)=>s!==e[i])}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class Ti{#t=Object.create(null);updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,s)}updateSVGProperty(t,e){this.#t[t]=e}toSVGProperties(){const t=this.#t;return this.#t=Object.create(null),{root:t}}reset(){this.#t=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){$("Not implemented")}}class L extends k{#t=null;#e;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#s=null;static#i=null;static#r=null;static#n=NaN;static#o=null;static#a=null;static#d=NaN;static _INNER_MARGIN=3;constructor(t){super(t),this.#e=t.mustBeCommitted||!1,this._addOutlines(t)}_addOutlines(t){t.drawOutlines&&(this.#l(t),this.#f())}#l({drawOutlines:t,drawId:e,drawingOptions:s}){this.#t=t,this._drawingOptions||=s,this.annotationElementId||this._uiManager.a11yAlert(`pdfjs-editor-${this.editorType}-added-alert`),e>=0?(this._drawId=e,this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)):this._drawId=this.#u(t,this.parent),this.#h(t.box)}#u(t,e){const{id:s}=e.drawLayer.draw(L._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return s}static _mergeSVGProperties(t,e){const s=new Set(Object.keys(t));for(const[i,n]of Object.entries(e))s.has(i)?Object.assign(t[i],n):t[i]=n;return t}static getDefaultDrawingOptions(t){$("Not implemented")}static get typesMap(){$("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const s=this.typesMap.get(t);s&&this._defaultDrawingOptions.updateProperty(s,e),this._currentParent&&(L.#s.updateProperty(s,e),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(t,e){const s=this.constructor.typesMap.get(t);s&&this._updateProperty(t,s,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[s,i]of this.typesMap)t.push([s,e[i]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[s,i]of this.constructor.typesMap)t.push([s,e[i]]);return t}_updateProperty(t,e,s){const i=this._drawingOptions,n=i[e],r=a=>{i.updateProperty(e,a);const o=this.#t.updateProperty(e,a);o&&this.#h(o),this.parent?.drawLayer.updateProperties(this._drawId,i.toSVGProperties())};this.addCommands({cmd:r.bind(this,s),undo:r.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,L._mergeSVGProperties(this.#t.getPathResizingSVGProperties(this.#g()),{bbox:this.#m()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,L._mergeSVGProperties(this.#t.getPathResizedSVGProperties(this.#g()),{bbox:this.#m()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#m()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,L._mergeSVGProperties(this.#t.getPathTranslatedSVGProperties(this.#g(),this.parentDimensions),{bbox:this.#m()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,this.#e&&(this.#e=!1,this.commit(),this.parent.setSelected(this),t&&this.isOnScreen&&this.div.focus())}remove(){this.#c(),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.#f(),this.#h(this.#t.box),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?(this._uiManager.removeShouldRescale(this),this.#c()):t&&(this._uiManager.addShouldRescale(this),this.#f(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),e&&this.select()}#c(){this._drawId===null||!this.parent||(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())}#f(t=this.parent){if(!(this._drawId!==null&&this.parent===t)){if(this._drawId!==null){this.parent.drawLayer.updateParent(this._drawId,t.drawLayer);return}this._drawingOptions.updateAll(),this._drawId=this.#u(this.#t,t)}}#p([t,e,s,i]){const{parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[e,1-t,s*(r/n),i*(n/r)];case 180:return[1-t,1-e,s,i];case 270:return[1-e,t,s*(r/n),i*(n/r)];default:return[t,e,s,i]}}#g(){const{x:t,y:e,width:s,height:i,parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[1-e,t,s*(n/r),i*(r/n)];case 180:return[1-t,1-e,s,i];case 270:return[e,1-t,s*(n/r),i*(r/n)];default:return[t,e,s,i]}}#h(t){if([this.x,this.y,this.width,this.height]=this.#p(t),this.div){this.fixAndSetPosition();const[e,s]=this.parentDimensions;this.setDims(this.width*e,this.height*s)}this._onResized()}#m(){const{x:t,y:e,width:s,height:i,rotation:n,parentRotation:r,parentDimensions:[a,o]}=this;switch((n*4+r)/90){case 1:return[1-e-i,t,i,s];case 2:return[1-t-s,1-e-i,s,i];case 3:return[e,1-t-s,i,s];case 4:return[t,e-s*(a/o),i*(o/a),s*(a/o)];case 5:return[1-e,t,s*(a/o),i*(o/a)];case 6:return[1-t-i*(o/a),1-e,i*(o/a),s*(a/o)];case 7:return[e-s*(a/o),1-t-i*(o/a),s*(a/o),i*(o/a)];case 8:return[t-s,e-i,s,i];case 9:return[1-e,t-s,i,s];case 10:return[1-t,1-e,s,i];case 11:return[e-i,1-t,i,s];case 12:return[t-i*(o/a),e,i*(o/a),s*(a/o)];case 13:return[1-e-s*(a/o),t-i*(o/a),s*(a/o),i*(o/a)];case 14:return[1-t,1-e-s*(a/o),i*(o/a),s*(a/o)];case 15:return[e,1-t,s*(a/o),i*(o/a)];default:return[t,e,s,i]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,L._mergeSVGProperties({bbox:this.#m()},this.#t.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#h(this.#t.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;this._isCopy&&(t=this.x,e=this.y);const s=super.render();s.classList.add("draw");const i=document.createElement("div");s.append(i),i.setAttribute("aria-hidden","true"),i.className="internal";const[n,r]=this.parentDimensions;return this.setDims(this.width*n,this.height*r),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(t,e),s}static createDrawerInstance(t,e,s,i,n){$("Not implemented")}static startDrawing(t,e,s,i){const{target:n,offsetX:r,offsetY:a,pointerId:o,pointerType:l}=i;if(L.#o&&L.#o!==l)return;const{viewport:{rotation:h}}=t,{width:d,height:u}=n.getBoundingClientRect(),f=L.#i=new AbortController,g=t.combinedSignal(f);if(L.#n||=o,L.#o??=l,window.addEventListener("pointerup",p=>{L.#n===p.pointerId?this._endDraw(p):L.#a?.delete(p.pointerId)},{signal:g}),window.addEventListener("pointercancel",p=>{L.#n===p.pointerId?this._currentParent.endDrawingSession():L.#a?.delete(p.pointerId)},{signal:g}),window.addEventListener("pointerdown",p=>{L.#o===p.pointerType&&((L.#a||=new Set).add(p.pointerId),L.#s.isCancellable()&&(L.#s.removeLastElement(),L.#s.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:g}),window.addEventListener("contextmenu",yt,{signal:g}),n.addEventListener("pointermove",this._drawMove.bind(this),{signal:g}),n.addEventListener("touchmove",p=>{p.timeStamp===L.#d&&Q(p)},{signal:g}),t.toggleDrawing(),e._editorUndoBar?.hide(),L.#s){t.drawLayer.updateProperties(this._currentDrawId,L.#s.startNew(r,a,d,u,h));return}e.updateUIForDefaultProperties(this),L.#s=this.createDrawerInstance(r,a,d,u,h),L.#r=this.getDefaultDrawingOptions(),this._currentParent=t,{id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(L.#r.toSVGProperties(),L.#s.defaultSVGProperties),!0,!1)}static _drawMove(t){if(L.#d=-1,!L.#s)return;const{offsetX:e,offsetY:s,pointerId:i}=t;if(L.#n===i){if(L.#a?.size>=1){this._endDraw(t);return}this._currentParent.drawLayer.updateProperties(this._currentDrawId,L.#s.add(e,s)),L.#d=t.timeStamp,Q(t)}}static _cleanup(t){t&&(this._currentDrawId=-1,this._currentParent=null,L.#s=null,L.#r=null,L.#o=null,L.#d=NaN),L.#i&&(L.#i.abort(),L.#i=null,L.#n=NaN,L.#a=null)}static _endDraw(t){const e=this._currentParent;if(e){if(e.toggleDrawing(!0),this._cleanup(!1),t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,L.#s.end(t.offsetX,t.offsetY)),this.supportMultipleDrawings){const s=L.#s,i=this._currentDrawId,n=s.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,s.setLastElement(n))},undo:()=>{e.drawLayer.updateProperties(i,s.removeLastElement())},mustExec:!1,type:O.DRAW_STEP});return}this.endDrawing(!1)}}static endDrawing(t){const e=this._currentParent;if(!e)return null;if(e.toggleDrawing(!0),e.cleanUndoStack(O.DRAW_STEP),!L.#s.isEmpty()){const{pageDimensions:[s,i],scale:n}=e,r=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:L.#s.getOutlines(s*n,i*n,n,this._INNER_MARGIN),drawingOptions:L.#r,mustBeCommitted:!t});return this._cleanup(!0),r}return e.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(t){}static deserializeDraw(t,e,s,i,n,r){$("Not implemented")}static async deserialize(t,e,s){const{rawDims:{pageWidth:i,pageHeight:n,pageX:r,pageY:a}}=e.viewport,o=this.deserializeDraw(r,a,i,n,this._INNER_MARGIN,t),l=await super.deserialize(t,e,s);return l.createDrawingOptions(t),l.#l({drawOutlines:o}),l.#f(),l.onScaleChanging(),l.rotate(),l}serializeDraw(t){const[e,s]=this.pageTranslation,[i,n]=this.pageDimensions;return this.#t.serialize([e,s,i,n],t)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class vr{#t=new Float64Array(6);#e;#s;#i;#r;#n;#o="";#a=0;#d=new ye;#l;#u;constructor(t,e,s,i,n,r){this.#l=s,this.#u=i,this.#i=n,this.#r=r,[t,e]=this.#c(t,e);const a=this.#e=[NaN,NaN,NaN,NaN,t,e];this.#n=[t,e],this.#s=[{line:a,points:this.#n}],this.#t.set(a,0)}updateProperty(t,e){t==="stroke-width"&&(this.#r=e)}#c(t,e){return x._normalizePoint(t,e,this.#l,this.#u,this.#i)}isEmpty(){return!this.#s||this.#s.length===0}isCancellable(){return this.#n.length<=10}add(t,e){[t,e]=this.#c(t,e);const[s,i,n,r]=this.#t.subarray(2,6),a=t-n,o=e-r;return Math.hypot(this.#l*a,this.#u*o)<=2?null:(this.#n.push(t,e),isNaN(s)?(this.#t.set([n,r,t,e],2),this.#e.push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(this.#t[0])&&this.#e.splice(6,6),this.#t.set([s,i,n,r,t,e],0),this.#e.push(...x.createBezierPoints(s,i,n,r,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const s=this.add(t,e);return s||(this.#n.length===2?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,n){this.#l=s,this.#u=i,this.#i=n,[t,e]=this.#c(t,e);const r=this.#e=[NaN,NaN,NaN,NaN,t,e];this.#n=[t,e];const a=this.#s.at(-1);return a&&(a.line=new Float32Array(a.line),a.points=new Float32Array(a.points)),this.#s.push({line:r,points:this.#n}),this.#t.set(r,0),this.#a=0,this.toSVGPath(),null}getLastElement(){return this.#s.at(-1)}setLastElement(t){return this.#s?(this.#s.push(t),this.#e=t.line,this.#n=t.points,this.#a=0,{path:{d:this.toSVGPath()}}):this.#d.setLastElement(t)}removeLastElement(){if(!this.#s)return this.#d.removeLastElement();this.#s.pop(),this.#o="";for(let t=0,e=this.#s.length;t<e;t++){const{line:s,points:i}=this.#s[t];this.#e=s,this.#n=i,this.#a=0,this.toSVGPath()}return{path:{d:this.#o}}}toSVGPath(){const t=x.svgRound(this.#e[4]),e=x.svgRound(this.#e[5]);if(this.#n.length===2)return this.#o=`${this.#o} M ${t} ${e} Z`,this.#o;if(this.#n.length<=6){const i=this.#o.lastIndexOf("M");this.#o=`${this.#o.slice(0,i)} M ${t} ${e}`,this.#a=6}if(this.#n.length===4){const i=x.svgRound(this.#e[10]),n=x.svgRound(this.#e[11]);return this.#o=`${this.#o} L ${i} ${n}`,this.#a=12,this.#o}const s=[];this.#a===0&&(s.push(`M ${t} ${e}`),this.#a=6);for(let i=this.#a,n=this.#e.length;i<n;i+=6){const[r,a,o,l,h,d]=this.#e.slice(i,i+6).map(x.svgRound);s.push(`C${r} ${a} ${o} ${l} ${h} ${d}`)}return this.#o+=s.join(" "),this.#a=this.#e.length,this.#o}getOutlines(t,e,s,i){const n=this.#s.at(-1);return n.line=new Float32Array(n.line),n.points=new Float32Array(n.points),this.#d.build(this.#s,t,e,s,this.#i,this.#r,i),this.#t=null,this.#e=null,this.#s=null,this.#o=null,this.#d}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class ye extends x{#t;#e=0;#s;#i;#r;#n;#o;#a;#d;build(t,e,s,i,n,r,a){this.#r=e,this.#n=s,this.#o=i,this.#a=n,this.#d=r,this.#s=a??0,this.#i=t,this.#c()}get thickness(){return this.#d}setLastElement(t){return this.#i.push(t),{path:{d:this.toSVGPath()}}}removeLastElement(){return this.#i.pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#i){if(t.push(`M${x.svgRound(e[4])} ${x.svgRound(e[5])}`),e.length===6){t.push("Z");continue}if(e.length===12&&isNaN(e[6])){t.push(`L${x.svgRound(e[10])} ${x.svgRound(e[11])}`);continue}for(let s=6,i=e.length;s<i;s+=6){const[n,r,a,o,l,h]=e.subarray(s,s+6).map(x.svgRound);t.push(`C${n} ${r} ${a} ${o} ${l} ${h}`)}}return t.join("")}serialize([t,e,s,i],n){const r=[],a=[],[o,l,h,d]=this.#u();let u,f,g,p,b,m,v,A,w;switch(this.#a){case 0:w=x._rescale,u=t,f=e+i,g=s,p=-i,b=t+o*s,m=e+(1-l-d)*i,v=t+(o+h)*s,A=e+(1-l)*i;break;case 90:w=x._rescaleAndSwap,u=t,f=e,g=s,p=i,b=t+l*s,m=e+o*i,v=t+(l+d)*s,A=e+(o+h)*i;break;case 180:w=x._rescale,u=t+s,f=e,g=-s,p=i,b=t+(1-o-h)*s,m=e+l*i,v=t+(1-o)*s,A=e+(l+d)*i;break;case 270:w=x._rescaleAndSwap,u=t+s,f=e+i,g=-s,p=-i,b=t+(1-l-d)*s,m=e+(1-o-h)*i,v=t+(1-l)*s,A=e+(1-o)*i;break}for(const{line:y,points:_}of this.#i)r.push(w(y,u,f,g,p,n?new Array(y.length):null)),a.push(w(_,u,f,g,p,n?new Array(_.length):null));return{lines:r,points:a,rect:[b,m,v,A]}}static deserialize(t,e,s,i,n,{paths:{lines:r,points:a},rotation:o,thickness:l}){const h=[];let d,u,f,g,p;switch(o){case 0:p=x._rescale,d=-t/s,u=e/i+1,f=1/s,g=-1/i;break;case 90:p=x._rescaleAndSwap,d=-e/i,u=-t/s,f=1/i,g=1/s;break;case 180:p=x._rescale,d=t/s+1,u=-e/i,f=-1/s,g=1/i;break;case 270:p=x._rescaleAndSwap,d=e/i+1,u=t/s+1,f=-1/i,g=-1/s;break}if(!r){r=[];for(const m of a){const v=m.length;if(v===2){r.push(new Float32Array([NaN,NaN,NaN,NaN,m[0],m[1]]));continue}if(v===4){r.push(new Float32Array([NaN,NaN,NaN,NaN,m[0],m[1],NaN,NaN,NaN,NaN,m[2],m[3]]));continue}const A=new Float32Array(3*(v-2));r.push(A);let[w,y,_,S]=m.subarray(0,4);A.set([NaN,NaN,NaN,NaN,w,y],0);for(let E=4;E<v;E+=2){const C=m[E],M=m[E+1];A.set(x.createBezierPoints(w,y,_,S,C,M),(E-2)*3),[w,y,_,S]=[_,S,C,M]}}}for(let m=0,v=r.length;m<v;m++)h.push({line:p(r[m].map(A=>A??NaN),d,u,f,g),points:p(a[m].map(A=>A??NaN),d,u,f,g)});const b=new this.prototype.constructor;return b.build(h,s,i,1,o,l,n),b}#l(t=this.#d){const e=this.#s+t/2*this.#o;return this.#a%180===0?[e/this.#r,e/this.#n]:[e/this.#n,e/this.#r]}#u(){const[t,e,s,i]=this.#t,[n,r]=this.#l(0);return[t+n,e+r,s-2*n,i-2*r]}#c(){const t=this.#t=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:i}of this.#i){if(i.length<=12){for(let a=4,o=i.length;a<o;a+=6)R.pointBoundingBox(i[a],i[a+1],t);continue}let n=i[4],r=i[5];for(let a=6,o=i.length;a<o;a+=6){const[l,h,d,u,f,g]=i.subarray(a,a+6);R.bezierBoundingBox(n,r,l,h,d,u,f,g,t),n=f,r=g}}const[e,s]=this.#l();t[0]=lt(t[0]-e,0,1),t[1]=lt(t[1]-s,0,1),t[2]=lt(t[2]+e,0,1),t[3]=lt(t[3]+s,0,1),t[2]-=t[0],t[3]-=t[1]}get box(){return this.#t}updateProperty(t,e){return t==="stroke-width"?this.#f(e):null}#f(t){const[e,s]=this.#l();this.#d=t;const[i,n]=this.#l(),[r,a]=[i-e,n-s],o=this.#t;return o[0]-=r,o[1]-=a,o[2]+=2*r,o[3]+=2*a,o}updateParentDimensions([t,e],s){const[i,n]=this.#l();this.#r=t,this.#n=e,this.#o=s;const[r,a]=this.#l(),o=r-i,l=a-n,h=this.#t;return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h}updateRotation(t){return this.#e=t,{path:{transform:this.rotationTransform}}}get viewBox(){return this.#t.map(x.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#t;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${x.svgRound(t)} ${x.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#t;let s=0,i=0,n=0,r=0,a=0,o=0;switch(this.#e){case 90:i=e/t,n=-t/e,a=t;break;case 180:s=-1,r=-1,a=t,o=e;break;case 270:i=-e/t,n=t/e,o=e;break;default:return""}return`matrix(${s} ${i} ${n} ${r} ${x.svgRound(a)} ${x.svgRound(o)})`}getPathResizingSVGProperties([t,e,s,i]){const[n,r]=this.#l(),[a,o,l,h]=this.#t;if(Math.abs(l-n)<=x.PRECISION||Math.abs(h-r)<=x.PRECISION){const p=t+s/2-(a+l/2),b=e+i/2-(o+h/2);return{path:{"transform-origin":`${x.svgRound(t)} ${x.svgRound(e)}`,transform:`${this.rotationTransform} translate(${p} ${b})`}}}const d=(s-2*n)/(l-2*n),u=(i-2*r)/(h-2*r),f=l/s,g=h/i;return{path:{"transform-origin":`${x.svgRound(a)} ${x.svgRound(o)}`,transform:`${this.rotationTransform} scale(${f} ${g}) translate(${x.svgRound(n)} ${x.svgRound(r)}) scale(${d} ${u}) translate(${x.svgRound(-n)} ${x.svgRound(-r)})`}}}getPathResizedSVGProperties([t,e,s,i]){const[n,r]=this.#l(),a=this.#t,[o,l,h,d]=a;if(a[0]=t,a[1]=e,a[2]=s,a[3]=i,Math.abs(h-n)<=x.PRECISION||Math.abs(d-r)<=x.PRECISION){const b=t+s/2-(o+h/2),m=e+i/2-(l+d/2);for(const{line:v,points:A}of this.#i)x._translate(v,b,m,v),x._translate(A,b,m,A);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${x.svgRound(t)} ${x.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const u=(s-2*n)/(h-2*n),f=(i-2*r)/(d-2*r),g=-u*(o+n)+t+n,p=-f*(l+r)+e+r;if(u!==1||f!==1||g!==0||p!==0)for(const{line:b,points:m}of this.#i)x._rescale(b,g,p,u,f,b),x._rescale(m,g,p,u,f,m);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${x.svgRound(t)} ${x.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],s){const[i,n]=s,r=this.#t,a=t-r[0],o=e-r[1];if(this.#r===i&&this.#n===n)for(const{line:l,points:h}of this.#i)x._translate(l,a,o,l),x._translate(h,a,o,h);else{const l=this.#r/i,h=this.#n/n;this.#r=i,this.#n=n;for(const{line:d,points:u}of this.#i)x._rescale(d,a,o,l,h,d),x._rescale(u,a,o,l,h,u);r[2]*=l,r[3]*=h}return r[0]=t,r[1]=e,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${x.svgRound(t)} ${x.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#t;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${x.svgRound(t[0])} ${x.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class $e extends Ti{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:k._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){t==="stroke-width"&&(e??=this["stroke-width"],e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new $e(this._viewParameters);return t.updateAll(this),t}}class vs extends L{static _type="ink";static _editorType=D.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"}),this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){k.initialize(t,e),this._defaultDrawingOptions=new $e(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!0}static get typesMap(){return N(this,"typesMap",new Map([[O.INK_THICKNESS,"stroke-width"],[O.INK_COLOR,"stroke"],[O.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,s,i,n){return new vr(t,e,s,i,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,s,i,n,r){return ye.deserialize(t,e,s,i,n,r)}static async deserialize(t,e,s){let i=null;if(t instanceof ys){const{data:{inkLists:r,rect:a,rotation:o,id:l,color:h,opacity:d,borderStyle:{rawWidth:u},popupRef:f},parent:{page:{pageNumber:g}}}=t;i=t={annotationType:D.INK,color:Array.from(h),thickness:u,opacity:d,paths:{points:r},boxes:null,pageIndex:g-1,rect:a.slice(0),rotation:o,annotationElementId:l,id:l,deleted:!1,popupRef:f}}const n=await super.deserialize(t,e,s);return n._initialData=i,n}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:s}=this;e.updateSVGProperty("stroke-width"),s.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;t&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:t,thickness:e,opacity:s}){this._drawingOptions=vs.getDefaultDrawingOptions({stroke:R.makeHexColor(...t),"stroke-width":e,"stroke-opacity":s})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:s,rect:i}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":r,"stroke-width":a}}=this,o={annotationType:D.INK,color:k._colorManager.convert(n),opacity:r,thickness:a,paths:{lines:e,points:s},pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(o.isCopy=!0,o):this.annotationElementId&&!this.#t(o)?null:(o.id=this.annotationElementId,o)}#t(t){const{color:e,thickness:s,opacity:i,pageIndex:n}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||t.color.some((r,a)=>r!==e[a])||t.thickness!==s||t.opacity!==i||t.pageIndex!==n}renderAnnotationElement(t){const{points:e,rect:s}=this.serializeDraw(!1);return t.updateEdited({rect:s,thickness:this._drawingOptions["stroke-width"],points:e}),null}}class os extends ye{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}const xe=8,ae=3;class Kt{static#t={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static#e(t,e,s,i){return s-=t,i-=e,s===0?i>0?0:4:s===1?i+6:2-i}static#s=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static#i(t,e,s,i,n,r,a){const o=this.#e(s,i,n,r);for(let l=0;l<8;l++){const h=(-l+o-a+16)%8,d=this.#s[2*h],u=this.#s[2*h+1];if(t[(s+d)*e+(i+u)]!==0)return h}return-1}static#r(t,e,s,i,n,r,a){const o=this.#e(s,i,n,r);for(let l=0;l<8;l++){const h=(l+o+a+16)%8,d=this.#s[2*h],u=this.#s[2*h+1];if(t[(s+d)*e+(i+u)]!==0)return h}return-1}static#n(t,e,s,i){const n=t.length,r=new Int32Array(n);for(let h=0;h<n;h++)r[h]=t[h]<=i?1:0;for(let h=1;h<s-1;h++)r[h*e]=r[h*e+e-1]=0;for(let h=0;h<e;h++)r[h]=r[e*s-1-h]=0;let a=1,o;const l=[];for(let h=1;h<s-1;h++){o=1;for(let d=1;d<e-1;d++){const u=h*e+d,f=r[u];if(f===0)continue;let g=h,p=d;if(f===1&&r[u-1]===0)a+=1,p-=1;else if(f>=1&&r[u+1]===0)a+=1,p+=1,f>1&&(o=f);else{f!==1&&(o=Math.abs(f));continue}const b=[d,h],m=p===d+1,v={isHole:m,points:b,id:a,parent:0};l.push(v);let A;for(const I of l)if(I.id===o){A=I;break}A?A.isHole?v.parent=m?A.parent:o:v.parent=m?o:A.parent:v.parent=m?o:0;const w=this.#i(r,e,h,d,g,p,0);if(w===-1){r[u]=-a,r[u]!==1&&(o=Math.abs(r[u]));continue}let y=this.#s[2*w],_=this.#s[2*w+1];const S=h+y,E=d+_;g=S,p=E;let C=h,M=d;for(;;){const I=this.#r(r,e,C,M,g,p,1);y=this.#s[2*I],_=this.#s[2*I+1];const H=C+y,G=M+_;b.push(G,H);const B=C*e+M;if(r[B+1]===0?r[B]=-a:r[B]===1&&(r[B]=a),H===h&&G===d&&C===S&&M===E){r[u]!==1&&(o=Math.abs(r[u]));break}else g=C,p=M,C=H,M=G}}}return l}static#o(t,e,s,i){if(s-e<=4){for(let S=e;S<s-2;S+=2)i.push(t[S],t[S+1]);return}const n=t[e],r=t[e+1],a=t[s-4]-n,o=t[s-3]-r,l=Math.hypot(a,o),h=a/l,d=o/l,u=h*r-d*n,f=o/a,g=1/l,p=Math.atan(f),b=Math.cos(p),m=Math.sin(p),v=g*(Math.abs(b)+Math.abs(m)),A=g*(1-v+v**2),w=Math.max(Math.atan(Math.abs(m+b)*A),Math.atan(Math.abs(m-b)*A));let y=0,_=e;for(let S=e+2;S<s-2;S+=2){const E=Math.abs(u-h*t[S+1]+d*t[S]);E>y&&(_=S,y=E)}y>(l*w)**2?(this.#o(t,e,_+2,i),this.#o(t,_,s,i)):i.push(n,r)}static#a(t){const e=[],s=t.length;return this.#o(t,0,s,e),e.push(t[s-2],t[s-1]),e.length<=4?null:e}static#d(t,e,s,i,n,r){const a=new Float32Array(r**2),o=-2*i**2,l=r>>1;for(let p=0;p<r;p++){const b=(p-l)**2;for(let m=0;m<r;m++)a[p*r+m]=Math.exp((b+(m-l)**2)/o)}const h=new Float32Array(256),d=-2*n**2;for(let p=0;p<256;p++)h[p]=Math.exp(p**2/d);const u=t.length,f=new Uint8Array(u),g=new Uint32Array(256);for(let p=0;p<s;p++)for(let b=0;b<e;b++){const m=p*e+b,v=t[m];let A=0,w=0;for(let _=0;_<r;_++){const S=p+_-l;if(!(S<0||S>=s))for(let E=0;E<r;E++){const C=b+E-l;if(C<0||C>=e)continue;const M=t[S*e+C],I=a[_*r+E]*h[Math.abs(M-v)];A+=M*I,w+=I}}const y=f[m]=Math.round(A/w);g[y]++}return[f,g]}static#l(t){const e=new Uint32Array(256);for(const s of t)e[s]++;return e}static#u(t){const e=t.length,s=new Uint8ClampedArray(e>>2);let i=-1/0,n=1/0;for(let a=0,o=s.length;a<o;a++){if(t[(a<<2)+3]===0){i=s[a]=255;continue}const h=s[a]=t[a<<2];h>i&&(i=h),h<n&&(n=h)}const r=255/(i-n);for(let a=0;a<e;a++)s[a]=(s[a]-n)*r;return s}static#c(t){let e,s=-1/0,i=-1/0;const n=t.findIndex(o=>o!==0);let r=n,a=n;for(e=n;e<256;e++){const o=t[e];o>s&&(e-r>i&&(i=e-r,a=e-1),s=o,r=e)}for(e=a-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static#f(t){const e=t,{width:s,height:i}=t,{maxDim:n}=this.#t;let r=s,a=i;if(s>n||i>n){let u=s,f=i,g=Math.log2(Math.max(s,i)/n);const p=Math.floor(g);g=g===p?p-1:p;for(let m=0;m<g;m++){r=Math.ceil(u/2),a=Math.ceil(f/2);const v=new OffscreenCanvas(r,a);v.getContext("2d").drawImage(t,0,0,u,f,0,0,r,a),u=r,f=a,t!==e&&t.close(),t=v.transferToImageBitmap()}const b=Math.min(n/r,n/a);r=Math.round(r*b),a=Math.round(a*b)}const l=new OffscreenCanvas(r,a).getContext("2d",{willReadFrequently:!0});l.filter="grayscale(1)",l.drawImage(t,0,0,t.width,t.height,0,0,r,a);const h=l.getImageData(0,0,r,a).data;return[this.#u(h),r,a]}static extractContoursFromText(t,{fontFamily:e,fontStyle:s,fontWeight:i},n,r,a,o){let l=new OffscreenCanvas(1,1),h=l.getContext("2d",{alpha:!1});const d=200,u=h.font=`${s} ${i} ${d}px ${e}`,{actualBoundingBoxLeft:f,actualBoundingBoxRight:g,actualBoundingBoxAscent:p,actualBoundingBoxDescent:b,fontBoundingBoxAscent:m,fontBoundingBoxDescent:v,width:A}=h.measureText(t),w=1.5,y=Math.ceil(Math.max(Math.abs(f)+Math.abs(g)||0,A)*w),_=Math.ceil(Math.max(Math.abs(p)+Math.abs(b)||d,Math.abs(m)+Math.abs(v)||d)*w);l=new OffscreenCanvas(y,_),h=l.getContext("2d",{alpha:!0,willReadFrequently:!0}),h.font=u,h.filter="grayscale(1)",h.fillStyle="white",h.fillRect(0,0,y,_),h.fillStyle="black",h.fillText(t,y*(w-1)/2,_*(3-w)/2);const S=this.#u(h.getImageData(0,0,y,_).data),E=this.#l(S),C=this.#c(E),M=this.#n(S,y,_,C);return this.processDrawnLines({lines:{curves:M,width:y,height:_},pageWidth:n,pageHeight:r,rotation:a,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,s,i,n){const[r,a,o]=this.#f(t),[l,h]=this.#d(r,a,o,Math.hypot(a,o)*this.#t.sigmaSFactor,this.#t.sigmaR,this.#t.kernelSize),d=this.#c(h),u=this.#n(l,a,o,d);return this.processDrawnLines({lines:{curves:u,width:a,height:o},pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:r,areContours:a}){i%180!==0&&([e,s]=[s,e]);const{curves:o,width:l,height:h}=t,d=t.thickness??0,u=[],f=Math.min(e/l,s/h),g=f/e,p=f/s,b=[];for(const{points:v}of o){const A=r?this.#a(v):v;if(!A)continue;b.push(A);const w=A.length,y=new Float32Array(w),_=new Float32Array(3*(w===2?2:w-2));if(u.push({line:_,points:y}),w===2){y[0]=A[0]*g,y[1]=A[1]*p,_.set([NaN,NaN,NaN,NaN,y[0],y[1]],0);continue}let[S,E,C,M]=A;S*=g,E*=p,C*=g,M*=p,y.set([S,E,C,M],0),_.set([NaN,NaN,NaN,NaN,S,E],0);for(let I=4;I<w;I+=2){const H=y[I]=A[I]*g,G=y[I+1]=A[I+1]*p;_.set(x.createBezierPoints(S,E,C,M,H,G),(I-2)*3),[S,E,C,M]=[C,M,H,G]}}if(u.length===0)return null;const m=a?new os:new ye;return m.build(u,e,s,1,i,a?0:d,n),{outline:m,newCurves:b,areContours:a,thickness:d,width:l,height:h}}static async compressSignature({outlines:t,areContours:e,thickness:s,width:i,height:n}){let r=1/0,a=-1/0,o=0;for(const A of t){o+=A.length;for(let w=2,y=A.length;w<y;w++){const _=A[w]-A[w-2];r=Math.min(r,_),a=Math.max(a,_)}}let l;r>=-128&&a<=127?l=Int8Array:r>=-32768&&a<=32767?l=Int16Array:l=Int32Array;const h=t.length,d=xe+ae*h,u=new Uint32Array(d);let f=0;u[f++]=d*Uint32Array.BYTES_PER_ELEMENT+(o-2*h)*l.BYTES_PER_ELEMENT,u[f++]=0,u[f++]=i,u[f++]=n,u[f++]=e?0:1,u[f++]=Math.max(0,Math.floor(s??0)),u[f++]=h,u[f++]=l.BYTES_PER_ELEMENT;for(const A of t)u[f++]=A.length-2,u[f++]=A[0],u[f++]=A[1];const g=new CompressionStream("deflate-raw"),p=g.writable.getWriter();await p.ready,p.write(u);const b=l.prototype.constructor;for(const A of t){const w=new b(A.length-2);for(let y=2,_=A.length;y<_;y++)w[y-2]=A[y]-A[y-2];p.write(w)}p.close();const m=await new Response(g.readable).arrayBuffer(),v=new Uint8Array(m);return Zs(v)}static async decompressSignature(t){try{const e=Ui(t),{readable:s,writable:i}=new DecompressionStream("deflate-raw"),n=i.getWriter();await n.ready,n.write(e).then(async()=>{await n.ready,await n.close()}).catch(()=>{});let r=null,a=0;for await(const A of s)r||=new Uint8Array(new Uint32Array(A.buffer,0,4)[0]),r.set(A,a),a+=A.length;const o=new Uint32Array(r.buffer,0,r.length>>2),l=o[1];if(l!==0)throw new Error(`Invalid version: ${l}`);const h=o[2],d=o[3],u=o[4]===0,f=o[5],g=o[6],p=o[7],b=[],m=(xe+ae*g)*Uint32Array.BYTES_PER_ELEMENT;let v;switch(p){case Int8Array.BYTES_PER_ELEMENT:v=new Int8Array(r.buffer,m);break;case Int16Array.BYTES_PER_ELEMENT:v=new Int16Array(r.buffer,m);break;case Int32Array.BYTES_PER_ELEMENT:v=new Int32Array(r.buffer,m);break}a=0;for(let A=0;A<g;A++){const w=o[ae*A+xe],y=new Float32Array(w+2);b.push(y);for(let _=0;_<ae-1;_++)y[_]=o[ae*A+xe+_+1];for(let _=0;_<w;_++)y[_+2]=y[_]+v[a++]}return{areContours:u,thickness:f,outlines:b,width:h,height:d}}catch(e){return F(`decompressSignature: ${e}`),null}}}class _s extends Ti{constructor(){super(),super.updateProperties({fill:k._defaultLineColor,"stroke-width":0})}clone(){const t=new _s;return t.updateAll(this),t}}class Ss extends $e{constructor(t){super(t),super.updateProperties({stroke:k._defaultLineColor,"stroke-width":1})}clone(){const t=new Ss(this._viewParameters);return t.updateAll(this),t}}class Et extends L{#t=!1;#e=null;#s=null;#i=null;static _type="signature";static _editorType=D.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"}),this._willKeepAspectRatio=!0,this.#s=t.signatureData||null,this.#e=null,this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){k.initialize(t,e),this._defaultDrawingOptions=new _s,this._defaultDrawnSignatureOptions=new Ss(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!1}static get typesMap(){return N(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#e}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){this._drawId!==null&&super.onScaleChanging()}render(){if(this.div)return this.div;let t,e;const{_isCopy:s}=this;if(s&&(this._isCopy=!1,t=this.x,e=this.y),super.render(),this._drawId===null)if(this.#s){const{lines:i,mustSmooth:n,areContours:r,description:a,uuid:o,heightInPage:l}=this.#s,{rawDims:{pageWidth:h,pageHeight:d},rotation:u}=this.parent.viewport,f=Kt.processDrawnLines({lines:i,pageWidth:h,pageHeight:d,rotation:u,innerMargin:Et._INNER_MARGIN,mustSmooth:n,areContours:r});this.addSignature(f,l,a,o)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);return s&&(this._isCopy=!0,this._moveAfterPaste(t,e)),this.div}setUuid(t){this.#i=t,this.addEditToolbar()}getUuid(){return this.#i}get description(){return this.#e}set description(t){this.#e=t,super.addEditToolbar().then(e=>{e?.updateEditSignatureButton(t)})}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:s,width:i,height:n}=this.#s,r=Math.max(i,n),a=Kt.processDrawnLines({lines:{curves:t.map(o=>({points:o})),thickness:s,width:i,height:n},pageWidth:r,pageHeight:r,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e});return{areContours:e,outline:a.outline}}get toolbarButtons(){return this._uiManager.signatureManager?[["editSignature",this._uiManager.signatureManager]]:super.toolbarButtons}addSignature(t,e,s,i){const{x:n,y:r}=this,{outline:a}=this.#s=t;this.#t=a instanceof os,this.description=s,this.div.setAttribute("data-l10n-args",JSON.stringify({description:s}));let o;this.#t?o=Et.getDefaultDrawingOptions():(o=Et._defaultDrawnSignatureOptions.clone(),o.updateProperties({"stroke-width":a.thickness})),this._addOutlines({drawOutlines:a,drawingOptions:o});const[l,h]=this.parentDimensions,[,d]=this.pageDimensions;let u=e/d;u=u>=1?.5:u,this.width*=u/this.height,this.width>=1&&(u*=.9/this.width,this.width=.9),this.height=u,this.setDims(l*this.width,h*this.height),this.x=n,this.y=r,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(i),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!i,hasDescription:!!s}}),this.div.hidden=!1}getFromImage(t){const{rawDims:{pageWidth:e,pageHeight:s},rotation:i}=this.parent.viewport;return Kt.process(t,e,s,i,Et._INNER_MARGIN)}getFromText(t,e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:n}=this.parent.viewport;return Kt.extractContoursFromText(t,e,s,i,n,Et._INNER_MARGIN)}getDrawnSignature(t){const{rawDims:{pageWidth:e,pageHeight:s},rotation:i}=this.parent.viewport;return Kt.processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:Et._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){t?this._drawingOptions=Et.getDefaultDrawingOptions():(this._drawingOptions=Et._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":e}))}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:s,rect:i}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":n}}=this,r={annotationType:D.SIGNATURE,isSignature:!0,areContours:this.#t,color:[0,0,0],thickness:this.#t?0:n,pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?(r.paths={lines:e,points:s},r.uuid=this.#i,r.isCopy=!0):r.lines=e,this.#e&&(r.accessibilityData={type:"Figure",alt:this.#e}),r}static deserializeDraw(t,e,s,i,n,r){return r.areContours?os.deserialize(t,e,s,i,n,r):ye.deserialize(t,e,s,i,n,r)}static async deserialize(t,e,s){const i=await super.deserialize(t,e,s);return i.#t=t.areContours,i.#e=t.accessibilityData?.alt||"",i.#i=t.uuid,i}}class _r extends k{#t=null;#e=null;#s=null;#i=null;#r=null;#n="";#o=null;#a=!1;#d=null;#l=!1;#u=!1;static _type="stamp";static _editorType=D.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#i=t.bitmapUrl,this.#r=t.bitmapFile,this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){k.initialize(t,e)}static isHandlingMimeForPasting(t){return ts.includes(t)}static paste(t,e){e.pasteEditor({mode:D.STAMP},{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#c(t,e=!1){if(!t){this.remove();return}this.#t=t.bitmap,e||(this.#e=t.id,this.#l=t.isSvg),t.file&&(this.#n=t.file.name),this.#g()}#f(){if(this.#s=null,this._uiManager.enableWaiting(!1),!!this.#o){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#t){this.addEditToolbar().then(()=>{this._editToolbar.hide(),this._uiManager.editAltText(this,!0)});return}if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#t){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:s}=this._uiManager;if(!s)throw new Error("No ML.");if(!await s.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:i,width:n,height:r}=t||this.copyCanvas(null,null,!0).imageData,a=await s.guess({name:"altText",request:{data:i,width:n,height:r,channels:i.length/(n*r)}});if(!a)throw new Error("No response from the AI service.");if(a.error)throw new Error("Error from the AI service.");if(a.cancel)return null;if(!a.output)throw new Error("No valid response from the AI service.");const o=a.output;return await this.setGuessedAltText(o),e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1}),o}#p(){if(this.#e){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(this.#e).then(s=>this.#c(s,!0)).finally(()=>this.#f());return}if(this.#i){const s=this.#i;this.#i=null,this._uiManager.enableWaiting(!0),this.#s=this._uiManager.imageManager.getFromUrl(s).then(i=>this.#c(i)).finally(()=>this.#f());return}if(this.#r){const s=this.#r;this.#r=null,this._uiManager.enableWaiting(!0),this.#s=this._uiManager.imageManager.getFromFile(s).then(i=>this.#c(i)).finally(()=>this.#f());return}const t=document.createElement("input");t.type="file",t.accept=ts.join(",");const e=this._uiManager._signal;this.#s=new Promise(s=>{t.addEventListener("change",async()=>{if(!t.files||t.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const i=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),this.#c(i)}s()},{signal:e}),t.addEventListener("cancel",()=>{this.remove(),s()},{signal:e})}).finally(()=>this.#f()),t.click()}remove(){this.#e&&(this.#t=null,this._uiManager.imageManager.deleteId(this.#e),this.#o?.remove(),this.#o=null,this.#d&&(clearTimeout(this.#d),this.#d=null)),super.remove()}rebuild(){if(!this.parent){this.#e&&this.#p();return}super.rebuild(),this.div!==null&&(this.#e&&this.#o===null&&this.#p(),this.isAttachedToDOM||this.parent.add(this))}onceAdded(t){this._isDraggable=!0,t&&this.div.focus()}isEmpty(){return!(this.#s||this.#t||this.#i||this.#r||this.#e||this.#a)}get toolbarButtons(){return[["altText",this.createAltText()]]}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;return this._isCopy&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.createAltText(),this.#a||(this.#t?this.#g():this.#p()),this._isCopy&&this._moveAfterPaste(t,e),this._uiManager.addShouldRescale(this),this.div}setCanvas(t,e){const{id:s,bitmap:i}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove(),s&&this._uiManager.imageManager.isValidId(s)&&(this.#e=s,i&&(this.#t=i),this.#a=!1,this.#g())}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;this.#d!==null&&clearTimeout(this.#d);const t=200;this.#d=setTimeout(()=>{this.#d=null,this.#m()},t)}#g(){const{div:t}=this;let{width:e,height:s}=this.#t;const[i,n]=this.pageDimensions,r=.75;if(this.width)e=this.width*i,s=this.height*n;else if(e>r*i||s>r*n){const h=Math.min(r*i/e,r*n/s);e*=h,s*=h}const[a,o]=this.parentDimensions;this.setDims(e*a/i,s*o/n),this._uiManager.enableWaiting(!1);const l=this.#o=document.createElement("canvas");l.setAttribute("role","img"),this.addContainer(l),this.width=e/i,this.height=s/n,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,(!this._uiManager.useNewAltTextWhenAddingImage||!this._uiManager.useNewAltTextFlow||this.annotationElementId)&&(t.hidden=!1),this.#m(),this.#u||(this.parent.addUndoableEditor(this),this.#u=!0),this._reportTelemetry({action:"inserted_image"}),this.#n&&this.div.setAttribute("aria-description",this.#n),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-stamp-added-alert")}copyCanvas(t,e,s=!1){t||(t=224);const{width:i,height:n}=this.#t,r=new xt;let a=this.#t,o=i,l=n,h=null;if(e){if(i>e||n>e){const _=Math.min(e/i,e/n);o=Math.floor(i*_),l=Math.floor(n*_)}h=document.createElement("canvas");const u=h.width=Math.ceil(o*r.sx),f=h.height=Math.ceil(l*r.sy);this.#l||(a=this.#h(u,f));const g=h.getContext("2d");g.filter=this._uiManager.hcmFilter;let p="white",b="#cfcfd8";this._uiManager.hcmFilter!=="none"?b="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(p="#8f8f9d",b="#42414d");const m=15,v=m*r.sx,A=m*r.sy,w=new OffscreenCanvas(v*2,A*2),y=w.getContext("2d");y.fillStyle=p,y.fillRect(0,0,v*2,A*2),y.fillStyle=b,y.fillRect(0,0,v,A),y.fillRect(v,A,v,A),g.fillStyle=g.createPattern(w,"repeat"),g.fillRect(0,0,u,f),g.drawImage(a,0,0,a.width,a.height,0,0,u,f)}let d=null;if(s){let u,f;if(r.symmetric&&a.width<t&&a.height<t)u=a.width,f=a.height;else if(a=this.#t,i>t||n>t){const b=Math.min(t/i,t/n);u=Math.floor(i*b),f=Math.floor(n*b),this.#l||(a=this.#h(u,f))}const p=new OffscreenCanvas(u,f).getContext("2d",{willReadFrequently:!0});p.drawImage(a,0,0,a.width,a.height,0,0,u,f),d={width:u,height:f,data:p.getImageData(0,0,u,f).data}}return{canvas:h,width:o,height:l,imageData:d}}#h(t,e){const{width:s,height:i}=this.#t;let n=s,r=i,a=this.#t;for(;n>2*t||r>2*e;){const o=n,l=r;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2)),r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const h=new OffscreenCanvas(n,r);h.getContext("2d").drawImage(a,0,0,o,l,0,0,n,r),a=h.transferToImageBitmap()}return a}#m(){const[t,e]=this.parentDimensions,{width:s,height:i}=this,n=new xt,r=Math.ceil(s*t*n.sx),a=Math.ceil(i*e*n.sy),o=this.#o;if(!o||o.width===r&&o.height===a)return;o.width=r,o.height=a;const l=this.#l?this.#t:this.#h(r,a),h=o.getContext("2d");h.filter=this._uiManager.hcmFilter,h.drawImage(l,0,0,l.width,l.height,0,0,r,a)}#b(t){if(t){if(this.#l){const i=this._uiManager.imageManager.getSvgUrl(this.#e);if(i)return i}const e=document.createElement("canvas");return{width:e.width,height:e.height}=this.#t,e.getContext("2d").drawImage(this.#t,0,0),e.toDataURL()}if(this.#l){const[e,s]=this.pageDimensions,i=Math.round(this.width*e*ee.PDF_TO_CSS_UNITS),n=Math.round(this.height*s*ee.PDF_TO_CSS_UNITS),r=new OffscreenCanvas(i,n);return r.getContext("2d").drawImage(this.#t,0,0,this.#t.width,this.#t.height,0,0,i,n),r.transferToImageBitmap()}return structuredClone(this.#t)}static async deserialize(t,e,s){let i=null,n=!1;if(t instanceof Ci){const{data:{rect:p,rotation:b,id:m,structParent:v,popupRef:A},container:w,parent:{page:{pageNumber:y}},canvas:_}=t;let S,E;_?(delete t.canvas,{id:S,bitmap:E}=s.imageManager.getFromCanvas(w.id,_),_.remove()):(n=!0,t._hasNoCanvas=!0);const C=(await e._structTree.getAriaAttributes(`${ls}${m}`))?.get("aria-label")||"";i=t={annotationType:D.STAMP,bitmapId:S,bitmap:E,pageIndex:y-1,rect:p.slice(0),rotation:b,annotationElementId:m,id:m,deleted:!1,accessibilityData:{decorative:!1,altText:C},isSvg:!1,structParent:v,popupRef:A}}const r=await super.deserialize(t,e,s),{rect:a,bitmap:o,bitmapUrl:l,bitmapId:h,isSvg:d,accessibilityData:u}=t;n?(s.addMissingCanvas(t.id,r),r.#a=!0):h&&s.imageManager.isValidId(h)?(r.#e=h,o&&(r.#t=o)):r.#i=l,r.#l=d;const[f,g]=r.pageDimensions;return r.width=(a[2]-a[0])/f,r.height=(a[3]-a[1])/g,u&&(r.altTextData=u),r._initialData=i,r.#u=!!i,r}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s={annotationType:D.STAMP,bitmapId:this.#e,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#l,structTreeParentId:this._structTreeParentId};if(t)return s.bitmapUrl=this.#b(!0),s.accessibilityData=this.serializeAltText(!0),s.isCopy=!0,s;const{decorative:i,altText:n}=this.serializeAltText(!1);if(!i&&n&&(s.accessibilityData={type:"Figure",alt:n}),this.annotationElementId){const a=this.#A(s);if(a.isSame)return null;a.isSameAltText?delete s.accessibilityData:s.accessibilityData.structParent=this._initialData.structParent??-1}if(s.id=this.annotationElementId,e===null)return s;e.stamps||=new Map;const r=this.#l?(s.rect[2]-s.rect[0])*(s.rect[3]-s.rect[1]):null;if(!e.stamps.has(this.#e))e.stamps.set(this.#e,{area:r,serialized:s}),s.bitmap=this.#b(!1);else if(this.#l){const a=e.stamps.get(this.#e);r>a.area&&(a.area=r,a.serialized.bitmap.close(),a.serialized.bitmap=this.#b(!1))}return s}#A(t){const{pageIndex:e,accessibilityData:{altText:s}}=this._initialData,i=t.pageIndex===e,n=(t.accessibilityData?.alt||"")===s;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&i&&n,isSameAltText:n}}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}}class It{#t;#e=!1;#s=null;#i=null;#r=null;#n=new Map;#o=!1;#a=!1;#d=!1;#l=null;#u=null;#c=null;#f=null;#p=null;#g=-1;#h;static _initialized=!1;static#m=new Map([Z,vs,_r,q,Et].map(t=>[t._editorType,t]));constructor({uiManager:t,pageIndex:e,div:s,structTreeLayer:i,accessibilityManager:n,annotationLayer:r,drawLayer:a,textLayer:o,viewport:l,l10n:h}){const d=[...It.#m.values()];if(!It._initialized){It._initialized=!0;for(const u of d)u.initialize(h,t)}t.registerEditorTypes(d),this.#h=t,this.pageIndex=e,this.div=s,this.#t=n,this.#s=r,this.viewport=l,this.#c=o,this.drawLayer=a,this._structTree=i,this.#h.addLayer(this)}get isEmpty(){return this.#n.size===0}get isInvisible(){return this.isEmpty&&this.#h.getMode()===D.NONE}updateToolbar(t){this.#h.updateToolbar(t)}updateMode(t=this.#h.getMode()){switch(this.#_(),t){case D.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case D.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case D.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of It.#m.values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#c?.div}setEditingState(t){this.#h.setEditingState(t)}addCommands(t){this.#h.addCommands(t)}cleanUndoStack(t){this.#h.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#s?.div.classList.toggle("disabled",!t)}async enable(){this.#d=!0,this.div.tabIndex=0,this.togglePointerEvents(!0),this.#p?.abort(),this.#p=null;const t=new Set;for(const s of this.#n.values())s.enableEditing(),s.show(!0),s.annotationElementId&&(this.#h.removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!this.#s){this.#d=!1;return}const e=this.#s.getEditableAnnotations();for(const s of e){if(s.hide(),this.#h.isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=await this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}this.#d=!1}disable(){if(this.#a=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1),this.#c&&!this.#p){this.#p=new AbortController;const i=this.#h.combinedSignal(this.#p);this.#c.div.addEventListener("pointerdown",n=>{const{clientX:a,clientY:o,timeStamp:l}=n,h=this.#g;if(l-h>500){this.#g=l;return}this.#g=-1;const{classList:d}=this.div;d.toggle("getElements",!0);const u=document.elementsFromPoint(a,o);if(d.toggle("getElements",!1),!this.div.contains(u[0]))return;let f;const g=new RegExp(`^${Xs}[0-9]+$`);for(const b of u)if(g.test(b.id)){f=b.id;break}if(!f)return;const p=this.#n.get(f);p?.annotationElementId===null&&(n.stopPropagation(),n.preventDefault(),p.dblclick())},{signal:i,capture:!0})}const t=new Map,e=new Map;for(const i of this.#n.values())if(i.disableEditing(),!!i.annotationElementId){if(i.serialize()!==null){t.set(i.annotationElementId,i);continue}else e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()}if(this.#s){const i=this.#s.getEditableAnnotations();for(const n of i){const{id:r}=n.data;if(this.#h.isDeletedAnnotationElement(r))continue;let a=e.get(r);if(a){a.resetAnnotationElement(n),a.show(!1),n.show();continue}a=t.get(r),a&&(this.#h.addChangedExistingAnnotation(a),a.renderAnnotationElement(n)&&a.show(!1)),n.show()}}this.#_(),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const i of It.#m.values())s.remove(`${i._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#a=!1}getEditableAnnotation(t){return this.#s?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#h.getActive()!==t&&this.#h.setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,this.#c?.div&&!this.#f){this.#f=new AbortController;const t=this.#h.combinedSignal(this.#f);this.#c.div.addEventListener("pointerdown",this.#b.bind(this),{signal:t}),this.#c.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,this.#c?.div&&this.#f&&(this.#f.abort(),this.#f=null,this.#c.div.classList.remove("highlighting"))}#b(t){this.#h.unselectAll();const{target:e}=t;if(e===this.#c.div||(e.getAttribute("role")==="img"||e.classList.contains("endOfContent"))&&this.#c.div.contains(e)){const{isMac:s}=it.platform;if(t.button!==0||t.ctrlKey&&s)return;this.#h.showAllEditors("highlight",!0,!0),this.#c.div.classList.add("free"),this.toggleDrawing(),q.startHighlighting(this,this.#h.direction==="ltr",{target:this.#c.div,x:t.x,y:t.y}),this.#c.div.addEventListener("pointerup",()=>{this.#c.div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:this.#h._signal}),t.preventDefault()}}enableClick(){if(this.#i)return;this.#i=new AbortController;const t=this.#h.combinedSignal(this.#i);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#i?.abort(),this.#i=null}attach(t){this.#n.set(t.id,t);const{annotationElementId:e}=t;e&&this.#h.isDeletedAnnotationElement(e)&&this.#h.removeDeletedAnnotationElement(t)}detach(t){this.#n.delete(t.id),this.#t?.removePointerInTextLayer(t.contentDiv),!this.#a&&t.annotationElementId&&this.#h.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#h.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(this.#h.addDeletedAnnotationElement(t.annotationElementId),k.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(!(t.parent===this&&t.isAttachedToDOM)){if(this.changeParent(t),this.#h.addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!this.#d),this.#h.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!this.#r&&(t._focusEventsAllowed=!1,this.#r=setTimeout(()=>{this.#r=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this.#h._signal}),e.focus())},0)),t._structTreeParentId=this.#t?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),s=()=>{t.remove()};this.addCommands({cmd:e,undo:s,mustExec:!1})}getNextId(){return this.#h.getId()}get#A(){return It.#m.get(this.#h.getMode())}combinedSignal(t){return this.#h.combinedSignal(t)}#w(t){const e=this.#A;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#A?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.updateToolbar(t),await this.#h.updateMode(t.mode);const{offsetX:s,offsetY:i}=this.#v(),n=this.getNextId(),r=this.#w({parent:this,id:n,x:s,y:i,uiManager:this.#h,isCentered:!0,...e});r&&this.add(r)}async deserialize(t){return await It.#m.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#h)||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),n=this.#w({parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:this.#h,isCentered:e,...s});return n&&this.add(n),n}#v(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),n=Math.max(0,t),r=Math.max(0,e),a=Math.min(window.innerWidth,t+s),o=Math.min(window.innerHeight,e+i),l=(n+a)/2-t,h=(r+o)/2-e,[d,u]=this.viewport.rotation%180===0?[l,h]:[h,l];return{offsetX:d,offsetY:u}}addNewEditor(t={}){this.createAndAddNewEditor(this.#v(),!0,t)}setSelected(t){this.#h.setSelected(t)}toggleSelected(t){this.#h.toggleSelected(t)}unselect(t){this.#h.unselect(t)}pointerup(t){const{isMac:e}=it.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div||!this.#o||(this.#o=!1,this.#A?.isDrawer&&this.#A.supportMultipleDrawings))return;if(!this.#e){this.#e=!0;return}const s=this.#h.getMode();if(s===D.STAMP||s===D.SIGNATURE){this.#h.unselectAll();return}this.createAndAddNewEditor(t,!1)}pointerdown(t){if(this.#h.getMode()===D.HIGHLIGHT&&this.enableTextSelection(),this.#o){this.#o=!1;return}const{isMac:e}=it.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;if(this.#o=!0,this.#A?.isDrawer){this.startDrawingSession(t);return}const s=this.#h.getActive();this.#e=!s||s.isEmpty()}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),this.#l){this.#A.startDrawing(this,this.#h,!1,t);return}this.#h.setCurrentDrawingSession(this),this.#l=new AbortController;const e=this.#h.combinedSignal(this.#l);this.div.addEventListener("blur",({relatedTarget:s})=>{s&&!this.div.contains(s)&&(this.#u=null,this.commitOrRemove())},{signal:e}),this.#A.startDrawing(this,this.#h,!1,t)}pause(t){if(t){const{activeElement:e}=document;this.div.contains(e)&&(this.#u=e);return}this.#u&&setTimeout(()=>{this.#u?.focus(),this.#u=null},0)}endDrawingSession(t=!1){return this.#l?(this.#h.setCurrentDrawingSession(null),this.#l.abort(),this.#l=null,this.#u=null,this.#A.endDrawing(t)):null}findNewParent(t,e,s){const i=this.#h.findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}commitOrRemove(){return this.#l?(this.endDrawingSession(),!0):!1}onScaleChanging(){this.#l&&this.#A.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove(),this.#h.getActive()?.parent===this&&(this.#h.commitOrRemove(),this.#h.setActiveEditor(null)),this.#r&&(clearTimeout(this.#r),this.#r=null);for(const t of this.#n.values())this.#t?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#n.clear(),this.#h.removeLayer(this)}#_(){for(const t of this.#n.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t,Ut(this.div,t);for(const e of this.#h.getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){this.#h.commitOrRemove(),this.#_();const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,Ut(this.div,{rotation:s}),e!==s)for(const i of this.#n.values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#h.viewParameters.realScale}}class et{#t=null;#e=new Map;#s=new Map;static#i=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(!this.#t){this.#t=t;return}if(this.#t!==t){if(this.#e.size>0)for(const e of this.#e.values())e.remove(),t.append(e);this.#t=t}}static get _svgFactory(){return N(this,"_svgFactory",new Me)}static#r(t,[e,s,i,n]){const{style:r}=t;r.top=`${100*s}%`,r.left=`${100*e}%`,r.width=`${100*i}%`,r.height=`${100*n}%`}#n(){const t=et._svgFactory.create(1,1,!0);return this.#t.append(t),t.setAttribute("aria-hidden",!0),t}#o(t,e){const s=et._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const n=et._svgFactory.createElement("use");return s.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),i}#a(t,e){for(const[s,i]of Object.entries(e))i===null?t.removeAttribute(s):t.setAttribute(s,i)}draw(t,e=!1,s=!1){const i=et.#i++,n=this.#n(),r=et._svgFactory.createElement("defs");n.append(r);const a=et._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${i}`;a.setAttribute("id",o),a.setAttribute("vector-effect","non-scaling-stroke"),e&&this.#s.set(i,a);const l=s?this.#o(r,o):null,h=et._svgFactory.createElement("use");return n.append(h),h.setAttribute("href",`#${o}`),this.updateProperties(n,t),this.#e.set(i,n),{id:i,clipPathId:`url(#${l})`}}drawOutline(t,e){const s=et.#i++,i=this.#n(),n=et._svgFactory.createElement("defs");i.append(n);const r=et._svgFactory.createElement("path");n.append(r);const a=`path_p${this.pageIndex}_${s}`;r.setAttribute("id",a),r.setAttribute("vector-effect","non-scaling-stroke");let o;if(e){const d=et._svgFactory.createElement("mask");n.append(d),o=`mask_p${this.pageIndex}_${s}`,d.setAttribute("id",o),d.setAttribute("maskUnits","objectBoundingBox");const u=et._svgFactory.createElement("rect");d.append(u),u.setAttribute("width","1"),u.setAttribute("height","1"),u.setAttribute("fill","white");const f=et._svgFactory.createElement("use");d.append(f),f.setAttribute("href",`#${a}`),f.setAttribute("stroke","none"),f.setAttribute("fill","black"),f.setAttribute("fill-rule","nonzero"),f.classList.add("mask")}const l=et._svgFactory.createElement("use");i.append(l),l.setAttribute("href",`#${a}`),o&&l.setAttribute("mask",`url(#${o})`);const h=l.cloneNode();return i.append(h),l.classList.add("mainOutline"),h.classList.add("secondaryOutline"),this.updateProperties(i,t),this.#e.set(s,i),s}finalizeDraw(t,e){this.#s.delete(t),this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:s,bbox:i,rootClass:n,path:r}=e,a=typeof t=="number"?this.#e.get(t):t;if(a){if(s&&this.#a(a,s),i&&et.#r(a,i),n){const{classList:o}=a;for(const[l,h]of Object.entries(n))o.toggle(l,h)}if(r){const l=a.firstChild.firstChild;this.#a(l,r)}}}updateParent(t,e){if(e===this)return;const s=this.#e.get(t);s&&(e.#t.append(s),this.#e.delete(t),e.#e.set(t,s))}remove(t){this.#s.delete(t),this.#t!==null&&(this.#e.get(t).remove(),this.#e.delete(t))}destroy(){this.#t=null;for(const t of this.#e.values())t.remove();this.#e.clear(),this.#s.clear()}}globalThis._pdfjsTestingUtils={HighlightOutliner:rs};globalThis.pdfjsLib={AbortException:Nt,AnnotationEditorLayer:It,AnnotationEditorParamsType:O,AnnotationEditorType:D,AnnotationEditorUIManager:Ot,AnnotationLayer:ws,AnnotationMode:Dt,AnnotationType:K,build:tr,ColorPicker:bt,createValidAbsoluteUrl:Ys,DOMSVGFactory:Me,DrawLayer:et,FeatureTest:it,fetchData:ge,getDocument:Xn,getFilenameFromUrl:ji,getPdfFilenameFromUrl:Vi,getUuid:Js,getXfaPageViewport:qi,GlobalWorkerOptions:de,ImageKind:Te,InvalidPDFException:Ze,isDataScheme:Oe,isPdfFile:cs,isValidExplicitDest:rn,MathClamp:lt,noContextMenu:yt,normalizeUnicode:Gi,OPS:Ie,OutputScale:xt,PasswordResponses:Mi,PDFDataRangeTransport:mi,PDFDateString:ti,PDFWorker:fe,PermissionFlag:ki,PixelsPerInch:ee,RenderingCancelledException:hs,ResponseException:ke,setLayerDimensions:Ut,shadow:N,SignatureExtractor:Kt,stopEvent:Q,SupportedImageMimeTypes:ts,TextLayer:at,TouchManager:Be,updateUrlHash:Ks,Util:R,VerbosityLevel:De,version:Zn,XfaLayer:yi};export{Nt as AbortException,It as AnnotationEditorLayer,O as AnnotationEditorParamsType,D as AnnotationEditorType,Ot as AnnotationEditorUIManager,ws as AnnotationLayer,Dt as AnnotationMode,K as AnnotationType,bt as ColorPicker,Me as DOMSVGFactory,et as DrawLayer,it as FeatureTest,de as GlobalWorkerOptions,Te as ImageKind,Ze as InvalidPDFException,lt as MathClamp,Ie as OPS,xt as OutputScale,mi as PDFDataRangeTransport,ti as PDFDateString,fe as PDFWorker,Mi as PasswordResponses,ki as PermissionFlag,ee as PixelsPerInch,hs as RenderingCancelledException,ke as ResponseException,Kt as SignatureExtractor,ts as SupportedImageMimeTypes,at as TextLayer,Be as TouchManager,R as Util,De as VerbosityLevel,yi as XfaLayer,tr as build,Ys as createValidAbsoluteUrl,ge as fetchData,Xn as getDocument,ji as getFilenameFromUrl,Vi as getPdfFilenameFromUrl,Js as getUuid,qi as getXfaPageViewport,Oe as isDataScheme,cs as isPdfFile,rn as isValidExplicitDest,yt as noContextMenu,Gi as normalizeUnicode,Ut as setLayerDimensions,N as shadow,Q as stopEvent,Ks as updateUrlHash,Zn as version};
