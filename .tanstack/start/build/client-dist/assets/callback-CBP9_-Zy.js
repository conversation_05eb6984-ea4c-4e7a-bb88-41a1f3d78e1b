import{u as w,r as f,j as n}from"./main-CXx9abZr.js";import{a as E,u as x}from"./use-auth-DVLhXigO.js";import{a as N,u as U,c as A}from"./user-CphpYdS2.js";const D={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,TSS_APP_BASE:"/",TSS_OUTPUT_PUBLIC_DIR:"/Users/<USER>/Project/sinochem-agent/.output/public",TSS_SERVER_FN_BASE:"/_serverFn",TSS_SPA_MODE:"false",VITE_CALCULATE_APP_ID:"680919cbac364a80b24306137e5debeb",VITE_DASHSCOPE_API_KEY:"sk-5ff58b88af7343b7bbb388079e1442f2",VITE_DINGTALK_CLIENT_ID:"dingaalizs3sqczrirqd",VITE_DINGTALK_CLIENT_SECRET:"Z5PSpa4CO9tg9EFOp4iOJcMVURaNHydKRAw3OEZvdSqrbzvmF0XegE-6pX4fvaXX",VITE_TURSO_AUTH_TOKEN:"***************************************************************************************************************************************************************************************************************************************************************************",VITE_TURSO_DATABASE_URL:"libsql://sinochem-agent-vercel-icfg-************************.aws-ap-northeast-1.turso.io",VITE_YOUR_APP_ID:"622fbd2ef57c413baafa29527d205414",VITE_YOUR_FILE_APP_ID:"38669993697942e6a8ac1a9f1aa591e0"};async function j(T,l,a){try{const t=await fetch("/api/auth/dingtalk-callback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({clientId:l,clientSecret:a,code:T,grantType:"authorization_code"})}),u=await t.json();if(!t.ok)throw console.error("API代理调用失败:",u),new Error(u.message||`API调用失败: ${t.status} ${t.statusText}`);return u}catch(t){return console.error("调用钉钉API获取用户token失败:",t),null}}async function O(T,l="me"){try{const a=await fetch("/api/auth/dingtalk-userinfo",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({accessToken:T,unionId:l})}),t=await a.json();if(!a.ok)throw console.error("获取钉钉用户信息失败:",t),new Error(t.error||`获取用户信息失败: ${a.status} ${a.statusText}`);return t}catch(a){return console.error("调用钉钉用户信息API失败:",a),null}}const C=function(){const l=w(),{login:a}=E(),[t,u]=f.useState("loading"),[p,h]=f.useState("正在处理钉钉登录...");return f.useEffect(()=>{(async()=>{try{const c=new URLSearchParams(window.location.search),d=c.get("code")||c.get("authCode"),b=c.get("state");if(!d)throw new Error("未获取到授权码");if(b!=="dingtalk_login")throw new Error("状态参数验证失败");const I="dingaalizs3sqczrirqd",i="Z5PSpa4CO9tg9EFOp4iOJcMVURaNHydKRAw3OEZvdSqrbzvmF0XegE-6pX4fvaXX";console.log("=== 钉钉配置调试信息 ==="),console.log("环境变量 VITE_DINGTALK_CLIENT_ID:","dingaalizs3sqczrirqd"),console.log("环境变量 VITE_DINGTALK_CLIENT_SECRET:","Z5PSpa4CO9tg9EFOp4iOJcMVURaNHydKRAw3OEZvdSqrbzvmF0XegE-6pX4fvaXX"),console.log("实际使用的 clientId:",I),console.log("实际使用的 clientSecret:",i),console.log("clientId 长度:",I?.length),console.log("clientSecret 长度:",i?.length),console.log("所有环境变量:",D),console.log("收到钉钉授权码 (authCode):",d);const _={code:d,state:b,timestamp:new Date().toISOString(),clientId:I,clientSecret:i?`${i.substring(0,4)}****${i.substring(i.length-4)}`:"undefined",redirectUri:window.location.origin+"/auth/dingtalk/callback"};console.log("钉钉登录数据:",_);const r=await j(d,I,i);console.log("钉钉用户token响应:",r);let s,m,e=null;r&&r.accessToken?(m=r.accessToken,console.log("使用钉钉API返回的token:",{accessToken:m,refreshToken:r.refreshToken,expireIn:r.expireIn,corpId:r.corpId}),e=await O(m),console.log("钉钉用户个人信息:",e),e&&e.unionId?s=e.unionId:r.corpId?s=`dingtalk_${r.corpId}`:s=`dingtalk_${d.substring(0,8)}`):(console.warn("钉钉API调用失败，使用fallback token"),s=`dingtalk_${d.substring(0,8)}`,m=`dingtalk_fallback_${d}`);let o;try{if(console.log("正在通过dingTalkUnionId查询用户:",s),o=await N(s),o){if(console.log("用户已存在:",o),e){const g={name:e.nick||o.name,mobile:e.mobile||o.mobile,avatar:e.avatarUrl||o.avatar,dingTalkUserId:e.openId||o.dingTalkUserId,updatedAt:new Date().toISOString()};console.log("正在更新用户信息:",g);try{await U(o.dingTalkUnionId,g),console.log("用户信息更新成功"),o={...o,...g}}catch(S){console.error("更新用户信息失败:",S)}}}else{console.log("用户不存在，正在创建新用户...");const g=e?{dingTalkUserId:e.openId,dingTalkUnionId:e.unionId||s,name:e.nick,avatar:e.avatarUrl,mobile:e.mobile}:{dingTalkUnionId:s,name:"钉钉用户",mobile:"未设置"};o=await A(g),console.log("新用户创建成功:",o)}}catch(g){console.error("数据库操作失败:",g),o={id:Date.now(),dingTalkUnionId:e?.unionId||s,isAdmin:!1,token:2e4,requestTimes:0,name:e?.nick||"钉钉用户",mobile:e?.mobile||"未设置",avatar:e?.avatarUrl,dingTalkUserId:e?.openId,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}}e&&localStorage.setItem("dingtalk_user_info",JSON.stringify(e)),x.getState().setUser(o),a(o,m),u("success"),h("登录成功，正在跳转...");let k=localStorage.getItem("redirectAfterLogin");k||(k=o.isAdmin?"/dashboard":"/ai"),localStorage.removeItem("redirectAfterLogin"),setTimeout(()=>{l({to:k})},1500)}catch(c){console.error("钉钉登录失败:",c),u("error"),h(c instanceof Error?c.message:"登录失败，请重试"),setTimeout(()=>{l({to:"/auth/login"})},3e3)}})()},[]),n.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-white",children:n.jsxs("div",{className:"w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl p-8 flex flex-col items-center border border-blue-100",children:[n.jsxs("div",{className:"flex items-center justify-center w-16 h-16 rounded-2xl bg-white mb-6 shadow-lg",children:[t==="loading"&&n.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t==="success"&&n.jsx("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),t==="error"&&n.jsx("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]}),n.jsxs("h2",{className:"text-xl font-semibold mb-2 text-center text-gray-800",children:[t==="loading"&&"处理中",t==="success"&&"登录成功",t==="error"&&"登录失败"]}),n.jsx("p",{className:"text-gray-600 text-sm text-center",children:p}),t==="error"&&n.jsx("button",{onClick:()=>l({to:"/auth/login"}),className:"mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"返回登录"})]})})};export{C as component};
