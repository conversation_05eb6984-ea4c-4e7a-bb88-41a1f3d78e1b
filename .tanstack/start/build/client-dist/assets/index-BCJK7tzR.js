const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-Cq3MfCug.js","assets/main-CXx9abZr.js"])))=>i.map(i=>d[i]);
import{g as bl,r as d,j as f,R as ue,a as wl,b as Ff,c as Bf,u as Sl,_ as Ei}from"./main-CXx9abZr.js";import{t as $f,a as ms,c as V}from"./index-DFx3G0N8.js";import{c as de}from"./createLucideIcon-6fKCiQbJ.js";import{L as No,u as ft,c as tt,d as jt,e as Uf,A as Cl,a as Tl,b as El}from"./loader-circle-DOZEO2hA.js";import{c as zf,p as Wf,a as Al,u as Kf}from"./use-auth-DVLhXigO.js";import{u as ie,c as vr,a as Hf,B as De,S as Bn,b as Pl}from"./button-DSogGQaG.js";import{P as te,d as Rl,S as Gf}from"./separator-DeQipmPh.js";import{B as Yf}from"./badge-CJmvZTiH.js";import{U as Xf}from"./user-2Hxu6S1o.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Ml=de("check",qf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zf=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Jf=de("chevron-down",Zf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qf=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],eh=de("chevron-right",Qf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const th=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],nh=de("chevron-up",th);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rh=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],oh=de("circle",rh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sh=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],ih=de("copy",sh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ah=[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]],lh=de("image-plus",ah);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ch=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],uh=de("log-out",ch);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dh=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],fh=de("menu",dh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hh=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],ph=de("message-square",hh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mh=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]],gh=de("panel-left-close",mh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vh=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]],yh=de("panel-left-open",vh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xh=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],bh=de("panel-left",xh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wh=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],Sh=de("pen-line",wh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ch=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Th=de("plus",Ch);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eh=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Ah=de("trash-2",Eh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Nr=de("x",Ph);var no,Ai;function Rh(){return Ai||(Ai=1,no=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,o,s;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(s=Object.keys(t),r=s.length,r!==Object.keys(n).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(n,s[o]))return!1;for(o=r;o--!==0;){var i=s[o];if(!e(t[i],n[i]))return!1}return!0}return t!==t&&n!==n}),no}var Mh=Rh();const Dh=bl(Mh),gs=d.createContext({});function vs(e){const t=d.useRef(null);return t.current===null&&(t.current=e()),t.current}const ys=typeof window<"u",Dl=ys?d.useLayoutEffect:d.useEffect,jr=d.createContext(null);function xs(e,t){e.indexOf(t)===-1&&e.push(t)}function bs(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const nt=(e,t,n)=>n>t?t:n<e?e:n;let ws=()=>{};const rt={},Nl=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function jl(e){return typeof e=="object"&&e!==null}const kl=e=>/^0[^.\s]+$/u.test(e);function Ss(e){let t;return()=>(t===void 0&&(t=e()),t)}const Ne=e=>e,Nh=(e,t)=>n=>t(e(n)),$n=(...e)=>e.reduce(Nh),Rn=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r};class Cs{constructor(){this.subscriptions=[]}add(t){return xs(this.subscriptions,t),()=>bs(this.subscriptions,t)}notify(t,n,r){const o=this.subscriptions.length;if(o)if(o===1)this.subscriptions[0](t,n,r);else for(let s=0;s<o;s++){const i=this.subscriptions[s];i&&i(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ge=e=>e*1e3,Ye=e=>e/1e3;function _l(e,t){return t?e*(1e3/t):0}const Ol=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,jh=1e-7,kh=12;function _h(e,t,n,r,o){let s,i,a=0;do i=t+(n-t)/2,s=Ol(i,r,o)-e,s>0?n=i:t=i;while(Math.abs(s)>jh&&++a<kh);return i}function Un(e,t,n,r){if(e===t&&n===r)return Ne;const o=s=>_h(s,0,1,e,n);return s=>s===0||s===1?s:Ol(o(s),t,r)}const Il=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Ll=e=>t=>1-e(1-t),Vl=Un(.33,1.53,.69,.99),Ts=Ll(Vl),Fl=Il(Ts),Bl=e=>(e*=2)<1?.5*Ts(e):.5*(2-Math.pow(2,-10*(e-1))),Es=e=>1-Math.sin(Math.acos(e)),$l=Ll(Es),Ul=Il(Es),Oh=Un(.42,0,1,1),Ih=Un(0,0,.58,1),zl=Un(.42,0,.58,1),Lh=e=>Array.isArray(e)&&typeof e[0]!="number",Wl=e=>Array.isArray(e)&&typeof e[0]=="number",Vh={linear:Ne,easeIn:Oh,easeInOut:zl,easeOut:Ih,circIn:Es,circInOut:Ul,circOut:$l,backIn:Ts,backInOut:Fl,backOut:Vl,anticipate:Bl},Fh=e=>typeof e=="string",Pi=e=>{if(Wl(e)){ws(e.length===4);const[t,n,r,o]=e;return Un(t,n,r,o)}else if(Fh(e))return Vh[e];return e},er=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function Bh(e,t){let n=new Set,r=new Set,o=!1,s=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function l(c){i.has(c)&&(u.schedule(c),e()),c(a)}const u={schedule:(c,h=!1,p=!1)=>{const v=p&&o?n:r;return h&&i.add(c),v.has(c)||v.add(c),c},cancel:c=>{r.delete(c),i.delete(c)},process:c=>{if(a=c,o){s=!0;return}o=!0,[n,r]=[r,n],n.forEach(l),n.clear(),o=!1,s&&(s=!1,u.process(c))}};return u}const $h=40;function Kl(e,t){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,i=er.reduce((b,S)=>(b[S]=Bh(s),b),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:c,update:h,preRender:p,render:m,postRender:v}=i,g=()=>{const b=rt.useManualTiming?o.timestamp:performance.now();n=!1,rt.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(b-o.timestamp,$h),1)),o.timestamp=b,o.isProcessing=!0,a.process(o),l.process(o),u.process(o),c.process(o),h.process(o),p.process(o),m.process(o),v.process(o),o.isProcessing=!1,n&&t&&(r=!1,e(g))},y=()=>{n=!0,r=!0,o.isProcessing||e(g)};return{schedule:er.reduce((b,S)=>{const C=i[S];return b[S]=(R,T=!1,P=!1)=>(n||y(),C.schedule(R,T,P)),b},{}),cancel:b=>{for(let S=0;S<er.length;S++)i[er[S]].cancel(b)},state:o,steps:i}}const{schedule:q,cancel:ht,state:ce,steps:ro}=Kl(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ne,!0);let ur;function Uh(){ur=void 0}const xe={now:()=>(ur===void 0&&xe.set(ce.isProcessing||rt.useManualTiming?ce.timestamp:performance.now()),ur),set:e=>{ur=e,queueMicrotask(Uh)}},Hl=e=>t=>typeof t=="string"&&t.startsWith(e),As=Hl("--"),zh=Hl("var(--"),Ps=e=>zh(e)?Wh.test(e.split("/*")[0].trim()):!1,Wh=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,on={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Mn={...on,transform:e=>nt(0,1,e)},tr={...on,default:1},Cn=e=>Math.round(e*1e5)/1e5,Rs=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Kh(e){return e==null}const Hh=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ms=(e,t)=>n=>!!(typeof n=="string"&&Hh.test(n)&&n.startsWith(e)||t&&!Kh(n)&&Object.prototype.hasOwnProperty.call(n,t)),Gl=(e,t,n)=>r=>{if(typeof r!="string")return r;const[o,s,i,a]=r.match(Rs);return{[e]:parseFloat(o),[t]:parseFloat(s),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},Gh=e=>nt(0,255,e),oo={...on,transform:e=>Math.round(Gh(e))},Et={test:Ms("rgb","red"),parse:Gl("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+oo.transform(e)+", "+oo.transform(t)+", "+oo.transform(n)+", "+Cn(Mn.transform(r))+")"};function Yh(e){let t="",n="",r="",o="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),o=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),o=e.substring(4,5),t+=t,n+=n,r+=r,o+=o),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:o?parseInt(o,16)/255:1}}const jo={test:Ms("#"),parse:Yh,transform:Et.transform},zn=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),ut=zn("deg"),Xe=zn("%"),$=zn("px"),Xh=zn("vh"),qh=zn("vw"),Ri={...Xe,parse:e=>Xe.parse(e)/100,transform:e=>Xe.transform(e*100)},Gt={test:Ms("hsl","hue"),parse:Gl("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Xe.transform(Cn(t))+", "+Xe.transform(Cn(n))+", "+Cn(Mn.transform(r))+")"},oe={test:e=>Et.test(e)||jo.test(e)||Gt.test(e),parse:e=>Et.test(e)?Et.parse(e):Gt.test(e)?Gt.parse(e):jo.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?Et.transform(e):Gt.transform(e),getAnimatableNone:e=>{const t=oe.parse(e);return t.alpha=0,oe.transform(t)}},Zh=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Jh(e){return isNaN(e)&&typeof e=="string"&&(e.match(Rs)?.length||0)+(e.match(Zh)?.length||0)>0}const Yl="number",Xl="color",Qh="var",ep="var(",Mi="${}",tp=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Dn(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},o=[];let s=0;const a=t.replace(tp,l=>(oe.test(l)?(r.color.push(s),o.push(Xl),n.push(oe.parse(l))):l.startsWith(ep)?(r.var.push(s),o.push(Qh),n.push(l)):(r.number.push(s),o.push(Yl),n.push(parseFloat(l))),++s,Mi)).split(Mi);return{values:n,split:a,indexes:r,types:o}}function ql(e){return Dn(e).values}function Zl(e){const{split:t,types:n}=Dn(e),r=t.length;return o=>{let s="";for(let i=0;i<r;i++)if(s+=t[i],o[i]!==void 0){const a=n[i];a===Yl?s+=Cn(o[i]):a===Xl?s+=oe.transform(o[i]):s+=o[i]}return s}}const np=e=>typeof e=="number"?0:oe.test(e)?oe.getAnimatableNone(e):e;function rp(e){const t=ql(e);return Zl(e)(t.map(np))}const pt={test:Jh,parse:ql,createTransformer:Zl,getAnimatableNone:rp};function so(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function op({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let o=0,s=0,i=0;if(!t)o=s=i=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;o=so(l,a,e+1/3),s=so(l,a,e),i=so(l,a,e-1/3)}return{red:Math.round(o*255),green:Math.round(s*255),blue:Math.round(i*255),alpha:r}}function yr(e,t){return n=>n>0?t:e}const Q=(e,t,n)=>e+(t-e)*n,io=(e,t,n)=>{const r=e*e,o=n*(t*t-r)+r;return o<0?0:Math.sqrt(o)},sp=[jo,Et,Gt],ip=e=>sp.find(t=>t.test(e));function Di(e){const t=ip(e);if(!t)return!1;let n=t.parse(e);return t===Gt&&(n=op(n)),n}const Ni=(e,t)=>{const n=Di(e),r=Di(t);if(!n||!r)return yr(e,t);const o={...n};return s=>(o.red=io(n.red,r.red,s),o.green=io(n.green,r.green,s),o.blue=io(n.blue,r.blue,s),o.alpha=Q(n.alpha,r.alpha,s),Et.transform(o))},ko=new Set(["none","hidden"]);function ap(e,t){return ko.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function lp(e,t){return n=>Q(e,t,n)}function Ds(e){return typeof e=="number"?lp:typeof e=="string"?Ps(e)?yr:oe.test(e)?Ni:dp:Array.isArray(e)?Jl:typeof e=="object"?oe.test(e)?Ni:cp:yr}function Jl(e,t){const n=[...e],r=n.length,o=e.map((s,i)=>Ds(s)(s,t[i]));return s=>{for(let i=0;i<r;i++)n[i]=o[i](s);return n}}function cp(e,t){const n={...e,...t},r={};for(const o in n)e[o]!==void 0&&t[o]!==void 0&&(r[o]=Ds(e[o])(e[o],t[o]));return o=>{for(const s in r)n[s]=r[s](o);return n}}function up(e,t){const n=[],r={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){const s=t.types[o],i=e.indexes[s][r[s]],a=e.values[i]??0;n[o]=a,r[s]++}return n}const dp=(e,t)=>{const n=pt.createTransformer(t),r=Dn(e),o=Dn(t);return r.indexes.var.length===o.indexes.var.length&&r.indexes.color.length===o.indexes.color.length&&r.indexes.number.length>=o.indexes.number.length?ko.has(e)&&!o.values.length||ko.has(t)&&!r.values.length?ap(e,t):$n(Jl(up(r,o),o.values),n):yr(e,t)};function Ql(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?Q(e,t,n):Ds(e)(e,t)}const fp=e=>{const t=({timestamp:n})=>e(n);return{start:(n=!0)=>q.update(t,n),stop:()=>ht(t),now:()=>ce.isProcessing?ce.timestamp:xe.now()}},ec=(e,t,n=10)=>{let r="";const o=Math.max(Math.round(t/n),2);for(let s=0;s<o;s++)r+=Math.round(e(s/(o-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},xr=2e4;function Ns(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<xr;)t+=n,r=e.next(t);return t>=xr?1/0:t}function hp(e,t=100,n){const r=n({...e,keyframes:[0,t]}),o=Math.min(Ns(r),xr);return{type:"keyframes",ease:s=>r.next(o*s).value/t,duration:Ye(o)}}const pp=5;function tc(e,t,n){const r=Math.max(t-pp,0);return _l(n-e(r),t-r)}const ee={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ao=.001;function mp({duration:e=ee.duration,bounce:t=ee.bounce,velocity:n=ee.velocity,mass:r=ee.mass}){let o,s,i=1-t;i=nt(ee.minDamping,ee.maxDamping,i),e=nt(ee.minDuration,ee.maxDuration,Ye(e)),i<1?(o=u=>{const c=u*i,h=c*e,p=c-n,m=_o(u,i),v=Math.exp(-h);return ao-p/m*v},s=u=>{const h=u*i*e,p=h*n+n,m=Math.pow(i,2)*Math.pow(u,2)*e,v=Math.exp(-h),g=_o(Math.pow(u,2),i);return(-o(u)+ao>0?-1:1)*((p-m)*v)/g}):(o=u=>{const c=Math.exp(-u*e),h=(u-n)*e+1;return-ao+c*h},s=u=>{const c=Math.exp(-u*e),h=(n-u)*(e*e);return c*h});const a=5/e,l=vp(o,s,a);if(e=Ge(e),isNaN(l))return{stiffness:ee.stiffness,damping:ee.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:i*2*Math.sqrt(r*u),duration:e}}}const gp=12;function vp(e,t,n){let r=n;for(let o=1;o<gp;o++)r=r-e(r)/t(r);return r}function _o(e,t){return e*Math.sqrt(1-t*t)}const yp=["duration","bounce"],xp=["stiffness","damping","mass"];function ji(e,t){return t.some(n=>e[n]!==void 0)}function bp(e){let t={velocity:ee.velocity,stiffness:ee.stiffness,damping:ee.damping,mass:ee.mass,isResolvedFromDuration:!1,...e};if(!ji(e,xp)&&ji(e,yp))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),o=r*r,s=2*nt(.05,1,1-(e.bounce||0))*Math.sqrt(o);t={...t,mass:ee.mass,stiffness:o,damping:s}}else{const n=mp(e);t={...t,...n,mass:ee.mass},t.isResolvedFromDuration=!0}return t}function br(e=ee.visualDuration,t=ee.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:o}=n;const s=n.keyframes[0],i=n.keyframes[n.keyframes.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:c,duration:h,velocity:p,isResolvedFromDuration:m}=bp({...n,velocity:-Ye(n.velocity||0)}),v=p||0,g=u/(2*Math.sqrt(l*c)),y=i-s,x=Ye(Math.sqrt(l/c)),w=Math.abs(y)<5;r||(r=w?ee.restSpeed.granular:ee.restSpeed.default),o||(o=w?ee.restDelta.granular:ee.restDelta.default);let b;if(g<1){const C=_o(x,g);b=R=>{const T=Math.exp(-g*x*R);return i-T*((v+g*x*y)/C*Math.sin(C*R)+y*Math.cos(C*R))}}else if(g===1)b=C=>i-Math.exp(-x*C)*(y+(v+x*y)*C);else{const C=x*Math.sqrt(g*g-1);b=R=>{const T=Math.exp(-g*x*R),P=Math.min(C*R,300);return i-T*((v+g*x*y)*Math.sinh(P)+C*y*Math.cosh(P))/C}}const S={calculatedDuration:m&&h||null,next:C=>{const R=b(C);if(m)a.done=C>=h;else{let T=C===0?v:0;g<1&&(T=C===0?Ge(v):tc(b,C,R));const P=Math.abs(T)<=r,A=Math.abs(i-R)<=o;a.done=P&&A}return a.value=a.done?i:R,a},toString:()=>{const C=Math.min(Ns(S),xr),R=ec(T=>S.next(C*T).value,C,30);return C+"ms "+R},toTransition:()=>{}};return S}br.applyToOptions=e=>{const t=hp(e,100,br);return e.ease=t.ease,e.duration=Ge(t.duration),e.type="keyframes",e};function Oo({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:s=500,modifyTarget:i,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=e[0],p={done:!1,value:h},m=P=>a!==void 0&&P<a||l!==void 0&&P>l,v=P=>a===void 0?l:l===void 0||Math.abs(a-P)<Math.abs(l-P)?a:l;let g=n*t;const y=h+g,x=i===void 0?y:i(y);x!==y&&(g=x-h);const w=P=>-g*Math.exp(-P/r),b=P=>x+w(P),S=P=>{const A=w(P),I=b(P);p.done=Math.abs(A)<=u,p.value=p.done?x:I};let C,R;const T=P=>{m(p.value)&&(C=P,R=br({keyframes:[p.value,v(p.value)],velocity:tc(b,P,p.value),damping:o,stiffness:s,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:P=>{let A=!1;return!R&&C===void 0&&(A=!0,S(P),T(P)),C!==void 0&&P>=C?R.next(P-C):(!A&&S(P),p)}}}function wp(e,t,n){const r=[],o=n||rt.mix||Ql,s=e.length-1;for(let i=0;i<s;i++){let a=o(e[i],e[i+1]);if(t){const l=Array.isArray(t)?t[i]||Ne:t;a=$n(l,a)}r.push(a)}return r}function Sp(e,t,{clamp:n=!0,ease:r,mixer:o}={}){const s=e.length;if(ws(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const i=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=wp(t,r,o),l=a.length,u=c=>{if(i&&c<e[0])return t[0];let h=0;if(l>1)for(;h<e.length-2&&!(c<e[h+1]);h++);const p=Rn(e[h],e[h+1],c);return a[h](p)};return n?c=>u(nt(e[0],e[s-1],c)):u}function Cp(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const o=Rn(0,t,r);e.push(Q(n,1,o))}}function Tp(e){const t=[0];return Cp(t,e.length-1),t}function Ep(e,t){return e.map(n=>n*t)}function Ap(e,t){return e.map(()=>t||zl).splice(0,e.length-1)}function Tn({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const o=Lh(r)?r.map(Pi):Pi(r),s={done:!1,value:t[0]},i=Ep(n&&n.length===t.length?n:Tp(t),e),a=Sp(i,t,{ease:Array.isArray(o)?o:Ap(t,o)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}const Pp=e=>e!==null;function js(e,{repeat:t,repeatType:n="loop"},r,o=1){const s=e.filter(Pp),a=o<0||t&&n!=="loop"&&t%2===1?0:s.length-1;return!a||r===void 0?s[a]:r}const Rp={decay:Oo,inertia:Oo,tween:Tn,keyframes:Tn,spring:br};function nc(e){typeof e.type=="string"&&(e.type=Rp[e.type])}class ks{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,n){return this.finished.then(t,n)}}const Mp=e=>e/100;class _s extends ks{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==xe.now()&&this.tick(xe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;nc(t);const{type:n=Tn,repeat:r=0,repeatDelay:o=0,repeatType:s,velocity:i=0}=t;let{keyframes:a}=t;const l=n||Tn;l!==Tn&&typeof a[0]!="number"&&(this.mixKeyframes=$n(Mp,Ql(a[0],a[1])),a=[0,100]);const u=l({...t,keyframes:a});s==="mirror"&&(this.mirroredGenerator=l({...t,keyframes:[...a].reverse(),velocity:-i})),u.calculatedDuration===null&&(u.calculatedDuration=Ns(u));const{calculatedDuration:c}=u;this.calculatedDuration=c,this.resolvedDuration=c+o,this.totalDuration=this.resolvedDuration*(r+1)-o,this.generator=u}updateTime(t){const n=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(t,n=!1){const{generator:r,totalDuration:o,mixKeyframes:s,mirroredGenerator:i,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return r.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:p,repeatDelay:m,type:v,onUpdate:g,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-o/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),w=this.playbackSpeed>=0?x<0:x>o;this.currentTime=Math.max(x,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=o);let b=this.currentTime,S=r;if(h){const P=Math.min(this.currentTime,o)/a;let A=Math.floor(P),I=P%1;!I&&P>=1&&(I=1),I===1&&A--,A=Math.min(A,h+1),!!(A%2)&&(p==="reverse"?(I=1-I,m&&(I-=m/a)):p==="mirror"&&(S=i)),b=nt(0,1,I)*a}const C=w?{done:!1,value:c[0]}:S.next(b);s&&(C.value=s(C.value));let{done:R}=C;!w&&l!==null&&(R=this.playbackSpeed>=0?this.currentTime>=o:this.currentTime<=0);const T=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&R);return T&&v!==Oo&&(C.value=js(c,this.options,y,this.speed)),g&&g(C.value),T&&this.finish(),C}then(t,n){return this.finished.then(t,n)}get duration(){return Ye(this.calculatedDuration)}get time(){return Ye(this.currentTime)}set time(t){t=Ge(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(xe.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=Ye(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=fp,startTime:n}=this.options;this.driver||(this.driver=t(o=>this.tick(o))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=n??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(xe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Dp(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const At=e=>e*180/Math.PI,Io=e=>{const t=At(Math.atan2(e[1],e[0]));return Lo(t)},Np={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Io,rotateZ:Io,skewX:e=>At(Math.atan(e[1])),skewY:e=>At(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Lo=e=>(e=e%360,e<0&&(e+=360),e),ki=Io,_i=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),Oi=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),jp={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:_i,scaleY:Oi,scale:e=>(_i(e)+Oi(e))/2,rotateX:e=>Lo(At(Math.atan2(e[6],e[5]))),rotateY:e=>Lo(At(Math.atan2(-e[2],e[0]))),rotateZ:ki,rotate:ki,skewX:e=>At(Math.atan(e[4])),skewY:e=>At(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Vo(e){return e.includes("scale")?1:0}function Fo(e,t){if(!e||e==="none")return Vo(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,o;if(n)r=jp,o=n;else{const a=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=Np,o=a}if(!o)return Vo(t);const s=r[t],i=o[1].split(",").map(_p);return typeof s=="function"?s(i):i[s]}const kp=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return Fo(n,t)};function _p(e){return parseFloat(e.trim())}const sn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],an=new Set(sn),Ii=e=>e===on||e===$,Op=new Set(["x","y","z"]),Ip=sn.filter(e=>!Op.has(e));function Lp(e){const t=[];return Ip.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Pt={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Fo(t,"x"),y:(e,{transform:t})=>Fo(t,"y")};Pt.translateX=Pt.x;Pt.translateY=Pt.y;const Rt=new Set;let Bo=!1,$o=!1,Uo=!1;function rc(){if($o){const e=Array.from(Rt).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const o=Lp(r);o.length&&(n.set(r,o),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const o=n.get(r);o&&o.forEach(([s,i])=>{r.getValue(s)?.set(i)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}$o=!1,Bo=!1,Rt.forEach(e=>e.complete(Uo)),Rt.clear()}function oc(){Rt.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&($o=!0)})}function Vp(){Uo=!0,oc(),rc(),Uo=!1}class Os{constructor(t,n,r,o,s,i=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=o,this.element=s,this.isAsync=i}scheduleResolve(){this.state="scheduled",this.isAsync?(Rt.add(this),Bo||(Bo=!0,q.read(oc),q.resolveKeyframes(rc))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:o}=this;if(t[0]===null){const s=o?.get(),i=t[t.length-1];if(s!==void 0)t[0]=s;else if(r&&n){const a=r.readValue(n,i);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=i),o&&s===void 0&&o.set(t[0])}Dp(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Rt.delete(this)}cancel(){this.state==="scheduled"&&(Rt.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const Fp=e=>e.startsWith("--");function Bp(e,t,n){Fp(t)?e.style.setProperty(t,n):e.style[t]=n}const $p=Ss(()=>window.ScrollTimeline!==void 0),Up={};function zp(e,t){const n=Ss(e);return()=>Up[t]??n()}const sc=zp(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),bn=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Li={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:bn([0,.65,.55,1]),circOut:bn([.55,0,1,.45]),backIn:bn([.31,.01,.66,-.59]),backOut:bn([.33,1.53,.69,.99])};function ic(e,t){if(e)return typeof e=="function"?sc()?ec(e,t):"ease-out":Wl(e)?bn(e):Array.isArray(e)?e.map(n=>ic(n,t)||Li.easeOut):Li[e]}function Wp(e,t,n,{delay:r=0,duration:o=300,repeat:s=0,repeatType:i="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[t]:n};l&&(c.offset=l);const h=ic(a,o);Array.isArray(h)&&(c.easing=h);const p={delay:r,duration:o,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:i==="reverse"?"alternate":"normal"};return u&&(p.pseudoElement=u),e.animate(c,p)}function ac(e){return typeof e=="function"&&"applyToOptions"in e}function Kp({type:e,...t}){return ac(e)&&sc()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Hp extends ks{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:r,keyframes:o,pseudoElement:s,allowFlatten:i=!1,finalKeyframe:a,onComplete:l}=t;this.isPseudoElement=!!s,this.allowFlatten=i,this.options=t,ws(typeof t.type!="string");const u=Kp(t);this.animation=Wp(n,r,o,u,s),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const c=js(o,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(c):Bp(n,r,c),this.animation.cancel()}l?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return Ye(Number(t))}get time(){return Ye(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Ge(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&$p()?(this.animation.timeline=t,Ne):n(this)}}const lc={anticipate:Bl,backInOut:Fl,circInOut:Ul};function Gp(e){return e in lc}function Yp(e){typeof e.ease=="string"&&Gp(e.ease)&&(e.ease=lc[e.ease])}const Vi=10;class Xp extends Hp{constructor(t){Yp(t),nc(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:r,onComplete:o,element:s,...i}=this.options;if(!n)return;if(t!==void 0){n.set(t);return}const a=new _s({...i,autoplay:!1}),l=Ge(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-Vi).value,a.sample(l).value,Vi),a.stop()}}const Fi=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(pt.test(e)||e==="0")&&!e.startsWith("url("));function qp(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function Zp(e,t,n,r){const o=e[0];if(o===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],i=Fi(o,t),a=Fi(s,t);return!i||!a?!1:qp(e)||(n==="spring"||ac(n))&&r}function zo(e){e.duration=0,e.type}const Jp=new Set(["opacity","clipPath","filter","transform"]),Qp=Ss(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function em(e){const{motionValue:t,name:n,repeatDelay:r,repeatType:o,damping:s,type:i}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=t.owner.getProps();return Qp()&&n&&Jp.has(n)&&(n!=="transform"||!u)&&!l&&!r&&o!=="mirror"&&s!==0&&i!=="inertia"}const tm=40;class nm extends ks{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:i="loop",keyframes:a,name:l,motionValue:u,element:c,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=xe.now();const p={autoplay:t,delay:n,type:r,repeat:o,repeatDelay:s,repeatType:i,name:l,motionValue:u,element:c,...h},m=c?.KeyframeResolver||Os;this.keyframeResolver=new m(a,(v,g,y)=>this.onKeyframesResolved(v,g,p,!y),l,u,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,r,o){this.keyframeResolver=void 0;const{name:s,type:i,velocity:a,delay:l,isHandoff:u,onUpdate:c}=r;this.resolvedAt=xe.now(),Zp(t,s,i,a)||((rt.instantAnimations||!l)&&c?.(js(t,r,n)),t[0]=t[t.length-1],zo(r),r.repeat=0);const p={startTime:o?this.resolvedAt?this.resolvedAt-this.createdAt>tm?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...r,keyframes:t},m=!u&&em(p)?new Xp({...p,element:p.motionValue.owner.current}):new _s(p);m.finished.then(()=>this.notifyFinished()).catch(Ne),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,n){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Vp()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const rm=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function om(e){const t=rm.exec(e);if(!t)return[,];const[,n,r,o]=t;return[`--${n??r}`,o]}function cc(e,t,n=1){const[r,o]=om(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const i=s.trim();return Nl(i)?parseFloat(i):i}return Ps(o)?cc(o,t,n+1):o}function Is(e,t){return e?.[t]??e?.default??e}const uc=new Set(["width","height","top","left","right","bottom",...sn]),sm={test:e=>e==="auto",parse:e=>e},dc=e=>t=>t.test(e),fc=[on,$,Xe,ut,qh,Xh,sm],Bi=e=>fc.find(dc(e));function im(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||kl(e):!0}const am=new Set(["brightness","contrast","saturate","opacity"]);function lm(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Rs)||[];if(!r)return e;const o=n.replace(r,"");let s=am.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+o+")"}const cm=/\b([a-z-]*)\(.*?\)/gu,Wo={...pt,getAnimatableNone:e=>{const t=e.match(cm);return t?t.map(lm).join(" "):e}},$i={...on,transform:Math.round},um={rotate:ut,rotateX:ut,rotateY:ut,rotateZ:ut,scale:tr,scaleX:tr,scaleY:tr,scaleZ:tr,skew:ut,skewX:ut,skewY:ut,distance:$,translateX:$,translateY:$,translateZ:$,x:$,y:$,z:$,perspective:$,transformPerspective:$,opacity:Mn,originX:Ri,originY:Ri,originZ:$},Ls={borderWidth:$,borderTopWidth:$,borderRightWidth:$,borderBottomWidth:$,borderLeftWidth:$,borderRadius:$,radius:$,borderTopLeftRadius:$,borderTopRightRadius:$,borderBottomRightRadius:$,borderBottomLeftRadius:$,width:$,maxWidth:$,height:$,maxHeight:$,top:$,right:$,bottom:$,left:$,padding:$,paddingTop:$,paddingRight:$,paddingBottom:$,paddingLeft:$,margin:$,marginTop:$,marginRight:$,marginBottom:$,marginLeft:$,backgroundPositionX:$,backgroundPositionY:$,...um,zIndex:$i,fillOpacity:Mn,strokeOpacity:Mn,numOctaves:$i},dm={...Ls,color:oe,backgroundColor:oe,outlineColor:oe,fill:oe,stroke:oe,borderColor:oe,borderTopColor:oe,borderRightColor:oe,borderBottomColor:oe,borderLeftColor:oe,filter:Wo,WebkitFilter:Wo},hc=e=>dm[e];function pc(e,t){let n=hc(e);return n!==Wo&&(n=pt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const fm=new Set(["auto","none","0"]);function hm(e,t,n){let r=0,o;for(;r<e.length&&!o;){const s=e[r];typeof s=="string"&&!fm.has(s)&&Dn(s).values.length&&(o=e[r]),r++}if(o&&n)for(const s of t)e[s]=pc(n,o)}class pm extends Os{constructor(t,n,r,o,s){super(t,n,r,o,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),Ps(u))){const c=cc(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!uc.has(r)||t.length!==2)return;const[o,s]=t,i=Bi(o),a=Bi(s);if(i!==a)if(Ii(i)&&Ii(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else Pt[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let o=0;o<t.length;o++)(t[o]===null||im(t[o]))&&r.push(o);r.length&&hm(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Pt[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const o=n[n.length-1];o!==void 0&&t.getValue(r,o).jump(o,!1)}measureEndState(){const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const o=t.getValue(n);o&&o.jump(this.measuredOrigin,!1);const s=r.length-1,i=r[s];r[s]=Pt[n](t.measureViewportBox(),window.getComputedStyle(t.current)),i!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,l])=>{t.getValue(a).set(l)}),this.resolveNoneKeyframes()}}function mm(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let r=document;const o=n?.[e]??r.querySelectorAll(e);return o?Array.from(o):[]}return Array.from(e)}const mc=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function gc(e){return jl(e)&&"offsetHeight"in e}const Ui=30,gm=e=>!isNaN(parseFloat(e));class vm{constructor(t,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=r=>{const o=xe.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const s of this.dependents)s.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=xe.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=gm(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Cs);const r=this.events[t].add(n);return t==="change"?()=>{r(),q.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=xe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Ui)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Ui);return _l(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function en(e,t){return new vm(e,t)}const{schedule:Vs}=Kl(queueMicrotask,!1),Oe={x:!1,y:!1};function vc(){return Oe.x||Oe.y}function ym(e){return e==="x"||e==="y"?Oe[e]?null:(Oe[e]=!0,()=>{Oe[e]=!1}):Oe.x||Oe.y?null:(Oe.x=Oe.y=!0,()=>{Oe.x=Oe.y=!1})}function yc(e,t){const n=mm(e),r=new AbortController,o={passive:!0,...t,signal:r.signal};return[n,o,()=>r.abort()]}function zi(e){return!(e.pointerType==="touch"||vc())}function xm(e,t,n={}){const[r,o,s]=yc(e,n),i=a=>{if(!zi(a))return;const{target:l}=a,u=t(l,a);if(typeof u!="function"||!l)return;const c=h=>{zi(h)&&(u(h),l.removeEventListener("pointerleave",c))};l.addEventListener("pointerleave",c,o)};return r.forEach(a=>{a.addEventListener("pointerenter",i,o)}),s}const xc=(e,t)=>t?e===t?!0:xc(e,t.parentElement):!1,Fs=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,bm=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function wm(e){return bm.has(e.tagName)||e.tabIndex!==-1}const dr=new WeakSet;function Wi(e){return t=>{t.key==="Enter"&&e(t)}}function lo(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const Sm=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=Wi(()=>{if(dr.has(n))return;lo(n,"down");const o=Wi(()=>{lo(n,"up")}),s=()=>lo(n,"cancel");n.addEventListener("keyup",o,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Ki(e){return Fs(e)&&!vc()}function Cm(e,t,n={}){const[r,o,s]=yc(e,n),i=a=>{const l=a.currentTarget;if(!Ki(a))return;dr.add(l);const u=t(l,a),c=(m,v)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",p),dr.has(l)&&dr.delete(l),Ki(m)&&typeof u=="function"&&u(m,{success:v})},h=m=>{c(m,l===window||l===document||n.useGlobalTarget||xc(l,m.target))},p=m=>{c(m,!1)};window.addEventListener("pointerup",h,o),window.addEventListener("pointercancel",p,o)};return r.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",i,o),gc(a)&&(a.addEventListener("focus",u=>Sm(u,o)),!wm(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),s}function bc(e){return jl(e)&&"ownerSVGElement"in e}function Tm(e){return bc(e)&&e.tagName==="svg"}const he=e=>!!(e&&e.getVelocity),Em=[...fc,oe,pt],Am=e=>Em.find(dc(e)),Bs=d.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class Pm extends d.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,o=gc(r)&&r.offsetWidth||0,s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft,s.right=o-s.width-s.left}return null}componentDidUpdate(){}render(){return this.props.children}}function Rm({children:e,isPresent:t,anchorX:n,root:r}){const o=d.useId(),s=d.useRef(null),i=d.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=d.useContext(Bs);return d.useInsertionEffect(()=>{const{width:l,height:u,top:c,left:h,right:p}=i.current;if(t||!s.current||!l||!u)return;const m=n==="left"?`left: ${h}`:`right: ${p}`;s.current.dataset.motionPopId=o;const v=document.createElement("style");a&&(v.nonce=a);const g=r??document.head;return g.appendChild(v),v.sheet&&v.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${l}px !important;
            height: ${u}px !important;
            ${m}px !important;
            top: ${c}px !important;
          }
        `),()=>{g.contains(v)&&g.removeChild(v)}},[t]),f.jsx(Pm,{isPresent:t,childRef:s,sizeRef:i,children:d.cloneElement(e,{ref:s})})}const Mm=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:o,presenceAffectsLayout:s,mode:i,anchorX:a,root:l})=>{const u=vs(Dm),c=d.useId();let h=!0,p=d.useMemo(()=>(h=!1,{id:c,initial:t,isPresent:n,custom:o,onExitComplete:m=>{u.set(m,!0);for(const v of u.values())if(!v)return;r&&r()},register:m=>(u.set(m,!1),()=>u.delete(m))}),[n,u,r]);return s&&h&&(p={...p}),d.useMemo(()=>{u.forEach((m,v)=>u.set(v,!1))},[n]),d.useEffect(()=>{!n&&!u.size&&r&&r()},[n]),i==="popLayout"&&(e=f.jsx(Rm,{isPresent:n,anchorX:a,root:l,children:e})),f.jsx(jr.Provider,{value:p,children:e})};function Dm(){return new Map}function wc(e=!0){const t=d.useContext(jr);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:o}=t,s=d.useId();d.useEffect(()=>{if(e)return o(s)},[e]);const i=d.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,i]:[!0]}const nr=e=>e.key||"";function Hi(e){const t=[];return d.Children.forEach(e,n=>{d.isValidElement(n)&&t.push(n)}),t}const Sc=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:o=!0,mode:s="sync",propagate:i=!1,anchorX:a="left",root:l})=>{const[u,c]=wc(i),h=d.useMemo(()=>Hi(e),[e]),p=i&&!u?[]:h.map(nr),m=d.useRef(!0),v=d.useRef(h),g=vs(()=>new Map),[y,x]=d.useState(h),[w,b]=d.useState(h);Dl(()=>{m.current=!1,v.current=h;for(let R=0;R<w.length;R++){const T=nr(w[R]);p.includes(T)?g.delete(T):g.get(T)!==!0&&g.set(T,!1)}},[w,p.length,p.join("-")]);const S=[];if(h!==y){let R=[...h];for(let T=0;T<w.length;T++){const P=w[T],A=nr(P);p.includes(A)||(R.splice(T,0,P),S.push(P))}return s==="wait"&&S.length&&(R=S),b(Hi(R)),x(h),null}const{forceRender:C}=d.useContext(gs);return f.jsx(f.Fragment,{children:w.map(R=>{const T=nr(R),P=i&&!u?!1:h===w||p.includes(T),A=()=>{if(g.has(T))g.set(T,!0);else return;let I=!0;g.forEach(F=>{F||(I=!1)}),I&&(C?.(),b(v.current),i&&c?.(),r&&r())};return f.jsx(Mm,{isPresent:P,initial:!m.current||n?void 0:!1,custom:t,presenceAffectsLayout:o,mode:s,root:l,onExitComplete:P?void 0:A,anchorX:a,children:R},T)})})},Cc=d.createContext({strict:!1}),Gi={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},tn={};for(const e in Gi)tn[e]={isEnabled:t=>Gi[e].some(n=>!!t[n])};function Nm(e){for(const t in e)tn[t]={...tn[t],...e[t]}}const jm=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function wr(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||jm.has(e)}let Tc=e=>!wr(e);function km(e){typeof e=="function"&&(Tc=t=>t.startsWith("on")?!wr(t):e(t))}try{km(require("@emotion/is-prop-valid").default)}catch{}function _m(e,t,n){const r={};for(const o in e)o==="values"&&typeof e.values=="object"||(Tc(o)||n===!0&&wr(o)||!t&&!wr(o)||e.draggable&&o.startsWith("onDrag"))&&(r[o]=e[o]);return r}const kr=d.createContext({});function _r(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function Nn(e){return typeof e=="string"||Array.isArray(e)}const $s=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Us=["initial",...$s];function Or(e){return _r(e.animate)||Us.some(t=>Nn(e[t]))}function Ec(e){return!!(Or(e)||e.variants)}function Om(e,t){if(Or(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Nn(n)?n:void 0,animate:Nn(r)?r:void 0}}return e.inherit!==!1?t:{}}function Im(e){const{initial:t,animate:n}=Om(e,d.useContext(kr));return d.useMemo(()=>({initial:t,animate:n}),[Yi(t),Yi(n)])}function Yi(e){return Array.isArray(e)?e.join(" "):e}const jn={};function Lm(e){for(const t in e)jn[t]=e[t],As(t)&&(jn[t].isCSSVariable=!0)}function Ac(e,{layout:t,layoutId:n}){return an.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!jn[e]||e==="opacity")}const Vm={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Fm=sn.length;function Bm(e,t,n){let r="",o=!0;for(let s=0;s<Fm;s++){const i=sn[s],a=e[i];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(i.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=mc(a,Ls[i]);if(!l){o=!1;const c=Vm[i]||i;r+=`${c}(${u}) `}n&&(t[i]=u)}}return r=r.trim(),n?r=n(t,o?"":r):o&&(r="none"),r}function zs(e,t,n){const{style:r,vars:o,transformOrigin:s}=e;let i=!1,a=!1;for(const l in t){const u=t[l];if(an.has(l)){i=!0;continue}else if(As(l)){o[l]=u;continue}else{const c=mc(u,Ls[l]);l.startsWith("origin")?(a=!0,s[l]=c):r[l]=c}}if(t.transform||(i||n?r.transform=Bm(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=s;r.transformOrigin=`${l} ${u} ${c}`}}const Ws=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Pc(e,t,n){for(const r in t)!he(t[r])&&!Ac(r,n)&&(e[r]=t[r])}function $m({transformTemplate:e},t){return d.useMemo(()=>{const n=Ws();return zs(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Um(e,t){const n=e.style||{},r={};return Pc(r,n,e),Object.assign(r,$m(e,t)),r}function zm(e,t){const n={},r=Um(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Wm={offset:"stroke-dashoffset",array:"stroke-dasharray"},Km={offset:"strokeDashoffset",array:"strokeDasharray"};function Hm(e,t,n=1,r=0,o=!0){e.pathLength=1;const s=o?Wm:Km;e[s.offset]=$.transform(-r);const i=$.transform(t),a=$.transform(n);e[s.array]=`${i} ${a}`}function Rc(e,{attrX:t,attrY:n,attrScale:r,pathLength:o,pathSpacing:s=1,pathOffset:i=0,...a},l,u,c){if(zs(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:p}=e;h.transform&&(p.transform=h.transform,delete h.transform),(p.transform||h.transformOrigin)&&(p.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),p.transform&&(p.transformBox=c?.transformBox??"fill-box",delete h.transformBox),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),o!==void 0&&Hm(h,o,s,i,!1)}const Mc=()=>({...Ws(),attrs:{}}),Dc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Gm(e,t,n,r){const o=d.useMemo(()=>{const s=Mc();return Rc(s,t,Dc(r),e.transformTemplate,e.style),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};Pc(s,e.style,e),o.style={...s,...o.style}}return o}const Ym=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ks(e){return typeof e!="string"||e.includes("-")?!1:!!(Ym.indexOf(e)>-1||/[A-Z]/u.test(e))}function Xm(e,t,n,{latestValues:r},o,s=!1){const a=(Ks(e)?Gm:zm)(t,r,o,e),l=_m(t,typeof e=="string",s),u=e!==d.Fragment?{...l,...a,ref:n}:{},{children:c}=t,h=d.useMemo(()=>he(c)?c.get():c,[c]);return d.createElement(e,{...u,children:h})}function Xi(e){const t=[{},{}];return e?.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Hs(e,t,n,r){if(typeof t=="function"){const[o,s]=Xi(r);t=t(n!==void 0?n:e.custom,o,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[o,s]=Xi(r);t=t(n!==void 0?n:e.custom,o,s)}return t}function fr(e){return he(e)?e.get():e}function qm({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:Zm(n,r,o,e),renderState:t()}}function Zm(e,t,n,r){const o={},s=r(e,{});for(const p in s)o[p]=fr(s[p]);let{initial:i,animate:a}=e;const l=Or(e),u=Ec(e);t&&u&&!l&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||i===!1;const h=c?a:i;if(h&&typeof h!="boolean"&&!_r(h)){const p=Array.isArray(h)?h:[h];for(let m=0;m<p.length;m++){const v=Hs(e,p[m]);if(v){const{transitionEnd:g,transition:y,...x}=v;for(const w in x){let b=x[w];if(Array.isArray(b)){const S=c?b.length-1:0;b=b[S]}b!==null&&(o[w]=b)}for(const w in g)o[w]=g[w]}}}return o}const Nc=e=>(t,n)=>{const r=d.useContext(kr),o=d.useContext(jr),s=()=>qm(e,t,r,o);return n?s():vs(s)};function Gs(e,t,n){const{style:r}=e,o={};for(const s in r)(he(r[s])||t.style&&he(t.style[s])||Ac(s,e)||n?.getValue(s)?.liveStyle!==void 0)&&(o[s]=r[s]);return o}const Jm=Nc({scrapeMotionValuesFromProps:Gs,createRenderState:Ws});function jc(e,t,n){const r=Gs(e,t,n);for(const o in e)if(he(e[o])||he(t[o])){const s=sn.indexOf(o)!==-1?"attr"+o.charAt(0).toUpperCase()+o.substring(1):o;r[s]=e[o]}return r}const Qm=Nc({scrapeMotionValuesFromProps:jc,createRenderState:Mc}),eg=Symbol.for("motionComponentSymbol");function Yt(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function tg(e,t,n){return d.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Yt(n)&&(n.current=r))},[t])}const Ys=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ng="framerAppearId",kc="data-"+Ys(ng),_c=d.createContext({});function rg(e,t,n,r,o){const{visualElement:s}=d.useContext(kr),i=d.useContext(Cc),a=d.useContext(jr),l=d.useContext(Bs).reducedMotion,u=d.useRef(null);r=r||i.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:s,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const c=u.current,h=d.useContext(_c);c&&!c.projection&&o&&(c.type==="html"||c.type==="svg")&&og(u.current,n,o,h);const p=d.useRef(!1);d.useInsertionEffect(()=>{c&&p.current&&c.update(n,a)});const m=n[kc],v=d.useRef(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return Dl(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),v.current&&c.animationState&&c.animationState.animateChanges())}),d.useEffect(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),v.current=!1))}),c}function og(e,t,n,r){const{layoutId:o,layout:s,drag:i,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Oc(e.parent)),e.projection.setOptions({layoutId:o,layout:s,alwaysMeasureLayout:!!i||a&&Yt(a),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}function Oc(e){if(e)return e.options.allowProjection!==!1?e.projection:Oc(e.parent)}function co(e,{forwardMotionProps:t=!1}={},n,r){n&&Nm(n);const o=Ks(e)?Qm:Jm;function s(a,l){let u;const c={...d.useContext(Bs),...a,layoutId:sg(a)},{isStatic:h}=c,p=Im(a),m=o(a,h);if(!h&&ys){ig();const v=ag(c);u=v.MeasureLayout,p.visualElement=rg(e,m,c,r,v.ProjectionNode)}return f.jsxs(kr.Provider,{value:p,children:[u&&p.visualElement?f.jsx(u,{visualElement:p.visualElement,...c}):null,Xm(e,a,tg(m,p.visualElement,l),m,h,t)]})}s.displayName=`motion.${typeof e=="string"?e:`create(${e.displayName??e.name??""})`}`;const i=d.forwardRef(s);return i[eg]=e,i}function sg({layoutId:e}){const t=d.useContext(gs).id;return t&&e!==void 0?t+"-"+e:e}function ig(e,t){d.useContext(Cc).strict}function ag(e){const{drag:t,layout:n}=tn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function lg(e,t){if(typeof Proxy>"u")return co;const n=new Map,r=(s,i)=>co(s,i,e,t),o=(s,i)=>r(s,i);return new Proxy(o,{get:(s,i)=>i==="create"?r:(n.has(i)||n.set(i,co(i,void 0,e,t)),n.get(i))})}function Ic({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function cg({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function ug(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function uo(e){return e===void 0||e===1}function Ko({scale:e,scaleX:t,scaleY:n}){return!uo(e)||!uo(t)||!uo(n)}function Tt(e){return Ko(e)||Lc(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Lc(e){return qi(e.x)||qi(e.y)}function qi(e){return e&&e!=="0%"}function Sr(e,t,n){const r=e-n,o=t*r;return n+o}function Zi(e,t,n,r,o){return o!==void 0&&(e=Sr(e,o,r)),Sr(e,n,r)+t}function Ho(e,t=0,n=1,r,o){e.min=Zi(e.min,t,n,r,o),e.max=Zi(e.max,t,n,r,o)}function Vc(e,{x:t,y:n}){Ho(e.x,t.translate,t.scale,t.originPoint),Ho(e.y,n.translate,n.scale,n.originPoint)}const Ji=.999999999999,Qi=1.0000000000001;function dg(e,t,n,r=!1){const o=n.length;if(!o)return;t.x=t.y=1;let s,i;for(let a=0;a<o;a++){s=n[a],i=s.projectionDelta;const{visualElement:l}=s.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&qt(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,Vc(e,i)),r&&Tt(s.latestValues)&&qt(e,s.latestValues))}t.x<Qi&&t.x>Ji&&(t.x=1),t.y<Qi&&t.y>Ji&&(t.y=1)}function Xt(e,t){e.min=e.min+t,e.max=e.max+t}function ea(e,t,n,r,o=.5){const s=Q(e.min,e.max,o);Ho(e,t,n,s,r)}function qt(e,t){ea(e.x,t.x,t.scaleX,t.scale,t.originX),ea(e.y,t.y,t.scaleY,t.scale,t.originY)}function Fc(e,t){return Ic(ug(e.getBoundingClientRect(),t))}function fg(e,t,n){const r=Fc(e,n),{scroll:o}=t;return o&&(Xt(r.x,o.offset.x),Xt(r.y,o.offset.y)),r}const ta=()=>({translate:0,scale:1,origin:0,originPoint:0}),Zt=()=>({x:ta(),y:ta()}),na=()=>({min:0,max:0}),ne=()=>({x:na(),y:na()}),Go={current:null},Bc={current:!1};function hg(){if(Bc.current=!0,!!ys)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Go.current=e.matches;e.addEventListener("change",t),t()}else Go.current=!1}const pg=new WeakMap;function mg(e,t,n){for(const r in t){const o=t[r],s=n[r];if(he(o))e.addValue(r,o);else if(he(s))e.addValue(r,en(o,{owner:e}));else if(s!==o)if(e.hasValue(r)){const i=e.getValue(r);i.liveStyle===!0?i.jump(o):i.hasAnimated||i.set(o)}else{const i=e.getStaticValue(r);e.addValue(r,en(i!==void 0?i:o,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const ra=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class gg{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:o,blockInitialAnimation:s,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Os,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const p=xe.now();this.renderScheduledAt<p&&(this.renderScheduledAt=p,q.render(this.render,!1,!0))};const{latestValues:l,renderState:u}=i;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=o,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=Or(n),this.isVariantNode=Ec(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const p in h){const m=h[p];l[p]!==void 0&&he(m)&&m.set(l[p])}}mount(t){this.current=t,pg.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Bc.current||hg(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Go.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ht(this.notifyUpdate),ht(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=an.has(t);r&&this.onBindTransform&&this.onBindTransform();const o=n.on("change",i=>{this.latestValues[t]=i,this.props.onUpdate&&q.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{o(),s&&s(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in tn){const n=tn[t];if(!n)continue;const{isEnabled:r,Feature:o}=n;if(!this.features[t]&&o&&r(this.props)&&(this.features[t]=new o(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ne()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<ra.length;r++){const o=ra[r];this.propEventSubscriptions[o]&&(this.propEventSubscriptions[o](),delete this.propEventSubscriptions[o]);const s="on"+o,i=t[s];i&&(this.propEventSubscriptions[o]=this.on(o,i))}this.prevMotionValues=mg(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=en(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(Nl(r)||kl(r))?r=parseFloat(r):!Am(r)&&pt.test(n)&&(r=pc(t,n)),this.setBaseTarget(t,he(r)?r.get():r)),he(r)?r.get():r}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const s=Hs(this.props,n,this.presenceContext?.custom);s&&(r=s[t])}if(n&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!he(o)?o:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Cs),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}scheduleRenderMicrotask(){Vs.render(this.render)}}class $c extends gg{constructor(){super(...arguments),this.KeyframeResolver=pm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;he(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Uc(e,{style:t,vars:n},r,o){const s=e.style;let i;for(i in t)s[i]=t[i];o?.applyProjectionStyles(s,r);for(i in n)s.setProperty(i,n[i])}function vg(e){return window.getComputedStyle(e)}class yg extends $c{constructor(){super(...arguments),this.type="html",this.renderInstance=Uc}readValueFromInstance(t,n){if(an.has(n))return this.projection?.isProjecting?Vo(n):kp(t,n);{const r=vg(t),o=(As(n)?r.getPropertyValue(n):r[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Fc(t,n)}build(t,n,r){zs(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return Gs(t,n,r)}}const zc=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function xg(e,t,n,r){Uc(e,t,void 0,r);for(const o in t.attrs)e.setAttribute(zc.has(o)?o:Ys(o),t.attrs[o])}class bg extends $c{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ne}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(an.has(n)){const r=hc(n);return r&&r.default||0}return n=zc.has(n)?n:Ys(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return jc(t,n,r)}build(t,n,r){Rc(t,n,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,n,r,o){xg(t,n,r,o)}mount(t){this.isSVGTag=Dc(t.tagName),super.mount(t)}}const wg=(e,t)=>Ks(e)?new bg(t):new yg(t,{allowProjection:e!==d.Fragment});function kn(e,t,n){const r=e.getProps();return Hs(r,t,n!==void 0?n:r.custom,e)}const Yo=e=>Array.isArray(e);function Sg(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,en(n))}function Cg(e){return Yo(e)?e[e.length-1]||0:e}function Tg(e,t){const n=kn(e,t);let{transitionEnd:r={},transition:o={},...s}=n||{};s={...s,...r};for(const i in s){const a=Cg(s[i]);Sg(e,i,a)}}function Eg(e){return!!(he(e)&&e.add)}function Xo(e,t){const n=e.getValue("willChange");if(Eg(n))return n.add(t);if(!n&&rt.WillChange){const r=new rt.WillChange("auto");e.addValue("willChange",r),r.add(t)}}function Wc(e){return e.props[kc]}const Ag=e=>e!==null;function Pg(e,{repeat:t,repeatType:n="loop"},r){const o=e.filter(Ag),s=t&&n!=="loop"&&t%2===1?0:o.length-1;return o[s]}const Rg={type:"spring",stiffness:500,damping:25,restSpeed:10},Mg=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Dg={type:"keyframes",duration:.8},Ng={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},jg=(e,{keyframes:t})=>t.length>2?Dg:an.has(e)?e.startsWith("scale")?Mg(t[1]):Rg:Ng;function kg({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:o,repeat:s,repeatType:i,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Xs=(e,t,n,r={},o,s)=>i=>{const a=Is(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-Ge(l);const c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:p=>{t.set(p),a.onUpdate&&a.onUpdate(p)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:o};kg(a)||Object.assign(c,jg(e,c)),c.duration&&(c.duration=Ge(c.duration)),c.repeatDelay&&(c.repeatDelay=Ge(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(zo(c),c.delay===0&&(h=!0)),(rt.instantAnimations||rt.skipAnimations)&&(h=!0,zo(c),c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!s&&t.get()!==void 0){const p=Pg(c.keyframes,a);if(p!==void 0){q.update(()=>{c.onUpdate(p),c.onComplete()});return}}return a.isSync?new _s(c):new nm(c)};function _g({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Kc(e,t,{delay:n=0,transitionOverride:r,type:o}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:i,...a}=t;r&&(s=r);const l=[],u=o&&e.animationState&&e.animationState.getState()[o];for(const c in a){const h=e.getValue(c,e.latestValues[c]??null),p=a[c];if(p===void 0||u&&_g(u,c))continue;const m={delay:n,...Is(s||{},c)},v=h.get();if(v!==void 0&&!h.isAnimating&&!Array.isArray(p)&&p===v&&!m.velocity)continue;let g=!1;if(window.MotionHandoffAnimation){const x=Wc(e);if(x){const w=window.MotionHandoffAnimation(x,c,q);w!==null&&(m.startTime=w,g=!0)}}Xo(e,c),h.start(Xs(c,h,p,e.shouldReduceMotion&&uc.has(c)?{type:!1}:m,e,g));const y=h.animation;y&&l.push(y)}return i&&Promise.all(l).then(()=>{q.update(()=>{i&&Tg(e,i)})}),l}function qo(e,t,n={}){const r=kn(e,t,n.type==="exit"?e.presenceContext?.custom:void 0);let{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const s=r?()=>Promise.all(Kc(e,r,n)):()=>Promise.resolve(),i=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:h}=o;return Og(e,t,l,u,c,h,n)}:()=>Promise.resolve(),{when:a}=o;if(a){const[l,u]=a==="beforeChildren"?[s,i]:[i,s];return l().then(()=>u())}else return Promise.all([s(),i(n.delay)])}function Og(e,t,n=0,r=0,o=0,s=1,i){const a=[],l=e.variantChildren.size,u=(l-1)*o,c=typeof r=="function",h=c?p=>r(p,l):s===1?(p=0)=>p*o:(p=0)=>u-p*o;return Array.from(e.variantChildren).sort(Ig).forEach((p,m)=>{p.notify("AnimationStart",t),a.push(qo(p,t,{...i,delay:n+(c?0:r)+h(m)}).then(()=>p.notify("AnimationComplete",t)))}),Promise.all(a)}function Ig(e,t){return e.sortNodePosition(t)}function Lg(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const o=t.map(s=>qo(e,s,n));r=Promise.all(o)}else if(typeof t=="string")r=qo(e,t,n);else{const o=typeof t=="function"?kn(e,t,n.custom):t;r=Promise.all(Kc(e,o,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}function Hc(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const Vg=Us.length;function Gc(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?Gc(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<Vg;n++){const r=Us[n],o=e.props[r];(Nn(o)||o===!1)&&(t[r]=o)}return t}const Fg=[...$s].reverse(),Bg=$s.length;function $g(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Lg(e,n,r)))}function Ug(e){let t=$g(e),n=oa(),r=!0;const o=l=>(u,c)=>{const h=kn(e,c,l==="exit"?e.presenceContext?.custom:void 0);if(h){const{transition:p,transitionEnd:m,...v}=h;u={...u,...v,...m}}return u};function s(l){t=l(e)}function i(l){const{props:u}=e,c=Gc(e.parent)||{},h=[],p=new Set;let m={},v=1/0;for(let y=0;y<Bg;y++){const x=Fg[y],w=n[x],b=u[x]!==void 0?u[x]:c[x],S=Nn(b),C=x===l?w.isActive:null;C===!1&&(v=y);let R=b===c[x]&&b!==u[x]&&S;if(R&&r&&e.manuallyAnimateOnMount&&(R=!1),w.protectedKeys={...m},!w.isActive&&C===null||!b&&!w.prevProp||_r(b)||typeof b=="boolean")continue;const T=zg(w.prevProp,b);let P=T||x===l&&w.isActive&&!R&&S||y>v&&S,A=!1;const I=Array.isArray(b)?b:[b];let F=I.reduce(o(x),{});C===!1&&(F={});const{prevResolvedValues:O={}}=w,E={...O,...F},M=D=>{P=!0,p.has(D)&&(A=!0,p.delete(D)),w.needsAnimating[D]=!0;const j=e.getValue(D);j&&(j.liveStyle=!1)};for(const D in E){const j=F[D],N=O[D];if(m.hasOwnProperty(D))continue;let G=!1;Yo(j)&&Yo(N)?G=!Hc(j,N):G=j!==N,G?j!=null?M(D):p.add(D):j!==void 0&&p.has(D)?M(D):w.protectedKeys[D]=!0}w.prevProp=b,w.prevResolvedValues=F,w.isActive&&(m={...m,...F}),r&&e.blockInitialAnimation&&(P=!1),P&&(!(R&&T)||A)&&h.push(...I.map(D=>({animation:D,options:{type:x}})))}if(p.size){const y={};if(typeof u.initial!="boolean"){const x=kn(e,Array.isArray(u.initial)?u.initial[0]:u.initial);x&&x.transition&&(y.transition=x.transition)}p.forEach(x=>{const w=e.getBaseTarget(x),b=e.getValue(x);b&&(b.liveStyle=!0),y[x]=w??null}),h.push({animation:y})}let g=!!h.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(h):Promise.resolve()}function a(l,u){if(n[l].isActive===u)return Promise.resolve();e.variantChildren?.forEach(h=>h.animationState?.setActive(l,u)),n[l].isActive=u;const c=i(l);for(const h in n)n[h].protectedKeys={};return c}return{animateChanges:i,setActive:a,setAnimateFunction:s,getState:()=>n,reset:()=>{n=oa(),r=!0}}}function zg(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Hc(t,e):!1}function Ct(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function oa(){return{animate:Ct(!0),whileInView:Ct(),whileHover:Ct(),whileTap:Ct(),whileDrag:Ct(),whileFocus:Ct(),exit:Ct()}}class vt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Wg extends vt{constructor(t){super(t),t.animationState||(t.animationState=Ug(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();_r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Kg=0;class Hg extends vt{constructor(){super(...arguments),this.id=Kg++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const o=this.node.animationState.setActive("exit",!t);n&&!t&&o.then(()=>{n(this.id)})}mount(){const{register:t,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const Gg={animation:{Feature:Wg},exit:{Feature:Hg}};function _n(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function Wn(e){return{point:{x:e.pageX,y:e.pageY}}}const Yg=e=>t=>Fs(t)&&e(t,Wn(t));function En(e,t,n,r){return _n(e,t,Yg(n),r)}const Yc=1e-4,Xg=1-Yc,qg=1+Yc,Xc=.01,Zg=0-Xc,Jg=0+Xc;function me(e){return e.max-e.min}function Qg(e,t,n){return Math.abs(e-t)<=n}function sa(e,t,n,r=.5){e.origin=r,e.originPoint=Q(t.min,t.max,e.origin),e.scale=me(n)/me(t),e.translate=Q(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Xg&&e.scale<=qg||isNaN(e.scale))&&(e.scale=1),(e.translate>=Zg&&e.translate<=Jg||isNaN(e.translate))&&(e.translate=0)}function An(e,t,n,r){sa(e.x,t.x,n.x,r?r.originX:void 0),sa(e.y,t.y,n.y,r?r.originY:void 0)}function ia(e,t,n){e.min=n.min+t.min,e.max=e.min+me(t)}function ev(e,t,n){ia(e.x,t.x,n.x),ia(e.y,t.y,n.y)}function aa(e,t,n){e.min=t.min-n.min,e.max=e.min+me(t)}function Pn(e,t,n){aa(e.x,t.x,n.x),aa(e.y,t.y,n.y)}function Me(e){return[e("x"),e("y")]}const qc=({current:e})=>e?e.ownerDocument.defaultView:null,la=(e,t)=>Math.abs(e-t);function tv(e,t){const n=la(e.x,t.x),r=la(e.y,t.y);return Math.sqrt(n**2+r**2)}class Zc{constructor(t,n,{transformPagePoint:r,contextWindow:o=window,dragSnapToOrigin:s=!1,distanceThreshold:i=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const p=ho(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,v=tv(p.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!v)return;const{point:g}=p,{timestamp:y}=ce;this.history.push({...g,timestamp:y});const{onStart:x,onMove:w}=this.handlers;m||(x&&x(this.lastMoveEvent,p),this.startEvent=this.lastMoveEvent),w&&w(this.lastMoveEvent,p)},this.handlePointerMove=(p,m)=>{this.lastMoveEvent=p,this.lastMoveEventInfo=fo(m,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(p,m)=>{this.end();const{onEnd:v,onSessionEnd:g,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ho(p.type==="pointercancel"?this.lastMoveEventInfo:fo(m,this.transformPagePoint),this.history);this.startEvent&&v&&v(p,x),g&&g(p,x)},!Fs(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.distanceThreshold=i,this.contextWindow=o||window;const a=Wn(t),l=fo(a,this.transformPagePoint),{point:u}=l,{timestamp:c}=ce;this.history=[{...u,timestamp:c}];const{onSessionStart:h}=n;h&&h(t,ho(l,this.history)),this.removeListeners=$n(En(this.contextWindow,"pointermove",this.handlePointerMove),En(this.contextWindow,"pointerup",this.handlePointerUp),En(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ht(this.updatePoint)}}function fo(e,t){return t?{point:t(e.point)}:e}function ca(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ho({point:e},t){return{point:e,delta:ca(e,Jc(t)),offset:ca(e,nv(t)),velocity:rv(t,.1)}}function nv(e){return e[0]}function Jc(e){return e[e.length-1]}function rv(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const o=Jc(e);for(;n>=0&&(r=e[n],!(o.timestamp-r.timestamp>Ge(t)));)n--;if(!r)return{x:0,y:0};const s=Ye(o.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const i={x:(o.x-r.x)/s,y:(o.y-r.y)/s};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function ov(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Q(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Q(n,e,r.max):Math.min(e,n)),e}function ua(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function sv(e,{top:t,left:n,bottom:r,right:o}){return{x:ua(e.x,n,o),y:ua(e.y,t,r)}}function da(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function iv(e,t){return{x:da(e.x,t.x),y:da(e.y,t.y)}}function av(e,t){let n=.5;const r=me(e),o=me(t);return o>r?n=Rn(t.min,t.max-r,e.min):r>o&&(n=Rn(e.min,e.max-o,t.min)),nt(0,1,n)}function lv(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Zo=.35;function cv(e=Zo){return e===!1?e=0:e===!0&&(e=Zo),{x:fa(e,"left","right"),y:fa(e,"top","bottom")}}function fa(e,t,n){return{min:ha(e,t),max:ha(e,n)}}function ha(e,t){return typeof e=="number"?e:e[t]||0}const uv=new WeakMap;class dv{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ne(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:n=!1,distanceThreshold:r}={}){const{presenceContext:o}=this.visualElement;if(o&&o.isPresent===!1)return;const s=h=>{const{dragSnapToOrigin:p}=this.getProps();p?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Wn(h).point)},i=(h,p)=>{const{drag:m,dragPropagation:v,onDragStart:g}=this.getProps();if(m&&!v&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ym(m),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=p,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Me(x=>{let w=this.getAxisMotionValue(x).get()||0;if(Xe.test(w)){const{projection:b}=this.visualElement;if(b&&b.layout){const S=b.layout.layoutBox[x];S&&(w=me(S)*(parseFloat(w)/100))}}this.originPoint[x]=w}),g&&q.postRender(()=>g(h,p)),Xo(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},a=(h,p)=>{this.latestPointerEvent=h,this.latestPanInfo=p;const{dragPropagation:m,dragDirectionLock:v,onDirectionLock:g,onDrag:y}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:x}=p;if(v&&this.currentDirection===null){this.currentDirection=fv(x),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",p.point,x),this.updateAxis("y",p.point,x),this.visualElement.render(),y&&y(h,p)},l=(h,p)=>{this.latestPointerEvent=h,this.latestPanInfo=p,this.stop(h,p),this.latestPointerEvent=null,this.latestPanInfo=null},u=()=>Me(h=>this.getAnimationState(h)==="paused"&&this.getAxisMotionValue(h).animation?.play()),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Zc(t,{onSessionStart:s,onStart:i,onMove:a,onSessionEnd:l,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,distanceThreshold:r,contextWindow:qc(this.visualElement)})}stop(t,n){const r=t||this.latestPointerEvent,o=n||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!o||!r)return;const{velocity:i}=o;this.startAnimation(i);const{onDragEnd:a}=this.getProps();a&&q.postRender(()=>a(r,o))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:o}=this.getProps();if(!r||!rr(t,o,this.currentDirection))return;const s=this.getAxisMotionValue(t);let i=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(i=ov(i,this.constraints[t],this.elastic[t])),s.set(i)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,o=this.constraints;t&&Yt(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=sv(r.layoutBox,t):this.constraints=!1,this.elastic=cv(n),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Me(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=lv(r.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Yt(t))return!1;const r=t.current,{projection:o}=this.visualElement;if(!o||!o.layout)return!1;const s=fg(r,o.root,this.visualElement.getTransformPagePoint());let i=iv(o.layout.layoutBox,s);if(n){const a=n(cg(i));this.hasMutatedConstraints=!!a,a&&(i=Ic(a))}return i}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:o,dragTransition:s,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Me(c=>{if(!rr(c,n,this.currentDirection))return;let h=l&&l[c]||{};i&&(h={min:0,max:0});const p=o?200:1e6,m=o?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...s,...h};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Xo(this.visualElement,t),r.start(Xs(t,r,0,n,this.visualElement,!1))}stopAnimation(){Me(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Me(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),o=r[n];return o||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Me(n=>{const{drag:r}=this.getProps();if(!rr(n,r,this.currentDirection))return;const{projection:o}=this.visualElement,s=this.getAxisMotionValue(n);if(o&&o.layout){const{min:i,max:a}=o.layout.layoutBox[n];s.set(t[n]-Q(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Yt(n)||!r||!this.constraints)return;this.stopAnimation();const o={x:0,y:0};Me(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const l=a.get();o[i]=av({min:l,max:l},this.constraints[i])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Me(i=>{if(!rr(i,t,null))return;const a=this.getAxisMotionValue(i),{min:l,max:u}=this.constraints[i];a.set(Q(l,u,o[i]))})}addListeners(){if(!this.visualElement.current)return;uv.set(this.visualElement,this);const t=this.visualElement.current,n=En(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Yt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:o}=this.visualElement,s=o.addEventListener("measure",r);o&&!o.layout&&(o.root&&o.root.updateScroll(),o.updateLayout()),q.read(r);const i=_n(window,"resize",()=>this.scalePositionWithinConstraints()),a=o.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Me(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{i(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:o=!1,dragConstraints:s=!1,dragElastic:i=Zo,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:o,dragConstraints:s,dragElastic:i,dragMomentum:a}}}function rr(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function fv(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class hv extends vt{constructor(t){super(t),this.removeGroupControls=Ne,this.removeListeners=Ne,this.controls=new dv(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ne}unmount(){this.removeGroupControls(),this.removeListeners()}}const pa=e=>(t,n)=>{e&&q.postRender(()=>e(t,n))};class pv extends vt{constructor(){super(...arguments),this.removePointerDownListener=Ne}onPointerDown(t){this.session=new Zc(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:qc(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:o}=this.node.getProps();return{onSessionStart:pa(t),onStart:pa(n),onMove:r,onEnd:(s,i)=>{delete this.session,o&&q.postRender(()=>o(s,i))}}}mount(){this.removePointerDownListener=En(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const hr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ma(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const xn={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if($.test(e))e=parseFloat(e);else return e;const n=ma(e,t.target.x),r=ma(e,t.target.y);return`${n}% ${r}%`}},mv={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,o=pt.parse(e);if(o.length>5)return r;const s=pt.createTransformer(e),i=typeof o[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;o[0+i]/=a,o[1+i]/=l;const u=Q(a,l,.5);return typeof o[2+i]=="number"&&(o[2+i]/=u),typeof o[3+i]=="number"&&(o[3+i]/=u),s(o)}};let ga=!1;class gv extends d.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:o}=this.props,{projection:s}=t;Lm(vv),s&&(n.group&&n.group.add(s),r&&r.register&&o&&r.register(s),ga&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),hr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:o,isPresent:s}=this.props,{projection:i}=r;return i&&(i.isPresent=s,ga=!0,o||t.layoutDependency!==n||n===void 0||t.isPresent!==s?i.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?i.promote():i.relegate()||q.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Vs.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:o}=t;o&&(o.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(o),r&&r.deregister&&r.deregister(o))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Qc(e){const[t,n]=wc(),r=d.useContext(gs);return f.jsx(gv,{...e,layoutGroup:r,switchLayoutGroup:d.useContext(_c),isPresent:t,safeToRemove:n})}const vv={borderRadius:{...xn,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xn,borderTopRightRadius:xn,borderBottomLeftRadius:xn,borderBottomRightRadius:xn,boxShadow:mv};function yv(e,t,n){const r=he(e)?e:en(e);return r.start(Xs("",r,t,n)),r.animation}const xv=(e,t)=>e.depth-t.depth;class bv{constructor(){this.children=[],this.isDirty=!1}add(t){xs(this.children,t),this.isDirty=!0}remove(t){bs(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(xv),this.isDirty=!1,this.children.forEach(t)}}function wv(e,t){const n=xe.now(),r=({timestamp:o})=>{const s=o-n;s>=t&&(ht(r),e(s-t))};return q.setup(r,!0),()=>ht(r)}const eu=["TopLeft","TopRight","BottomLeft","BottomRight"],Sv=eu.length,va=e=>typeof e=="string"?parseFloat(e):e,ya=e=>typeof e=="number"||$.test(e);function Cv(e,t,n,r,o,s){o?(e.opacity=Q(0,n.opacity??1,Tv(r)),e.opacityExit=Q(t.opacity??1,0,Ev(r))):s&&(e.opacity=Q(t.opacity??1,n.opacity??1,r));for(let i=0;i<Sv;i++){const a=`border${eu[i]}Radius`;let l=xa(t,a),u=xa(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||ya(l)===ya(u)?(e[a]=Math.max(Q(va(l),va(u),r),0),(Xe.test(u)||Xe.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=Q(t.rotate||0,n.rotate||0,r))}function xa(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Tv=tu(0,.5,$l),Ev=tu(.5,.95,Ne);function tu(e,t,n){return r=>r<e?0:r>t?1:n(Rn(e,t,r))}function ba(e,t){e.min=t.min,e.max=t.max}function Pe(e,t){ba(e.x,t.x),ba(e.y,t.y)}function wa(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Sa(e,t,n,r,o){return e-=t,e=Sr(e,1/n,r),o!==void 0&&(e=Sr(e,1/o,r)),e}function Av(e,t=0,n=1,r=.5,o,s=e,i=e){if(Xe.test(t)&&(t=parseFloat(t),t=Q(i.min,i.max,t/100)-i.min),typeof t!="number")return;let a=Q(s.min,s.max,r);e===s&&(a-=t),e.min=Sa(e.min,t,n,a,o),e.max=Sa(e.max,t,n,a,o)}function Ca(e,t,[n,r,o],s,i){Av(e,t[n],t[r],t[o],t.scale,s,i)}const Pv=["x","scaleX","originX"],Rv=["y","scaleY","originY"];function Ta(e,t,n,r){Ca(e.x,t,Pv,n?n.x:void 0,r?r.x:void 0),Ca(e.y,t,Rv,n?n.y:void 0,r?r.y:void 0)}function Ea(e){return e.translate===0&&e.scale===1}function nu(e){return Ea(e.x)&&Ea(e.y)}function Aa(e,t){return e.min===t.min&&e.max===t.max}function Mv(e,t){return Aa(e.x,t.x)&&Aa(e.y,t.y)}function Pa(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function ru(e,t){return Pa(e.x,t.x)&&Pa(e.y,t.y)}function Ra(e){return me(e.x)/me(e.y)}function Ma(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Dv{constructor(){this.members=[]}add(t){xs(this.members,t),t.scheduleRender()}remove(t){if(bs(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(o=>t===o);if(n===0)return!1;let r;for(let o=n;o>=0;o--){const s=this.members[o];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:o}=t.options;o===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Nv(e,t,n){let r="";const o=e.x.translate/t.x,s=e.y.translate/t.y,i=n?.z||0;if((o||s||i)&&(r=`translate3d(${o}px, ${s}px, ${i}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:h,rotateY:p,skewX:m,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),h&&(r+=`rotateX(${h}deg) `),p&&(r+=`rotateY(${p}deg) `),m&&(r+=`skewX(${m}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const po=["","X","Y","Z"],jv=1e3;let kv=0;function mo(e,t,n,r){const{latestValues:o}=t;o[e]&&(n[e]=o[e],t.setStaticValue(e,0),r&&(r[e]=0))}function ou(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Wc(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:o,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",q,!(o||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&ou(r)}function su({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:o}){return class{constructor(i={},a=t?.()){this.id=kv++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Iv),this.nodes.forEach(Bv),this.nodes.forEach($v),this.nodes.forEach(Lv)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new bv)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Cs),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const l=this.eventHandlers.get(i);l&&l.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i){if(this.instance)return;this.isSVG=bc(i)&&!Tm(i),this.instance=i;const{layoutId:a,layout:l,visualElement:u}=this.options;if(u&&!u.current&&u.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),e){let c,h=0;const p=()=>this.root.updateBlockedByResize=!1;q.read(()=>{h=window.innerWidth}),e(i,()=>{const m=window.innerWidth;m!==h&&(h=m,this.root.updateBlockedByResize=!0,c&&c(),c=wv(p,250),hr.hasAnimatedSinceResize&&(hr.hasAnimatedSinceResize=!1,this.nodes.forEach(ja)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||l)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:h,hasRelativeLayoutChanged:p,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||u.getDefaultTransition()||Hv,{onLayoutAnimationStart:g,onLayoutAnimationComplete:y}=u.getProps(),x=!this.targetLayout||!ru(this.targetLayout,m),w=!h&&p;if(this.options.layoutRoot||this.resumeFrom||w||h&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const b={...Is(v,"layout"),onPlay:g,onComplete:y};(u.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(c,w)}else h||ja(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ht(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Uv),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ou(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Da);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Na);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Fv),this.nodes.forEach(_v),this.nodes.forEach(Ov)):this.nodes.forEach(Na),this.clearAllSnapshots();const a=xe.now();ce.delta=nt(0,1e3/60,a-ce.timestamp),ce.timestamp=a,ce.isProcessing=!0,ro.update.process(ce),ro.preRender.process(ce),ro.render.process(ce),ce.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Vs.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Vv),this.sharedNodes.forEach(zv)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!me(this.snapshot.measuredBox.x)&&!me(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ne(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a&&this.instance){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!o)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!nu(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;i&&this.instance&&(a||Tt(this.latestValues)||c)&&(o(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return i&&(l=this.removeTransform(l)),Gv(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:i}=this.options;if(!i)return ne();const a=i.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Yv))){const{scroll:u}=this.root;u&&(Xt(a.x,u.offset.x),Xt(a.y,u.offset.y))}return a}removeElementScroll(i){const a=ne();if(Pe(a,i),this.scroll?.wasRoot)return a;for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:h}=u;u!==this.root&&c&&h.layoutScroll&&(c.wasRoot&&Pe(a,i),Xt(a.x,c.offset.x),Xt(a.y,c.offset.y))}return a}applyTransform(i,a=!1){const l=ne();Pe(l,i);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&qt(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Tt(c.latestValues)&&qt(l,c.latestValues)}return Tt(this.latestValues)&&qt(l,this.latestValues),l}removeTransform(i){const a=ne();Pe(a,i);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Tt(u.latestValues))continue;Ko(u.latestValues)&&u.updateSnapshot();const c=ne(),h=u.measurePageBox();Pe(c,h),Ta(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Tt(this.latestValues)&&Ta(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ce.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(i||l&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:c,layoutId:h}=this.options;if(!(!this.layout||!(c||h))){if(this.resolvedRelativeTargetAt=ce.timestamp,!this.targetDelta&&!this.relativeTarget){const p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Pn(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),Pe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=ne(),this.targetWithTransforms=ne()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ev(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Pe(this.target,this.layout.layoutBox),Vc(this.target,this.targetDelta)):Pe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ne(),this.relativeTargetOrigin=ne(),Pn(this.relativeTargetOrigin,this.target,p.target),Pe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ko(this.parent.latestValues)||Lc(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const i=this.getLead(),a=!!this.resumingFrom||this!==i;let l=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===ce.timestamp&&(l=!1),l)return;const{layout:u,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||c))return;Pe(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,p=this.treeScale.y;dg(this.layoutCorrected,this.treeScale,this.path,a),i.layout&&!i.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(i.target=i.layout.layoutBox,i.targetWithTransforms=ne());const{target:m}=i;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(wa(this.prevProjectionDelta.x,this.projectionDelta.x),wa(this.prevProjectionDelta.y,this.projectionDelta.y)),An(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==p||!Ma(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ma(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){if(this.options.visualElement?.scheduleRender(),i){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Zt(),this.projectionDelta=Zt(),this.projectionDeltaWithTransform=Zt()}setAnimationOrigin(i,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},h=Zt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=ne(),m=l?l.source:void 0,v=this.layout?this.layout.source:void 0,g=m!==v,y=this.getStack(),x=!y||y.members.length<=1,w=!!(g&&!x&&this.options.crossfade===!0&&!this.path.some(Kv));this.animationProgress=0;let b;this.mixTargetDelta=S=>{const C=S/1e3;ka(h.x,i.x,C),ka(h.y,i.y,C),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Pn(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Wv(this.relativeTarget,this.relativeTargetOrigin,p,C),b&&Mv(this.relativeTarget,b)&&(this.isProjectionDirty=!1),b||(b=ne()),Pe(b,this.relativeTarget)),g&&(this.animationValues=c,Cv(c,u,this.latestValues,C,w,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=C},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ht(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{hr.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=en(0)),this.currentAnimation=yv(this.motionValue,[0,1e3],{...i,velocity:0,isSync:!0,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onStop:()=>{},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(jv),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=i;if(!(!a||!l||!u)){if(this!==i&&this.layout&&u&&iu(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ne();const h=me(this.layout.layoutBox.x);l.x.min=i.target.x.min,l.x.max=l.x.min+h;const p=me(this.layout.layoutBox.y);l.y.min=i.target.y.min,l.y.max=l.y.min+p}Pe(a,l),qt(a,c),An(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new Dv),this.sharedNodes.get(i).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){const{layoutId:i}=this.options;return i?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:i}=this.options;return i?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:l}=i;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&mo("z",i,u,this.animationValues);for(let c=0;c<po.length;c++)mo(`rotate${po[c]}`,i,u,this.animationValues),mo(`skew${po[c]}`,i,u,this.animationValues);i.render();for(const c in u)i.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);i.scheduleRender()}applyProjectionStyles(i,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){i.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,i.visibility="",i.opacity="",i.pointerEvents=fr(a?.pointerEvents)||"",i.transform=l?l(this.latestValues,""):"none";return}const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){this.options.layoutId&&(i.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,i.pointerEvents=fr(a?.pointerEvents)||""),this.hasProjected&&!Tt(this.latestValues)&&(i.transform=l?l({},""):"none",this.hasProjected=!1);return}i.visibility="";const c=u.animationValues||u.latestValues;this.applyTransformsToTarget();let h=Nv(this.projectionDeltaWithTransform,this.treeScale,c);l&&(h=l(c,h)),i.transform=h;const{x:p,y:m}=this.projectionDelta;i.transformOrigin=`${p.origin*100}% ${m.origin*100}% 0`,u.animationValues?i.opacity=u===this?c.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:c.opacityExit:i.opacity=u===this?c.opacity!==void 0?c.opacity:"":c.opacityExit!==void 0?c.opacityExit:0;for(const v in jn){if(c[v]===void 0)continue;const{correct:g,applyTo:y,isCSSVariable:x}=jn[v],w=h==="none"?c[v]:g(c[v],u);if(y){const b=y.length;for(let S=0;S<b;S++)i[y[S]]=w}else x?this.options.visualElement.renderState.vars[v]=w:i[v]=w}this.options.layoutId&&(i.pointerEvents=u===this?fr(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>i.currentAnimation?.stop()),this.root.nodes.forEach(Da),this.root.sharedNodes.clear()}}}function _v(e){e.updateLayout()}function Ov(e){const t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=e.layout,{animationType:o}=e.options,s=t.source!==e.layout.source;o==="size"?Me(c=>{const h=s?t.measuredBox[c]:t.layoutBox[c],p=me(h);h.min=n[c].min,h.max=h.min+p}):iu(o,t.layoutBox,n)&&Me(c=>{const h=s?t.measuredBox[c]:t.layoutBox[c],p=me(n[c]);h.max=h.min+p,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+p)});const i=Zt();An(i,n,t.layoutBox);const a=Zt();s?An(a,e.applyTransform(r,!0),t.measuredBox):An(a,n,t.layoutBox);const l=!nu(i);let u=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:h,layout:p}=c;if(h&&p){const m=ne();Pn(m,t.layoutBox,h.layoutBox);const v=ne();Pn(v,n,p.layoutBox),ru(m,v)||(u=!0),c.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=m,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:i,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function Iv(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Lv(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Vv(e){e.clearSnapshot()}function Da(e){e.clearMeasurements()}function Na(e){e.isLayoutDirty=!1}function Fv(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function ja(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Bv(e){e.resolveTargetDelta()}function $v(e){e.calcProjection()}function Uv(e){e.resetSkewAndRotation()}function zv(e){e.removeLeadSnapshot()}function ka(e,t,n){e.translate=Q(t.translate,0,n),e.scale=Q(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function _a(e,t,n,r){e.min=Q(t.min,n.min,r),e.max=Q(t.max,n.max,r)}function Wv(e,t,n,r){_a(e.x,t.x,n.x,r),_a(e.y,t.y,n.y,r)}function Kv(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Hv={duration:.45,ease:[.4,0,.1,1]},Oa=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Ia=Oa("applewebkit/")&&!Oa("chrome/")?Math.round:Ne;function La(e){e.min=Ia(e.min),e.max=Ia(e.max)}function Gv(e){La(e.x),La(e.y)}function iu(e,t,n){return e==="position"||e==="preserve-aspect"&&!Qg(Ra(t),Ra(n),.2)}function Yv(e){return e!==e.root&&e.scroll?.wasRoot}const Xv=su({attachResizeListener:(e,t)=>_n(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),go={current:void 0},au=su({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!go.current){const e=new Xv({});e.mount(window),e.setOptions({layoutScroll:!0}),go.current=e}return go.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),qv={pan:{Feature:pv},drag:{Feature:hv,ProjectionNode:au,MeasureLayout:Qc}};function Va(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const o="onHover"+n,s=r[o];s&&q.postRender(()=>s(t,Wn(t)))}class Zv extends vt{mount(){const{current:t}=this.node;t&&(this.unmount=xm(t,(n,r)=>(Va(this.node,r,"Start"),o=>Va(this.node,o,"End"))))}unmount(){}}class Jv extends vt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=$n(_n(this.node.current,"focus",()=>this.onFocus()),_n(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Fa(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const o="onTap"+(n==="End"?"":n),s=r[o];s&&q.postRender(()=>s(t,Wn(t)))}class Qv extends vt{mount(){const{current:t}=this.node;t&&(this.unmount=Cm(t,(n,r)=>(Fa(this.node,r,"Start"),(o,{success:s})=>Fa(this.node,o,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Jo=new WeakMap,vo=new WeakMap,ey=e=>{const t=Jo.get(e.target);t&&t(e)},ty=e=>{e.forEach(ey)};function ny({root:e,...t}){const n=e||document;vo.has(n)||vo.set(n,{});const r=vo.get(n),o=JSON.stringify(t);return r[o]||(r[o]=new IntersectionObserver(ty,{root:e,...t})),r[o]}function ry(e,t,n){const r=ny(t);return Jo.set(e,n),r.observe(e),()=>{Jo.delete(e),r.unobserve(e)}}const oy={some:0,all:1};class sy extends vt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:o="some",once:s}=t,i={root:n?n.current:void 0,rootMargin:r,threshold:typeof o=="number"?o:oy[o]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),p=u?c:h;p&&p(l)};return ry(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(iy(t,n))&&this.startObserver()}unmount(){}}function iy({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const ay={inView:{Feature:sy},tap:{Feature:Qv},focus:{Feature:Jv},hover:{Feature:Zv}},ly={layout:{ProjectionNode:au,MeasureLayout:Qc}},cy={...Gg,...ay,...qv,...ly},qs=lg(cy,wg),uy=(...e)=>e.filter(Boolean).join(" "),Cr=(...e)=>$f(uy(e)),dy=ms("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-black text-white hover:bg-gray-800",destructive:"border border-black text-black hover:bg-gray-100",outline:"border border-gray-400 bg-white hover:bg-gray-100 hover:text-black",secondary:"bg-gray-200 text-black hover:bg-gray-300",ghost:"text-black hover:bg-gray-100 hover:text-black",link:"text-black underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default"}}),Kn=ue.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>f.jsx("button",{className:Cr(dy({variant:t,size:n,className:e})),ref:s,...o}));Kn.displayName="Button";const lu=ue.forwardRef(({className:e,...t},n)=>f.jsx("textarea",{className:Cr("flex min-h-[80px] w-full rounded-md border border-gray-400 bg-white px-3 py-2 text-base ring-offset-white placeholder:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-black",e),ref:n,...t}));lu.displayName="Textarea";const fy=({size:e=16})=>f.jsx("svg",{height:e,viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:f.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})}),hy=({size:e=16})=>f.jsx("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:f.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})});function py({onSelectAction:e}){const t=[{title:"受限空间内气体环境满足作业要求下",label:"气体检测分析合格标准是多少",action:"受限空间内气体环境满足作业要求下，气体检测分析合格标准是多少"},{title:"受限空间作业前,应根据受限空间",label:"盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",action:"受限空间作业前,应根据受限空间盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准"},{title:"固定式配电箱及开关箱的底面",label:"离地面垂直高度的高度是多少",action:"固定式配电箱及开关箱的底面离地面垂直高度的高度是多少"},{title:"八大保命原则",label:"是什么",action:"中化集团八大保命原则"}];return f.jsx("div",{"data-testid":"suggested-actions",className:"grid pb-2 grid-cols-1 sm:grid-cols-2 gap-2 w-full",children:f.jsx(Sc,{children:t.map((n,r)=>f.jsx(qs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*r},className:"block",children:f.jsxs(Kn,{variant:"ghost",onClick:()=>e(n.action),className:`text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start
                       border-gray-300 bg-white hover:bg-gray-100 text-black hover:text-gray-900`,children:[f.jsx("span",{className:"font-medium",children:n.title}),f.jsx("span",{className:"text-gray-500",children:n.label})]})},`suggested-action-${r}`))})})}const my=d.memo(py,(e,t)=>!(e.chatId!==t.chatId||e.selectedVisibilityType!==t.selectedVisibilityType)),Ba=({attachment:e,isUploading:t=!1})=>{const{name:n,url:r,contentType:o}=e;return f.jsxs("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-1",children:[f.jsxs("div",{className:"w-20 h-16 aspect-video bg-gray-200 rounded-md relative flex flex-col items-center justify-center overflow-hidden border border-gray-300",children:[o?.startsWith("image/")&&r?f.jsx("img",{src:r,alt:n??"An image attachment",className:"rounded-md size-full object-cover grayscale"},r):f.jsxs("div",{className:"flex items-center justify-center text-xs text-gray-600 text-center p-1",children:["File: ",n?.split(".").pop()?.toUpperCase()||"Unknown"]}),t&&f.jsx("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-gray-500",children:f.jsx(No,{className:"size-5"})})]}),f.jsx("div",{className:"text-xs text-gray-600 max-w-20 truncate",children:n})]})};function gy({onStop:e}){return f.jsx(Kn,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border border-black text-white",onClick:t=>{t.preventDefault(),e()},"aria-label":"Stop generating",children:f.jsx(fy,{size:14})})}const vy=d.memo(gy,(e,t)=>e.onStop===t.onStop);function yy({submitForm:e,input:t,uploadQueue:n,attachments:r,canSend:o,isGenerating:s}){const i=n.length>0||!o||s||t.trim().length===0&&r.length===0;return f.jsx(Kn,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit",onClick:a=>{a.preventDefault(),i||e()},disabled:i,"aria-label":"Send message",children:f.jsx(hy,{size:14})})}const xy=d.memo(yy,(e,t)=>!(e.input!==t.input||e.uploadQueue.length!==t.uploadQueue.length||e.attachments.length!==t.attachments.length||e.attachments.length>0&&!Dh(e.attachments,t.attachments)||e.canSend!==t.canSend||e.isGenerating!==t.isGenerating));function by({chatId:e,messages:t,attachments:n,setAttachments:r,onSendMessage:o,onStopGenerating:s,isGenerating:i,canSend:a,className:l,selectedVisibilityType:u}){const c=d.useRef(null),h=d.useRef(null),[p,m]=d.useState(""),[v,g]=d.useState([]),y=()=>{const A=c.current;A&&(A.style.height="auto",A.style.height=`${A.scrollHeight+2}px`)},x=d.useCallback(()=>{const A=c.current;A&&(A.style.height="auto",A.rows=1,y())},[]);d.useEffect(()=>{c.current&&y()},[p]);const w=A=>{m(A.target.value)},b=async A=>(console.log(`MOCK: Simulating upload for file: ${A.name}`),new Promise(I=>{setTimeout(()=>{try{const O={url:URL.createObjectURL(A),name:A.name,contentType:A.type||"application/octet-stream",size:A.size};console.log(`MOCK: Upload successful for ${A.name}`),I(O)}catch(F){console.error("MOCK: Failed to create object URL for preview:",F),I(void 0)}finally{g(F=>F.filter(O=>O!==A.name))}},700)})),S=d.useCallback(async A=>{const I=Array.from(A.target.files||[]);if(I.length===0)return;g(D=>[...D,...I.map(j=>j.name)]),h.current&&(h.current.value="");const F=25*1024*1024,O=I.filter(D=>D.size<=F),E=I.filter(D=>D.size>F);E.length>0&&(console.warn(`Skipped ${E.length} files larger than ${F/1024/1024}MB.`),g(D=>D.filter(j=>!E.some(N=>N.name===j))));const M=O.map(D=>b(D)),_=(await Promise.all(M)).filter(D=>D!==void 0);r(D=>[...D,..._])},[r,b]),C=d.useCallback(A=>{A.url.startsWith("blob:")&&URL.revokeObjectURL(A.url),r(I=>I.filter(F=>F.url!==A.url||F.name!==A.name)),c.current?.focus()},[r,c]),R=d.useCallback(()=>{if(p.trim().length===0&&n.length===0){console.warn("Please enter a message or add an attachment.");return}o({input:p,attachments:n}),m(""),r([]),n.forEach(A=>{A.url.startsWith("blob:")&&URL.revokeObjectURL(A.url)}),x(),c.current?.focus()},[p,n,o,r,c,x]),T=t.length===0&&n.length===0&&v.length===0,P=i||v.length>0;return f.jsxs("div",{className:Cr("relative w-full flex flex-col gap-4",l),children:[f.jsx(Sc,{children:T&&f.jsx(qs.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.2},children:f.jsx(my,{onSelectAction:A=>{m(A),requestAnimationFrame(()=>{y(),c.current?.focus()})},chatId:e,selectedVisibilityType:u})},"suggested-actions-container")}),f.jsx("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:h,multiple:!0,onChange:S,tabIndex:-1,disabled:P,accept:"image/*,video/*,audio/*,.pdf"}),(n.length>0||v.length>0)&&f.jsxs("div",{"data-testid":"attachments-preview",className:"flex pt-[10px] flex-row gap-3 overflow-x-auto items-end pb-2 pl-1",children:[n.map(A=>f.jsxs("div",{className:"relative group",children:[f.jsx(Ba,{attachment:A,isUploading:!1}),f.jsx(Kn,{variant:"destructive",size:"icon",className:"absolute top-[-8px] right-[-8px] h-5 w-5 rounded-full p-0 flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>C(A),"aria-label":`Remove ${A.name}`,children:f.jsx(Nr,{className:"size-3"})})]},A.url||A.name)),v.map((A,I)=>f.jsx(Ba,{attachment:{url:"",name:A,contentType:"",size:0},isUploading:!0},`upload-${A}-${I}`))]}),f.jsx(lu,{"data-testid":"multimodal-input",ref:c,placeholder:"你想问什么",value:p,onChange:w,className:Cr("min-h-[24px] max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-2xl !text-base pb-10","bg-gray-100 border border-gray-300",l),style:{color:"black"},rows:1,autoFocus:!0,disabled:!a||i||v.length>0,onKeyDown:A=>{A.key==="Enter"&&!A.shiftKey&&!A.nativeEvent.isComposing&&(A.preventDefault(),a&&!i&&v.length===0&&(p.trim().length>0||n.length>0)&&R())}}),f.jsx("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end z-10",children:i?f.jsx(vy,{onStop:s}):f.jsx(xy,{submitForm:R,input:p,uploadQueue:v,attachments:n,canSend:a,isGenerating:i})})]})}function wy({onSend:e}={}){const[t,n]=d.useState([]),[r,o]=d.useState(!1),[s]=d.useState("demo-input-only"),[i,a]=d.useState(!0),l=d.useCallback(({input:p,attachments:m})=>{console.log("--- 发送消息 ---"),console.log("输入:",p),console.log("附件:",m),console.log("---------------------------------"),e&&p.trim()&&e(p.trim()),o(!0),setTimeout(()=>{o(!1),n([])},500)},[e]),u=d.useCallback(()=>{console.log("停止按钮被点击（模拟）。"),o(!1)},[]);return f.jsx("div",{className:"w-full max-w-3xl mx-auto p-4",children:f.jsxs("div",{className:"flex flex-col gap-4",children:[f.jsx("div",{className:"flex justify-center",children:f.jsx("button",{onClick:()=>a(!i),className:"p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm",title:i?"收起快捷输入":"展开快捷输入",children:i?f.jsx(nh,{className:"w-4 h-4 text-gray-600"}):f.jsx(Jf,{className:"w-4 h-4 text-gray-600"})})}),f.jsx("div",{children:f.jsx(by,{chatId:s,messages:i?[]:[{id:"dummy",content:"",role:"user"}],attachments:t,setAttachments:n,onSendMessage:l,onStopGenerating:u,isGenerating:r,canSend:!0,selectedVisibilityType:"private"})})]})})}const Ir=d.forwardRef(({className:e,type:t,...n},r)=>f.jsx("input",{type:t,className:V("flex h-9 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm text-foreground shadow-sm shadow-black/5 transition-shadow placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:cursor-not-allowed disabled:opacity-50",t==="search"&&"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none",t==="file"&&"p-0 pr-3 italic text-muted-foreground/70 file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:border-input file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic file:text-foreground",e),ref:r,...n}));Ir.displayName="Input";function Sy({onUpload:e}={}){const t=d.useRef(null),n=d.useRef(null),[r,o]=d.useState(null),[s,i]=d.useState(null),a=d.useCallback(()=>{n.current?.click()},[]),l=d.useCallback(c=>{const h=c.target.files?.[0];if(h){i(h.name);const p=URL.createObjectURL(h);o(p),t.current=p,e?.(p)}},[e]),u=d.useCallback(()=>{r&&URL.revokeObjectURL(r),o(null),i(null),t.current=null,n.current&&(n.current.value="")},[r]);return d.useEffect(()=>()=>{t.current&&URL.revokeObjectURL(t.current)},[]),{previewUrl:r,fileName:s,fileInputRef:n,handleThumbnailClick:a,handleFileChange:l,handleRemove:u}}function Cy({onFileSelect:e,fileContent:t}){const{fileName:n,fileInputRef:r,handleThumbnailClick:o,handleFileChange:s,handleRemove:i}=Sy({onUpload:g=>console.log("Uploaded image URL:",g)}),[a,l]=d.useState(!1),u=d.useCallback(g=>{s(g);const y=g.target.files?.[0]||null;e?.(y)},[s,e]),c=d.useCallback(()=>{i(),e?.(null)},[i,e]),h=g=>{g.preventDefault(),g.stopPropagation()},p=g=>{g.preventDefault(),g.stopPropagation(),l(!0)},m=g=>{g.preventDefault(),g.stopPropagation(),l(!1)},v=d.useCallback(g=>{g.preventDefault(),g.stopPropagation(),l(!1);const y=g.dataTransfer.files?.[0];if(y){if(r.current){const x=new DataTransfer;x.items.add(y),r.current.files=x.files;const w=new Event("change",{bubbles:!0});r.current.dispatchEvent(w)}e?.(y)}},[r,e]);return f.jsxs("div",{className:"w-full max-w-md space-y-6 rounded-xl border border-border bg-card p-6 shadow-sm",children:[f.jsxs("div",{className:"space-y-2",children:[f.jsx("h3",{className:"text-lg font-medium",children:"文件上传"}),f.jsx("p",{className:"text-sm text-muted-foreground",children:"支持格式: TXT, PDF, DOCX"})]}),f.jsx(Ir,{type:"file",accept:".txt,.pdf,.docx,text/plain,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document",className:"hidden",ref:r,onChange:u}),n?f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"relative h-64 overflow-hidden rounded-lg border bg-background",children:f.jsx("div",{className:"h-full w-full p-4 overflow-auto",children:t?f.jsx("div",{className:"text-sm text-foreground whitespace-pre-wrap font-mono leading-relaxed",children:t}):f.jsx("div",{className:"flex h-full items-center justify-center text-muted-foreground",children:f.jsxs("div",{className:"text-center",children:[f.jsx("p",{className:"text-sm font-medium",children:"Preview"}),f.jsx("p",{className:"text-xs",children:"文件内容将在这里显示"})]})})})}),n&&f.jsxs("div",{className:"mt-2 flex items-center gap-2 text-sm text-muted-foreground",children:[f.jsx("span",{className:"truncate",children:n}),f.jsx("button",{onClick:c,className:"ml-auto rounded-full p-1 hover:bg-muted",children:f.jsx(Nr,{className:"h-4 w-4"})})]})]}):f.jsxs("div",{onClick:o,onDragOver:h,onDragEnter:p,onDragLeave:m,onDrop:v,className:V("flex h-64 cursor-pointer flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/50 transition-colors hover:bg-muted",a&&"border-primary/50 bg-primary/5"),children:[f.jsx("div",{className:"rounded-full bg-background p-3 shadow-sm",children:f.jsx(lh,{className:"h-6 w-6 text-muted-foreground"})}),f.jsxs("div",{className:"text-center",children:[f.jsx("p",{className:"text-sm font-medium",children:"点击选择文件"}),f.jsx("p",{className:"text-xs text-muted-foreground",children:"或拖拽文件到此处"})]})]})]})}function Ty({text:e,selected:t,setSelected:n,discount:r=!1}){return f.jsxs("button",{onClick:()=>n(e),className:V("relative w-fit px-4 py-2 text-sm font-semibold capitalize","text-foreground transition-colors",r&&"flex items-center justify-center gap-2.5"),children:[f.jsx("span",{className:"relative z-10",children:e}),t&&f.jsx(qs.span,{layoutId:"tab",transition:{type:"spring",duration:.4},className:"absolute inset-0 z-0 rounded-full bg-background shadow-sm"})]})}const Ey=zf()(Wf((e,t)=>({conversations:[],currentConversationId:null,createConversation:(n="新对话")=>{const r=`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,o={id:r,title:n,messages:[],createdAt:new Date,updatedAt:new Date};return e(s=>({conversations:[o,...s.conversations],currentConversationId:r})),r},deleteConversation:n=>{e(r=>{const o=r.conversations.filter(i=>i.id!==n),s=r.currentConversationId===n?o.length>0?o[0].id:null:r.currentConversationId;return{conversations:o,currentConversationId:s}})},updateConversationTitle:(n,r)=>{e(o=>({conversations:o.conversations.map(s=>s.id===n?{...s,title:r,updatedAt:new Date}:s)}))},setCurrentConversation:n=>{e({currentConversationId:n})},addMessage:(n,r,o)=>{const i={id:`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,content:r,role:o,timestamp:new Date};e(a=>({conversations:a.conversations.map(l=>l.id===n?{...l,messages:[...l.messages,i],updatedAt:new Date}:l)}))},getCurrentConversation:()=>{const n=t();return n.conversations.find(r=>r.id===n.currentConversationId)||null},clearAllConversations:()=>{e({conversations:[],currentConversationId:null})}}),{name:"conversation-store",partialize:e=>({conversations:e.conversations,currentConversationId:e.currentConversationId})})),yo=768;function Ay(){const[e,t]=d.useState(void 0);return d.useEffect(()=>{const n=window.matchMedia(`(max-width: ${yo-1}px)`),r=()=>{t(window.innerWidth<yo)};return n.addEventListener("change",r),t(window.innerWidth<yo),()=>n.removeEventListener("change",r)},[]),!!e}function z(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),n===!1||!o.defaultPrevented)return t?.(o)}}var Py=wl[" useId ".trim().toString()]||(()=>{}),Ry=0;function Mt(e){const[t,n]=d.useState(Py());return ft(()=>{n(r=>r??String(Ry++))},[e]),e||(t?`radix-${t}`:"")}var My=wl[" useInsertionEffect ".trim().toString()]||ft;function Lr({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,s,i]=Dy({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:o;{const c=d.useRef(e!==void 0);d.useEffect(()=>{const h=c.current;h!==a&&console.warn(`${r} is changing from ${h?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),c.current=a},[a,r])}const u=d.useCallback(c=>{if(a){const h=Ny(c)?c(e):c;h!==e&&i.current?.(h)}else s(c)},[a,e,s,i]);return[l,u]}function Dy({defaultProp:e,onChange:t}){const[n,r]=d.useState(e),o=d.useRef(n),s=d.useRef(t);return My(()=>{s.current=t},[t]),d.useEffect(()=>{o.current!==n&&(s.current?.(n),o.current=n)},[n,o]),[n,r,s]}function Ny(e){return typeof e=="function"}function jy(e,t=globalThis?.document){const n=tt(e);d.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var ky="DismissableLayer",Qo="dismissableLayer.update",_y="dismissableLayer.pointerDownOutside",Oy="dismissableLayer.focusOutside",$a,cu=d.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Vr=d.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:a,...l}=e,u=d.useContext(cu),[c,h]=d.useState(null),p=c?.ownerDocument??globalThis?.document,[,m]=d.useState({}),v=ie(t,T=>h(T)),g=Array.from(u.layers),[y]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=g.indexOf(y),w=c?g.indexOf(c):-1,b=u.layersWithOutsidePointerEventsDisabled.size>0,S=w>=x,C=Vy(T=>{const P=T.target,A=[...u.branches].some(I=>I.contains(P));!S||A||(o?.(T),i?.(T),T.defaultPrevented||a?.())},p),R=Fy(T=>{const P=T.target;[...u.branches].some(I=>I.contains(P))||(s?.(T),i?.(T),T.defaultPrevented||a?.())},p);return jy(T=>{w===u.layers.size-1&&(r?.(T),!T.defaultPrevented&&a&&(T.preventDefault(),a()))},p),d.useEffect(()=>{if(c)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&($a=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(c)),u.layers.add(c),Ua(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=$a)}},[c,p,n,u]),d.useEffect(()=>()=>{c&&(u.layers.delete(c),u.layersWithOutsidePointerEventsDisabled.delete(c),Ua())},[c,u]),d.useEffect(()=>{const T=()=>m({});return document.addEventListener(Qo,T),()=>document.removeEventListener(Qo,T)},[]),f.jsx(te.div,{...l,ref:v,style:{pointerEvents:b?S?"auto":"none":void 0,...e.style},onFocusCapture:z(e.onFocusCapture,R.onFocusCapture),onBlurCapture:z(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:z(e.onPointerDownCapture,C.onPointerDownCapture)})});Vr.displayName=ky;var Iy="DismissableLayerBranch",Ly=d.forwardRef((e,t)=>{const n=d.useContext(cu),r=d.useRef(null),o=ie(t,r);return d.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),f.jsx(te.div,{...e,ref:o})});Ly.displayName=Iy;function Vy(e,t=globalThis?.document){const n=tt(e),r=d.useRef(!1),o=d.useRef(()=>{});return d.useEffect(()=>{const s=a=>{if(a.target&&!r.current){let l=function(){uu(_y,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=l,t.addEventListener("click",o.current,{once:!0})):l()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Fy(e,t=globalThis?.document){const n=tt(e),r=d.useRef(!1);return d.useEffect(()=>{const o=s=>{s.target&&!r.current&&uu(Oy,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Ua(){const e=new CustomEvent(Qo);document.dispatchEvent(e)}function uu(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Rl(o,s):o.dispatchEvent(s)}var xo="focusScope.autoFocusOnMount",bo="focusScope.autoFocusOnUnmount",za={bubbles:!1,cancelable:!0},By="FocusScope",Zs=d.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[a,l]=d.useState(null),u=tt(o),c=tt(s),h=d.useRef(null),p=ie(t,g=>l(g)),m=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(r){let g=function(b){if(m.paused||!a)return;const S=b.target;a.contains(S)?h.current=S:dt(h.current,{select:!0})},y=function(b){if(m.paused||!a)return;const S=b.relatedTarget;S!==null&&(a.contains(S)||dt(h.current,{select:!0}))},x=function(b){if(document.activeElement===document.body)for(const C of b)C.removedNodes.length>0&&dt(a)};document.addEventListener("focusin",g),document.addEventListener("focusout",y);const w=new MutationObserver(x);return a&&w.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",g),document.removeEventListener("focusout",y),w.disconnect()}}},[r,a,m.paused]),d.useEffect(()=>{if(a){Ka.add(m);const g=document.activeElement;if(!a.contains(g)){const x=new CustomEvent(xo,za);a.addEventListener(xo,u),a.dispatchEvent(x),x.defaultPrevented||($y(Hy(du(a)),{select:!0}),document.activeElement===g&&dt(a))}return()=>{a.removeEventListener(xo,u),setTimeout(()=>{const x=new CustomEvent(bo,za);a.addEventListener(bo,c),a.dispatchEvent(x),x.defaultPrevented||dt(g??document.body,{select:!0}),a.removeEventListener(bo,c),Ka.remove(m)},0)}}},[a,u,c,m]);const v=d.useCallback(g=>{if(!n&&!r||m.paused)return;const y=g.key==="Tab"&&!g.altKey&&!g.ctrlKey&&!g.metaKey,x=document.activeElement;if(y&&x){const w=g.currentTarget,[b,S]=Uy(w);b&&S?!g.shiftKey&&x===S?(g.preventDefault(),n&&dt(b,{select:!0})):g.shiftKey&&x===b&&(g.preventDefault(),n&&dt(S,{select:!0})):x===w&&g.preventDefault()}},[n,r,m.paused]);return f.jsx(te.div,{tabIndex:-1,...i,ref:p,onKeyDown:v})});Zs.displayName=By;function $y(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(dt(r,{select:t}),document.activeElement!==n)return}function Uy(e){const t=du(e),n=Wa(t,e),r=Wa(t.reverse(),e);return[n,r]}function du(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Wa(e,t){for(const n of e)if(!zy(n,{upTo:t}))return n}function zy(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Wy(e){return e instanceof HTMLInputElement&&"select"in e}function dt(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Wy(e)&&t&&e.select()}}var Ka=Ky();function Ky(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=Ha(e,t),e.unshift(t)},remove(t){e=Ha(e,t),e[0]?.resume()}}}function Ha(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Hy(e){return e.filter(t=>t.tagName!=="A")}var Gy="Portal",Js=d.forwardRef((e,t)=>{const{container:n,...r}=e,[o,s]=d.useState(!1);ft(()=>s(!0),[]);const i=n||o&&globalThis?.document?.body;return i?Ff.createPortal(f.jsx(te.div,{...r,ref:t}),i):null});Js.displayName=Gy;function Yy(e,t){return d.useReducer((n,r)=>t[n][r]??n,e)}var it=e=>{const{present:t,children:n}=e,r=Xy(t),o=typeof n=="function"?n({present:r.isPresent}):d.Children.only(n),s=ie(r.ref,qy(o));return typeof n=="function"||r.isPresent?d.cloneElement(o,{ref:s}):null};it.displayName="Presence";function Xy(e){const[t,n]=d.useState(),r=d.useRef(null),o=d.useRef(e),s=d.useRef("none"),i=e?"mounted":"unmounted",[a,l]=Yy(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return d.useEffect(()=>{const u=or(r.current);s.current=a==="mounted"?u:"none"},[a]),ft(()=>{const u=r.current,c=o.current;if(c!==e){const p=s.current,m=or(u);e?l("MOUNT"):m==="none"||u?.display==="none"?l("UNMOUNT"):l(c&&p!==m?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,l]),ft(()=>{if(t){let u;const c=t.ownerDocument.defaultView??window,h=m=>{const g=or(r.current).includes(m.animationName);if(m.target===t&&g&&(l("ANIMATION_END"),!o.current)){const y=t.style.animationFillMode;t.style.animationFillMode="forwards",u=c.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=y)})}},p=m=>{m.target===t&&(s.current=or(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",h),t.addEventListener("animationend",h),()=>{c.clearTimeout(u),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",h),t.removeEventListener("animationend",h)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:d.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function or(e){return e?.animationName||"none"}function qy(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var wo=0;function fu(){d.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ga()),document.body.insertAdjacentElement("beforeend",e[1]??Ga()),wo++,()=>{wo===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),wo--}},[])}function Ga(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ke=function(){return Ke=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Ke.apply(this,arguments)};function hu(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Zy(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var pr="right-scroll-bar-position",mr="width-before-scroll-bar",Jy="with-scroll-bars-hidden",Qy="--removed-body-scroll-bar-size";function So(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function ex(e,t){var n=d.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var tx=typeof window<"u"?d.useLayoutEffect:d.useEffect,Ya=new WeakMap;function nx(e,t){var n=ex(null,function(r){return e.forEach(function(o){return So(o,r)})});return tx(function(){var r=Ya.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(a){s.has(a)||So(a,null)}),s.forEach(function(a){o.has(a)||So(a,i)})}Ya.set(n,e)},[e]),n}function rx(e){return e}function ox(e,t){t===void 0&&(t=rx);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(a){return s(a)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(s),i=n}var l=function(){var c=i;i=[],c.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(c){i.push(c),u()},filter:function(c){return i=i.filter(c),n}}}};return o}function sx(e){e===void 0&&(e={});var t=ox(null);return t.options=Ke({async:!0,ssr:!1},e),t}var pu=function(e){var t=e.sideCar,n=hu(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return d.createElement(r,Ke({},n))};pu.isSideCarExport=!0;function ix(e,t){return e.useMedium(t),pu}var mu=sx(),Co=function(){},Fr=d.forwardRef(function(e,t){var n=d.useRef(null),r=d.useState({onScrollCapture:Co,onWheelCapture:Co,onTouchMoveCapture:Co}),o=r[0],s=r[1],i=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,c=e.enabled,h=e.shards,p=e.sideCar,m=e.noRelative,v=e.noIsolation,g=e.inert,y=e.allowPinchZoom,x=e.as,w=x===void 0?"div":x,b=e.gapMode,S=hu(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=p,R=nx([n,t]),T=Ke(Ke({},S),o);return d.createElement(d.Fragment,null,c&&d.createElement(C,{sideCar:mu,removeScrollBar:u,shards:h,noRelative:m,noIsolation:v,inert:g,setCallbacks:s,allowPinchZoom:!!y,lockRef:n,gapMode:b}),i?d.cloneElement(d.Children.only(a),Ke(Ke({},T),{ref:R})):d.createElement(w,Ke({},T,{className:l,ref:R}),a))});Fr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Fr.classNames={fullWidth:mr,zeroRight:pr};var ax=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function lx(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=ax();return t&&e.setAttribute("nonce",t),e}function cx(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function ux(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var dx=function(){var e=0,t=null;return{add:function(n){e==0&&(t=lx())&&(cx(t,n),ux(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},fx=function(){var e=dx();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},gu=function(){var e=fx(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},hx={left:0,top:0,right:0,gap:0},To=function(e){return parseInt(e||"",10)||0},px=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[To(n),To(r),To(o)]},mx=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return hx;var t=px(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},gx=gu(),Jt="data-scroll-locked",vx=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Jy,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Jt,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(pr,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(mr,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(pr," .").concat(pr,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(mr," .").concat(mr,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Jt,`] {
    `).concat(Qy,": ").concat(a,`px;
  }
`)},Xa=function(){var e=parseInt(document.body.getAttribute(Jt)||"0",10);return isFinite(e)?e:0},yx=function(){d.useEffect(function(){return document.body.setAttribute(Jt,(Xa()+1).toString()),function(){var e=Xa()-1;e<=0?document.body.removeAttribute(Jt):document.body.setAttribute(Jt,e.toString())}},[])},xx=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;yx();var s=d.useMemo(function(){return mx(o)},[o]);return d.createElement(gx,{styles:vx(s,!t,o,n?"":"!important")})},es=!1;if(typeof window<"u")try{var sr=Object.defineProperty({},"passive",{get:function(){return es=!0,!0}});window.addEventListener("test",sr,sr),window.removeEventListener("test",sr,sr)}catch{es=!1}var zt=es?{passive:!1}:!1,bx=function(e){return e.tagName==="TEXTAREA"},vu=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!bx(e)&&n[t]==="visible")},wx=function(e){return vu(e,"overflowY")},Sx=function(e){return vu(e,"overflowX")},qa=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=yu(e,r);if(o){var s=xu(e,r),i=s[1],a=s[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Cx=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Tx=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},yu=function(e,t){return e==="v"?wx(t):Sx(t)},xu=function(e,t){return e==="v"?Cx(t):Tx(t)},Ex=function(e,t){return e==="h"&&t==="rtl"?-1:1},Ax=function(e,t,n,r,o){var s=Ex(e,window.getComputedStyle(t).direction),i=s*r,a=n.target,l=t.contains(a),u=!1,c=i>0,h=0,p=0;do{if(!a)break;var m=xu(e,a),v=m[0],g=m[1],y=m[2],x=g-y-s*v;(v||x)&&yu(e,a)&&(h+=x,p+=v);var w=a.parentNode;a=w&&w.nodeType===Node.DOCUMENT_FRAGMENT_NODE?w.host:w}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(c&&Math.abs(h)<1||!c&&Math.abs(p)<1)&&(u=!0),u},ir=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Za=function(e){return[e.deltaX,e.deltaY]},Ja=function(e){return e&&"current"in e?e.current:e},Px=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Rx=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Mx=0,Wt=[];function Dx(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState(Mx++)[0],s=d.useState(gu)[0],i=d.useRef(e);d.useEffect(function(){i.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=Zy([e.lockRef.current],(e.shards||[]).map(Ja),!0).filter(Boolean);return g.forEach(function(y){return y.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=d.useCallback(function(g,y){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!i.current.allowPinchZoom;var x=ir(g),w=n.current,b="deltaX"in g?g.deltaX:w[0]-x[0],S="deltaY"in g?g.deltaY:w[1]-x[1],C,R=g.target,T=Math.abs(b)>Math.abs(S)?"h":"v";if("touches"in g&&T==="h"&&R.type==="range")return!1;var P=qa(T,R);if(!P)return!0;if(P?C=T:(C=T==="v"?"h":"v",P=qa(T,R)),!P)return!1;if(!r.current&&"changedTouches"in g&&(b||S)&&(r.current=C),!C)return!0;var A=r.current||C;return Ax(A,y,g,A==="h"?b:S)},[]),l=d.useCallback(function(g){var y=g;if(!(!Wt.length||Wt[Wt.length-1]!==s)){var x="deltaY"in y?Za(y):ir(y),w=t.current.filter(function(C){return C.name===y.type&&(C.target===y.target||y.target===C.shadowParent)&&Px(C.delta,x)})[0];if(w&&w.should){y.cancelable&&y.preventDefault();return}if(!w){var b=(i.current.shards||[]).map(Ja).filter(Boolean).filter(function(C){return C.contains(y.target)}),S=b.length>0?a(y,b[0]):!i.current.noIsolation;S&&y.cancelable&&y.preventDefault()}}},[]),u=d.useCallback(function(g,y,x,w){var b={name:g,delta:y,target:x,should:w,shadowParent:Nx(x)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(S){return S!==b})},1)},[]),c=d.useCallback(function(g){n.current=ir(g),r.current=void 0},[]),h=d.useCallback(function(g){u(g.type,Za(g),g.target,a(g,e.lockRef.current))},[]),p=d.useCallback(function(g){u(g.type,ir(g),g.target,a(g,e.lockRef.current))},[]);d.useEffect(function(){return Wt.push(s),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",l,zt),document.addEventListener("touchmove",l,zt),document.addEventListener("touchstart",c,zt),function(){Wt=Wt.filter(function(g){return g!==s}),document.removeEventListener("wheel",l,zt),document.removeEventListener("touchmove",l,zt),document.removeEventListener("touchstart",c,zt)}},[]);var m=e.removeScrollBar,v=e.inert;return d.createElement(d.Fragment,null,v?d.createElement(s,{styles:Rx(o)}):null,m?d.createElement(xx,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Nx(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const jx=ix(mu,Dx);var Qs=d.forwardRef(function(e,t){return d.createElement(Fr,Ke({},e,{ref:t,sideCar:jx}))});Qs.classNames=Fr.classNames;var kx=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Kt=new WeakMap,ar=new WeakMap,lr={},Eo=0,bu=function(e){return e&&(e.host||bu(e.parentNode))},_x=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=bu(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Ox=function(e,t,n,r){var o=_x(t,Array.isArray(e)?e:[e]);lr[n]||(lr[n]=new WeakMap);var s=lr[n],i=[],a=new Set,l=new Set(o),u=function(h){!h||a.has(h)||(a.add(h),u(h.parentNode))};o.forEach(u);var c=function(h){!h||l.has(h)||Array.prototype.forEach.call(h.children,function(p){if(a.has(p))c(p);else try{var m=p.getAttribute(r),v=m!==null&&m!=="false",g=(Kt.get(p)||0)+1,y=(s.get(p)||0)+1;Kt.set(p,g),s.set(p,y),i.push(p),g===1&&v&&ar.set(p,!0),y===1&&p.setAttribute(n,"true"),v||p.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",p,x)}})};return c(t),a.clear(),Eo++,function(){i.forEach(function(h){var p=Kt.get(h)-1,m=s.get(h)-1;Kt.set(h,p),s.set(h,m),p||(ar.has(h)||h.removeAttribute(r),ar.delete(h)),m||h.removeAttribute(n)}),Eo--,Eo||(Kt=new WeakMap,Kt=new WeakMap,ar=new WeakMap,lr={})}},wu=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=kx(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Ox(r,o,n,"aria-hidden")):function(){return null}},Br="Dialog",[Su,PC]=jt(Br),[Ix,Ve]=Su(Br),Cu=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,a=d.useRef(null),l=d.useRef(null),[u,c]=Lr({prop:r,defaultProp:o??!1,onChange:s,caller:Br});return f.jsx(Ix,{scope:t,triggerRef:a,contentRef:l,contentId:Mt(),titleId:Mt(),descriptionId:Mt(),open:u,onOpenChange:c,onOpenToggle:d.useCallback(()=>c(h=>!h),[c]),modal:i,children:n})};Cu.displayName=Br;var Tu="DialogTrigger",Lx=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Tu,n),s=ie(t,o.triggerRef);return f.jsx(te.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ni(o.open),...r,ref:s,onClick:z(e.onClick,o.onOpenToggle)})});Lx.displayName=Tu;var ei="DialogPortal",[Vx,Eu]=Su(ei,{forceMount:void 0}),Au=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Ve(ei,t);return f.jsx(Vx,{scope:t,forceMount:n,children:d.Children.map(r,i=>f.jsx(it,{present:n||s.open,children:f.jsx(Js,{asChild:!0,container:o,children:i})}))})};Au.displayName=ei;var Tr="DialogOverlay",Pu=d.forwardRef((e,t)=>{const n=Eu(Tr,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ve(Tr,e.__scopeDialog);return s.modal?f.jsx(it,{present:r||s.open,children:f.jsx(Bx,{...o,ref:t})}):null});Pu.displayName=Tr;var Fx=vr("DialogOverlay.RemoveScroll"),Bx=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Tr,n);return f.jsx(Qs,{as:Fx,allowPinchZoom:!0,shards:[o.contentRef],children:f.jsx(te.div,{"data-state":ni(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),Dt="DialogContent",Ru=d.forwardRef((e,t)=>{const n=Eu(Dt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=Ve(Dt,e.__scopeDialog);return f.jsx(it,{present:r||s.open,children:s.modal?f.jsx($x,{...o,ref:t}):f.jsx(Ux,{...o,ref:t})})});Ru.displayName=Dt;var $x=d.forwardRef((e,t)=>{const n=Ve(Dt,e.__scopeDialog),r=d.useRef(null),o=ie(t,n.contentRef,r);return d.useEffect(()=>{const s=r.current;if(s)return wu(s)},[]),f.jsx(Mu,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:z(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:z(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&s.preventDefault()}),onFocusOutside:z(e.onFocusOutside,s=>s.preventDefault())})}),Ux=d.forwardRef((e,t)=>{const n=Ve(Dt,e.__scopeDialog),r=d.useRef(!1),o=d.useRef(!1);return f.jsx(Mu,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||n.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;n.triggerRef.current?.contains(i)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),Mu=d.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...i}=e,a=Ve(Dt,n),l=d.useRef(null),u=ie(t,l);return fu(),f.jsxs(f.Fragment,{children:[f.jsx(Zs,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:f.jsx(Vr,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":ni(a.open),...i,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),f.jsxs(f.Fragment,{children:[f.jsx(zx,{titleId:a.titleId}),f.jsx(Kx,{contentRef:l,descriptionId:a.descriptionId})]})]})}),ti="DialogTitle",Du=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(ti,n);return f.jsx(te.h2,{id:o.titleId,...r,ref:t})});Du.displayName=ti;var Nu="DialogDescription",ju=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(Nu,n);return f.jsx(te.p,{id:o.descriptionId,...r,ref:t})});ju.displayName=Nu;var ku="DialogClose",_u=d.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Ve(ku,n);return f.jsx(te.button,{type:"button",...r,ref:t,onClick:z(e.onClick,()=>o.onOpenChange(!1))})});_u.displayName=ku;function ni(e){return e?"open":"closed"}var Ou="DialogTitleWarning",[RC,Iu]=Uf(Ou,{contentName:Dt,titleName:ti,docsSlug:"dialog"}),zx=({titleId:e})=>{const t=Iu(Ou),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return d.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Wx="DialogDescriptionWarning",Kx=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Iu(Wx).contentName}}.`;return d.useEffect(()=>{const o=e.current?.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Hx=Cu,Gx=Au,Lu=Pu,Vu=Ru,Fu=Du,Bu=ju,Yx=_u;const Xx=Hx,qx=Gx,$u=d.forwardRef(({className:e,...t},n)=>f.jsx(Lu,{className:V("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));$u.displayName=Lu.displayName;const Zx=ms("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Uu=d.forwardRef(({side:e="right",className:t,children:n,...r},o)=>f.jsxs(qx,{children:[f.jsx($u,{}),f.jsxs(Vu,{ref:o,className:V(Zx({side:e}),t),...r,children:[n,f.jsxs(Yx,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[f.jsx(Nr,{className:"h-4 w-4"}),f.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Uu.displayName=Vu.displayName;const Jx=d.forwardRef(({className:e,...t},n)=>f.jsx(Fu,{ref:n,className:V("text-lg font-semibold text-foreground",e),...t}));Jx.displayName=Fu.displayName;const Qx=d.forwardRef(({className:e,...t},n)=>f.jsx(Bu,{ref:n,className:V("text-sm text-muted-foreground",e),...t}));Qx.displayName=Bu.displayName;function Qa({className:e,...t}){return f.jsx("div",{className:V("animate-pulse rounded-md bg-muted",e),...t})}const eb=["top","right","bottom","left"],mt=Math.min,Ce=Math.max,Er=Math.round,cr=Math.floor,qe=e=>({x:e,y:e}),tb={left:"right",right:"left",bottom:"top",top:"bottom"},nb={start:"end",end:"start"};function ts(e,t,n){return Ce(e,mt(t,n))}function ot(e,t){return typeof e=="function"?e(t):e}function st(e){return e.split("-")[0]}function ln(e){return e.split("-")[1]}function ri(e){return e==="x"?"y":"x"}function oi(e){return e==="y"?"height":"width"}const rb=new Set(["top","bottom"]);function He(e){return rb.has(st(e))?"y":"x"}function si(e){return ri(He(e))}function ob(e,t,n){n===void 0&&(n=!1);const r=ln(e),o=si(e),s=oi(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Ar(i)),[i,Ar(i)]}function sb(e){const t=Ar(e);return[ns(e),t,ns(t)]}function ns(e){return e.replace(/start|end/g,t=>nb[t])}const el=["left","right"],tl=["right","left"],ib=["top","bottom"],ab=["bottom","top"];function lb(e,t,n){switch(e){case"top":case"bottom":return n?t?tl:el:t?el:tl;case"left":case"right":return t?ib:ab;default:return[]}}function cb(e,t,n,r){const o=ln(e);let s=lb(st(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(ns)))),s}function Ar(e){return e.replace(/left|right|bottom|top/g,t=>tb[t])}function ub(e){return{top:0,right:0,bottom:0,left:0,...e}}function zu(e){return typeof e!="number"?ub(e):{top:e,right:e,bottom:e,left:e}}function Pr(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function nl(e,t,n){let{reference:r,floating:o}=e;const s=He(t),i=si(t),a=oi(i),l=st(t),u=s==="y",c=r.x+r.width/2-o.width/2,h=r.y+r.height/2-o.height/2,p=r[a]/2-o[a]/2;let m;switch(l){case"top":m={x:c,y:r.y-o.height};break;case"bottom":m={x:c,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:h};break;case"left":m={x:r.x-o.width,y:h};break;default:m={x:r.x,y:r.y}}switch(ln(t)){case"start":m[i]-=p*(n&&u?-1:1);break;case"end":m[i]+=p*(n&&u?-1:1);break}return m}const db=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,a=s.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:h}=nl(u,r,l),p=r,m={},v=0;for(let g=0;g<a.length;g++){const{name:y,fn:x}=a[g],{x:w,y:b,data:S,reset:C}=await x({x:c,y:h,initialPlacement:r,placement:p,strategy:o,middlewareData:m,rects:u,platform:i,elements:{reference:e,floating:t}});c=w??c,h=b??h,m={...m,[y]:{...m[y],...S}},C&&v<=50&&(v++,typeof C=="object"&&(C.placement&&(p=C.placement),C.rects&&(u=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:c,y:h}=nl(u,p,l)),g=-1)}return{x:c,y:h,placement:p,strategy:o,middlewareData:m}};async function On(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:h="floating",altBoundary:p=!1,padding:m=0}=ot(t,e),v=zu(m),y=a[p?h==="floating"?"reference":"floating":h],x=Pr(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(y)))==null||n?y:y.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),w=h==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a.floating)),S=await(s.isElement==null?void 0:s.isElement(b))?await(s.getScale==null?void 0:s.getScale(b))||{x:1,y:1}:{x:1,y:1},C=Pr(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:b,strategy:l}):w);return{top:(x.top-C.top+v.top)/S.y,bottom:(C.bottom-x.bottom+v.bottom)/S.y,left:(x.left-C.left+v.left)/S.x,right:(C.right-x.right+v.right)/S.x}}const fb=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:a,middlewareData:l}=t,{element:u,padding:c=0}=ot(e,t)||{};if(u==null)return{};const h=zu(c),p={x:n,y:r},m=si(o),v=oi(m),g=await i.getDimensions(u),y=m==="y",x=y?"top":"left",w=y?"bottom":"right",b=y?"clientHeight":"clientWidth",S=s.reference[v]+s.reference[m]-p[m]-s.floating[v],C=p[m]-s.reference[m],R=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let T=R?R[b]:0;(!T||!await(i.isElement==null?void 0:i.isElement(R)))&&(T=a.floating[b]||s.floating[v]);const P=S/2-C/2,A=T/2-g[v]/2-1,I=mt(h[x],A),F=mt(h[w],A),O=I,E=T-g[v]-F,M=T/2-g[v]/2+P,L=ts(O,M,E),_=!l.arrow&&ln(o)!=null&&M!==L&&s.reference[v]/2-(M<O?I:F)-g[v]/2<0,D=_?M<O?M-O:M-E:0;return{[m]:p[m]+D,data:{[m]:L,centerOffset:M-L-D,..._&&{alignmentOffset:D}},reset:_}}}),hb=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:c=!0,crossAxis:h=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:g=!0,...y}=ot(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=st(o),w=He(a),b=st(a)===a,S=await(l.isRTL==null?void 0:l.isRTL(u.floating)),C=p||(b||!g?[Ar(a)]:sb(a)),R=v!=="none";!p&&R&&C.push(...cb(a,g,v,S));const T=[a,...C],P=await On(t,y),A=[];let I=((r=s.flip)==null?void 0:r.overflows)||[];if(c&&A.push(P[x]),h){const M=ob(o,i,S);A.push(P[M[0]],P[M[1]])}if(I=[...I,{placement:o,overflows:A}],!A.every(M=>M<=0)){var F,O;const M=(((F=s.flip)==null?void 0:F.index)||0)+1,L=T[M];if(L&&(!(h==="alignment"?w!==He(L):!1)||I.every(j=>j.overflows[0]>0&&He(j.placement)===w)))return{data:{index:M,overflows:I},reset:{placement:L}};let _=(O=I.filter(D=>D.overflows[0]<=0).sort((D,j)=>D.overflows[1]-j.overflows[1])[0])==null?void 0:O.placement;if(!_)switch(m){case"bestFit":{var E;const D=(E=I.filter(j=>{if(R){const N=He(j.placement);return N===w||N==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(N=>N>0).reduce((N,G)=>N+G,0)]).sort((j,N)=>j[1]-N[1])[0])==null?void 0:E[0];D&&(_=D);break}case"initialPlacement":_=a;break}if(o!==_)return{reset:{placement:_}}}return{}}}};function rl(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ol(e){return eb.some(t=>e[t]>=0)}const pb=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ot(e,t);switch(r){case"referenceHidden":{const s=await On(t,{...o,elementContext:"reference"}),i=rl(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:ol(i)}}}case"escaped":{const s=await On(t,{...o,altBoundary:!0}),i=rl(s,n.floating);return{data:{escapedOffsets:i,escaped:ol(i)}}}default:return{}}}}},Wu=new Set(["left","top"]);async function mb(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=st(n),a=ln(n),l=He(n)==="y",u=Wu.has(i)?-1:1,c=s&&l?-1:1,h=ot(t,e);let{mainAxis:p,crossAxis:m,alignmentAxis:v}=typeof h=="number"?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&typeof v=="number"&&(m=a==="end"?v*-1:v),l?{x:m*c,y:p*u}:{x:p*u,y:m*c}}const gb=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:a}=t,l=await mb(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:s+l.y,data:{...l,placement:i}}}}},vb=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:a={fn:y=>{let{x,y:w}=y;return{x,y:w}}},...l}=ot(e,t),u={x:n,y:r},c=await On(t,l),h=He(st(o)),p=ri(h);let m=u[p],v=u[h];if(s){const y=p==="y"?"top":"left",x=p==="y"?"bottom":"right",w=m+c[y],b=m-c[x];m=ts(w,m,b)}if(i){const y=h==="y"?"top":"left",x=h==="y"?"bottom":"right",w=v+c[y],b=v-c[x];v=ts(w,v,b)}const g=a.fn({...t,[p]:m,[h]:v});return{...g,data:{x:g.x-n,y:g.y-r,enabled:{[p]:s,[h]:i}}}}}},yb=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=ot(e,t),c={x:n,y:r},h=He(o),p=ri(h);let m=c[p],v=c[h];const g=ot(a,t),y=typeof g=="number"?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){const b=p==="y"?"height":"width",S=s.reference[p]-s.floating[b]+y.mainAxis,C=s.reference[p]+s.reference[b]-y.mainAxis;m<S?m=S:m>C&&(m=C)}if(u){var x,w;const b=p==="y"?"width":"height",S=Wu.has(st(o)),C=s.reference[h]-s.floating[b]+(S&&((x=i.offset)==null?void 0:x[h])||0)+(S?0:y.crossAxis),R=s.reference[h]+s.reference[b]+(S?0:((w=i.offset)==null?void 0:w[h])||0)-(S?y.crossAxis:0);v<C?v=C:v>R&&(v=R)}return{[p]:m,[h]:v}}}},xb=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:a}=t,{apply:l=()=>{},...u}=ot(e,t),c=await On(t,u),h=st(o),p=ln(o),m=He(o)==="y",{width:v,height:g}=s.floating;let y,x;h==="top"||h==="bottom"?(y=h,x=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=h,y=p==="end"?"top":"bottom");const w=g-c.top-c.bottom,b=v-c.left-c.right,S=mt(g-c[y],w),C=mt(v-c[x],b),R=!t.middlewareData.shift;let T=S,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=b),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(T=w),R&&!p){const I=Ce(c.left,0),F=Ce(c.right,0),O=Ce(c.top,0),E=Ce(c.bottom,0);m?P=v-2*(I!==0||F!==0?I+F:Ce(c.left,c.right)):T=g-2*(O!==0||E!==0?O+E:Ce(c.top,c.bottom))}await l({...t,availableWidth:P,availableHeight:T});const A=await i.getDimensions(a.floating);return v!==A.width||g!==A.height?{reset:{rects:!0}}:{}}}};function $r(){return typeof window<"u"}function cn(e){return Ku(e)?(e.nodeName||"").toLowerCase():"#document"}function Te(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Je(e){var t;return(t=(Ku(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ku(e){return $r()?e instanceof Node||e instanceof Te(e).Node:!1}function Ie(e){return $r()?e instanceof Element||e instanceof Te(e).Element:!1}function Ze(e){return $r()?e instanceof HTMLElement||e instanceof Te(e).HTMLElement:!1}function sl(e){return!$r()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Te(e).ShadowRoot}const bb=new Set(["inline","contents"]);function Hn(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Le(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!bb.has(o)}const wb=new Set(["table","td","th"]);function Sb(e){return wb.has(cn(e))}const Cb=[":popover-open",":modal"];function Ur(e){return Cb.some(t=>{try{return e.matches(t)}catch{return!1}})}const Tb=["transform","translate","scale","rotate","perspective"],Eb=["transform","translate","scale","rotate","perspective","filter"],Ab=["paint","layout","strict","content"];function ii(e){const t=ai(),n=Ie(e)?Le(e):e;return Tb.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Eb.some(r=>(n.willChange||"").includes(r))||Ab.some(r=>(n.contain||"").includes(r))}function Pb(e){let t=gt(e);for(;Ze(t)&&!nn(t);){if(ii(t))return t;if(Ur(t))return null;t=gt(t)}return null}function ai(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Rb=new Set(["html","body","#document"]);function nn(e){return Rb.has(cn(e))}function Le(e){return Te(e).getComputedStyle(e)}function zr(e){return Ie(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function gt(e){if(cn(e)==="html")return e;const t=e.assignedSlot||e.parentNode||sl(e)&&e.host||Je(e);return sl(t)?t.host:t}function Hu(e){const t=gt(e);return nn(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ze(t)&&Hn(t)?t:Hu(t)}function In(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Hu(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=Te(o);if(s){const a=rs(i);return t.concat(i,i.visualViewport||[],Hn(o)?o:[],a&&n?In(a):[])}return t.concat(o,In(o,[],n))}function rs(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Gu(e){const t=Le(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Ze(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,a=Er(n)!==s||Er(r)!==i;return a&&(n=s,r=i),{width:n,height:r,$:a}}function li(e){return Ie(e)?e:e.contextElement}function Qt(e){const t=li(e);if(!Ze(t))return qe(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Gu(t);let i=(s?Er(n.width):n.width)/r,a=(s?Er(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const Mb=qe(0);function Yu(e){const t=Te(e);return!ai()||!t.visualViewport?Mb:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Db(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Te(e)?!1:t}function Nt(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=li(e);let i=qe(1);t&&(r?Ie(r)&&(i=Qt(r)):i=Qt(e));const a=Db(s,n,r)?Yu(s):qe(0);let l=(o.left+a.x)/i.x,u=(o.top+a.y)/i.y,c=o.width/i.x,h=o.height/i.y;if(s){const p=Te(s),m=r&&Ie(r)?Te(r):r;let v=p,g=rs(v);for(;g&&r&&m!==v;){const y=Qt(g),x=g.getBoundingClientRect(),w=Le(g),b=x.left+(g.clientLeft+parseFloat(w.paddingLeft))*y.x,S=x.top+(g.clientTop+parseFloat(w.paddingTop))*y.y;l*=y.x,u*=y.y,c*=y.x,h*=y.y,l+=b,u+=S,v=Te(g),g=rs(v)}}return Pr({width:c,height:h,x:l,y:u})}function ci(e,t){const n=zr(e).scrollLeft;return t?t.left+n:Nt(Je(e)).left+n}function Xu(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:ci(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function Nb(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Je(r),a=t?Ur(t.floating):!1;if(r===i||a&&s)return n;let l={scrollLeft:0,scrollTop:0},u=qe(1);const c=qe(0),h=Ze(r);if((h||!h&&!s)&&((cn(r)!=="body"||Hn(i))&&(l=zr(r)),Ze(r))){const m=Nt(r);u=Qt(r),c.x=m.x+r.clientLeft,c.y=m.y+r.clientTop}const p=i&&!h&&!s?Xu(i,l,!0):qe(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+p.x,y:n.y*u.y-l.scrollTop*u.y+c.y+p.y}}function jb(e){return Array.from(e.getClientRects())}function kb(e){const t=Je(e),n=zr(e),r=e.ownerDocument.body,o=Ce(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=Ce(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+ci(e);const a=-n.scrollTop;return Le(r).direction==="rtl"&&(i+=Ce(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:a}}function _b(e,t){const n=Te(e),r=Je(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,a=0,l=0;if(o){s=o.width,i=o.height;const u=ai();(!u||u&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:s,height:i,x:a,y:l}}const Ob=new Set(["absolute","fixed"]);function Ib(e,t){const n=Nt(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Ze(e)?Qt(e):qe(1),i=e.clientWidth*s.x,a=e.clientHeight*s.y,l=o*s.x,u=r*s.y;return{width:i,height:a,x:l,y:u}}function il(e,t,n){let r;if(t==="viewport")r=_b(e,n);else if(t==="document")r=kb(Je(e));else if(Ie(t))r=Ib(t,n);else{const o=Yu(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Pr(r)}function qu(e,t){const n=gt(e);return n===t||!Ie(n)||nn(n)?!1:Le(n).position==="fixed"||qu(n,t)}function Lb(e,t){const n=t.get(e);if(n)return n;let r=In(e,[],!1).filter(a=>Ie(a)&&cn(a)!=="body"),o=null;const s=Le(e).position==="fixed";let i=s?gt(e):e;for(;Ie(i)&&!nn(i);){const a=Le(i),l=ii(i);!l&&a.position==="fixed"&&(o=null),(s?!l&&!o:!l&&a.position==="static"&&!!o&&Ob.has(o.position)||Hn(i)&&!l&&qu(e,i))?r=r.filter(c=>c!==i):o=a,i=gt(i)}return t.set(e,r),r}function Vb(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Ur(t)?[]:Lb(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((u,c)=>{const h=il(t,c,o);return u.top=Ce(h.top,u.top),u.right=mt(h.right,u.right),u.bottom=mt(h.bottom,u.bottom),u.left=Ce(h.left,u.left),u},il(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Fb(e){const{width:t,height:n}=Gu(e);return{width:t,height:n}}function Bb(e,t,n){const r=Ze(t),o=Je(t),s=n==="fixed",i=Nt(e,!0,s,t);let a={scrollLeft:0,scrollTop:0};const l=qe(0);function u(){l.x=ci(o)}if(r||!r&&!s)if((cn(t)!=="body"||Hn(o))&&(a=zr(t)),r){const m=Nt(t,!0,s,t);l.x=m.x+t.clientLeft,l.y=m.y+t.clientTop}else o&&u();s&&!r&&o&&u();const c=o&&!r&&!s?Xu(o,a):qe(0),h=i.left+a.scrollLeft-l.x-c.x,p=i.top+a.scrollTop-l.y-c.y;return{x:h,y:p,width:i.width,height:i.height}}function Ao(e){return Le(e).position==="static"}function al(e,t){if(!Ze(e)||Le(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Je(e)===n&&(n=n.ownerDocument.body),n}function Zu(e,t){const n=Te(e);if(Ur(e))return n;if(!Ze(e)){let o=gt(e);for(;o&&!nn(o);){if(Ie(o)&&!Ao(o))return o;o=gt(o)}return n}let r=al(e,t);for(;r&&Sb(r)&&Ao(r);)r=al(r,t);return r&&nn(r)&&Ao(r)&&!ii(r)?n:r||Pb(e)||n}const $b=async function(e){const t=this.getOffsetParent||Zu,n=this.getDimensions,r=await n(e.floating);return{reference:Bb(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Ub(e){return Le(e).direction==="rtl"}const zb={convertOffsetParentRelativeRectToViewportRelativeRect:Nb,getDocumentElement:Je,getClippingRect:Vb,getOffsetParent:Zu,getElementRects:$b,getClientRects:jb,getDimensions:Fb,getScale:Qt,isElement:Ie,isRTL:Ub};function Ju(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Wb(e,t){let n=null,r;const o=Je(e);function s(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:c,top:h,width:p,height:m}=u;if(a||t(),!p||!m)return;const v=cr(h),g=cr(o.clientWidth-(c+p)),y=cr(o.clientHeight-(h+m)),x=cr(c),b={rootMargin:-v+"px "+-g+"px "+-y+"px "+-x+"px",threshold:Ce(0,mt(1,l))||1};let S=!0;function C(R){const T=R[0].intersectionRatio;if(T!==l){if(!S)return i();T?i(!1,T):r=setTimeout(()=>{i(!1,1e-7)},1e3)}T===1&&!Ju(u,e.getBoundingClientRect())&&i(),S=!1}try{n=new IntersectionObserver(C,{...b,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,b)}n.observe(e)}return i(!0),s}function Qu(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=li(e),c=o||s?[...u?In(u):[],...In(t)]:[];c.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const h=u&&a?Wb(u,n):null;let p=-1,m=null;i&&(m=new ResizeObserver(x=>{let[w]=x;w&&w.target===u&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var b;(b=m)==null||b.observe(t)})),n()}),u&&!l&&m.observe(u),m.observe(t));let v,g=l?Nt(e):null;l&&y();function y(){const x=Nt(e);g&&!Ju(g,x)&&n(),g=x,v=requestAnimationFrame(y)}return n(),()=>{var x;c.forEach(w=>{o&&w.removeEventListener("scroll",n),s&&w.removeEventListener("resize",n)}),h?.(),(x=m)==null||x.disconnect(),m=null,l&&cancelAnimationFrame(v)}}const ed=gb,td=vb,nd=hb,Kb=xb,Hb=pb,os=fb,Gb=yb,ss=(e,t,n)=>{const r=new Map,o={platform:zb,...n},s={...o.platform,_c:r};return db(e,t,{...o,platform:s})};var Yb=typeof document<"u",Xb=function(){},gr=Yb?d.useLayoutEffect:Xb;function Rr(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Rr(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Rr(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function rd(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ll(e,t){const n=rd(e);return Math.round(t*n)/n}function Po(e){const t=d.useRef(e);return gr(()=>{t.current=e}),t}function qb(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:a=!0,whileElementsMounted:l,open:u}=e,[c,h]=d.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=d.useState(r);Rr(p,r)||m(r);const[v,g]=d.useState(null),[y,x]=d.useState(null),w=d.useCallback(j=>{j!==R.current&&(R.current=j,g(j))},[]),b=d.useCallback(j=>{j!==T.current&&(T.current=j,x(j))},[]),S=s||v,C=i||y,R=d.useRef(null),T=d.useRef(null),P=d.useRef(c),A=l!=null,I=Po(l),F=Po(o),O=Po(u),E=d.useCallback(()=>{if(!R.current||!T.current)return;const j={placement:t,strategy:n,middleware:p};F.current&&(j.platform=F.current),ss(R.current,T.current,j).then(N=>{const G={...N,isPositioned:O.current!==!1};M.current&&!Rr(P.current,G)&&(P.current=G,Bf.flushSync(()=>{h(G)}))})},[p,t,n,F,O]);gr(()=>{u===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,h(j=>({...j,isPositioned:!1})))},[u]);const M=d.useRef(!1);gr(()=>(M.current=!0,()=>{M.current=!1}),[]),gr(()=>{if(S&&(R.current=S),C&&(T.current=C),S&&C){if(I.current)return I.current(S,C,E);E()}},[S,C,E,I,A]);const L=d.useMemo(()=>({reference:R,floating:T,setReference:w,setFloating:b}),[w,b]),_=d.useMemo(()=>({reference:S,floating:C}),[S,C]),D=d.useMemo(()=>{const j={position:n,left:0,top:0};if(!_.floating)return j;const N=ll(_.floating,c.x),G=ll(_.floating,c.y);return a?{...j,transform:"translate("+N+"px, "+G+"px)",...rd(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:N,top:G}},[n,a,_.floating,c.x,c.y]);return d.useMemo(()=>({...c,update:E,refs:L,elements:_,floatingStyles:D}),[c,E,L,_,D])}const Zb=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?os({element:r.current,padding:o}).fn(n):{}:r?os({element:r,padding:o}).fn(n):{}}}},Jb=(e,t)=>({...ed(e),options:[e,t]}),Qb=(e,t)=>({...td(e),options:[e,t]}),ew=(e,t)=>({...Gb(e),options:[e,t]}),tw=(e,t)=>({...nd(e),options:[e,t]}),nw=(e,t)=>({...Kb(e),options:[e,t]}),rw=(e,t)=>({...Hb(e),options:[e,t]}),ow=(e,t)=>({...Zb(e),options:[e,t]});var sw="Arrow",od=d.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return f.jsx(te.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:f.jsx("polygon",{points:"0,0 30,0 15,10"})})});od.displayName=sw;var iw=od;function aw(e){const[t,n]=d.useState(void 0);return ft(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,a;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,a=u.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var ui="Popper",[sd,Wr]=jt(ui),[lw,id]=sd(ui),ad=e=>{const{__scopePopper:t,children:n}=e,[r,o]=d.useState(null);return f.jsx(lw,{scope:t,anchor:r,onAnchorChange:o,children:n})};ad.displayName=ui;var ld="PopperAnchor",cd=d.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=id(ld,n),i=d.useRef(null),a=ie(t,i);return d.useEffect(()=>{s.onAnchorChange(r?.current||i.current)}),r?null:f.jsx(te.div,{...o,ref:a})});cd.displayName=ld;var di="PopperContent",[cw,uw]=sd(di),ud=d.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:c=0,sticky:h="partial",hideWhenDetached:p=!1,updatePositionStrategy:m="optimized",onPlaced:v,...g}=e,y=id(di,n),[x,w]=d.useState(null),b=ie(t,Z=>w(Z)),[S,C]=d.useState(null),R=aw(S),T=R?.width??0,P=R?.height??0,A=r+(s!=="center"?"-"+s:""),I=typeof c=="number"?c:{top:0,right:0,bottom:0,left:0,...c},F=Array.isArray(u)?u:[u],O=F.length>0,E={padding:I,boundary:F.filter(fw),altBoundary:O},{refs:M,floatingStyles:L,placement:_,isPositioned:D,middlewareData:j}=qb({strategy:"fixed",placement:A,whileElementsMounted:(...Z)=>Qu(...Z,{animationFrame:m==="always"}),elements:{reference:y.anchor},middleware:[Jb({mainAxis:o+P,alignmentAxis:i}),l&&Qb({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?ew():void 0,...E}),l&&tw({...E}),nw({...E,apply:({elements:Z,rects:ke,availableWidth:le,availableHeight:at})=>{const{width:be,height:$e}=ke.reference,Ue=Z.floating.style;Ue.setProperty("--radix-popper-available-width",`${le}px`),Ue.setProperty("--radix-popper-available-height",`${at}px`),Ue.setProperty("--radix-popper-anchor-width",`${be}px`),Ue.setProperty("--radix-popper-anchor-height",`${$e}px`)}}),S&&ow({element:S,padding:a}),hw({arrowWidth:T,arrowHeight:P}),p&&rw({strategy:"referenceHidden",...E})]}),[N,G]=hd(_),H=tt(v);ft(()=>{D&&H?.()},[D,H]);const ve=j.arrow?.x,Fe=j.arrow?.y,Be=j.arrow?.centerOffset!==0,[Qe,ye]=d.useState();return ft(()=>{x&&ye(window.getComputedStyle(x).zIndex)},[x]),f.jsx("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:D?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Qe,"--radix-popper-transform-origin":[j.transformOrigin?.x,j.transformOrigin?.y].join(" "),...j.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:f.jsx(cw,{scope:n,placedSide:N,onArrowChange:C,arrowX:ve,arrowY:Fe,shouldHideArrow:Be,children:f.jsx(te.div,{"data-side":N,"data-align":G,...g,ref:b,style:{...g.style,animation:D?void 0:"none"}})})})});ud.displayName=di;var dd="PopperArrow",dw={top:"bottom",right:"left",bottom:"top",left:"right"},fd=d.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=uw(dd,r),i=dw[s.placedSide];return f.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:f.jsx(iw,{...o,ref:n,style:{...o.style,display:"block"}})})});fd.displayName=dd;function fw(e){return e!==null}var hw=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=hd(n),h={start:"0%",center:"50%",end:"100%"}[c],p=(o.arrow?.x??0)+a/2,m=(o.arrow?.y??0)+l/2;let v="",g="";return u==="bottom"?(v=i?h:`${p}px`,g=`${-l}px`):u==="top"?(v=i?h:`${p}px`,g=`${r.floating.height+l}px`):u==="right"?(v=`${-l}px`,g=i?h:`${m}px`):u==="left"&&(v=`${r.floating.width+l}px`,g=i?h:`${m}px`),{data:{x:v,y:g}}}});function hd(e){const[t,n="center"]=e.split("-");return[t,n]}var pd=ad,md=cd,gd=ud,vd=fd,pw=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),mw="VisuallyHidden",yd=d.forwardRef((e,t)=>f.jsx(te.span,{...e,ref:t,style:{...pw,...e.style}}));yd.displayName=mw;var gw=yd,[Kr,MC]=jt("Tooltip",[Wr]),Hr=Wr(),xd="TooltipProvider",vw=700,is="tooltip.open",[yw,fi]=Kr(xd),bd=e=>{const{__scopeTooltip:t,delayDuration:n=vw,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,i=d.useRef(!0),a=d.useRef(!1),l=d.useRef(0);return d.useEffect(()=>{const u=l.current;return()=>window.clearTimeout(u)},[]),f.jsx(yw,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:d.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:d.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:d.useCallback(u=>{a.current=u},[]),disableHoverableContent:o,children:s})};bd.displayName=xd;var Ln="Tooltip",[xw,Gr]=Kr(Ln),wd=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:s,disableHoverableContent:i,delayDuration:a}=e,l=fi(Ln,e.__scopeTooltip),u=Hr(t),[c,h]=d.useState(null),p=Mt(),m=d.useRef(0),v=i??l.disableHoverableContent,g=a??l.delayDuration,y=d.useRef(!1),[x,w]=Lr({prop:r,defaultProp:o??!1,onChange:T=>{T?(l.onOpen(),document.dispatchEvent(new CustomEvent(is))):l.onClose(),s?.(T)},caller:Ln}),b=d.useMemo(()=>x?y.current?"delayed-open":"instant-open":"closed",[x]),S=d.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y.current=!1,w(!0)},[w]),C=d.useCallback(()=>{window.clearTimeout(m.current),m.current=0,w(!1)},[w]),R=d.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{y.current=!0,w(!0),m.current=0},g)},[g,w]);return d.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),f.jsx(pd,{...u,children:f.jsx(xw,{scope:t,contentId:p,open:x,stateAttribute:b,trigger:c,onTriggerChange:h,onTriggerEnter:d.useCallback(()=>{l.isOpenDelayedRef.current?R():S()},[l.isOpenDelayedRef,R,S]),onTriggerLeave:d.useCallback(()=>{v?C():(window.clearTimeout(m.current),m.current=0)},[C,v]),onOpen:S,onClose:C,disableHoverableContent:v,children:n})})};wd.displayName=Ln;var as="TooltipTrigger",Sd=d.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Gr(as,n),s=fi(as,n),i=Hr(n),a=d.useRef(null),l=ie(t,a,o.onTriggerChange),u=d.useRef(!1),c=d.useRef(!1),h=d.useCallback(()=>u.current=!1,[]);return d.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),f.jsx(md,{asChild:!0,...i,children:f.jsx(te.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:l,onPointerMove:z(e.onPointerMove,p=>{p.pointerType!=="touch"&&!c.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),c.current=!0)}),onPointerLeave:z(e.onPointerLeave,()=>{o.onTriggerLeave(),c.current=!1}),onPointerDown:z(e.onPointerDown,()=>{o.open&&o.onClose(),u.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:z(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:z(e.onBlur,o.onClose),onClick:z(e.onClick,o.onClose)})})});Sd.displayName=as;var bw="TooltipPortal",[DC,ww]=Kr(bw,{forceMount:void 0}),rn="TooltipContent",Cd=d.forwardRef((e,t)=>{const n=ww(rn,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=Gr(rn,e.__scopeTooltip);return f.jsx(it,{present:r||i.open,children:i.disableHoverableContent?f.jsx(Td,{side:o,...s,ref:t}):f.jsx(Sw,{side:o,...s,ref:t})})}),Sw=d.forwardRef((e,t)=>{const n=Gr(rn,e.__scopeTooltip),r=fi(rn,e.__scopeTooltip),o=d.useRef(null),s=ie(t,o),[i,a]=d.useState(null),{trigger:l,onClose:u}=n,c=o.current,{onPointerInTransitChange:h}=r,p=d.useCallback(()=>{a(null),h(!1)},[h]),m=d.useCallback((v,g)=>{const y=v.currentTarget,x={x:v.clientX,y:v.clientY},w=Pw(x,y.getBoundingClientRect()),b=Rw(x,w),S=Mw(g.getBoundingClientRect()),C=Nw([...b,...S]);a(C),h(!0)},[h]);return d.useEffect(()=>()=>p(),[p]),d.useEffect(()=>{if(l&&c){const v=y=>m(y,c),g=y=>m(y,l);return l.addEventListener("pointerleave",v),c.addEventListener("pointerleave",g),()=>{l.removeEventListener("pointerleave",v),c.removeEventListener("pointerleave",g)}}},[l,c,m,p]),d.useEffect(()=>{if(i){const v=g=>{const y=g.target,x={x:g.clientX,y:g.clientY},w=l?.contains(y)||c?.contains(y),b=!Dw(x,i);w?p():b&&(p(),u())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[l,c,i,u,p]),f.jsx(Td,{...e,ref:s})}),[Cw,Tw]=Kr(Ln,{isInside:!1}),Ew=Hf("TooltipContent"),Td=d.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...a}=e,l=Gr(rn,n),u=Hr(n),{onClose:c}=l;return d.useEffect(()=>(document.addEventListener(is,c),()=>document.removeEventListener(is,c)),[c]),d.useEffect(()=>{if(l.trigger){const h=p=>{p.target?.contains(l.trigger)&&c()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[l.trigger,c]),f.jsx(Vr,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:h=>h.preventDefault(),onDismiss:c,children:f.jsxs(gd,{"data-state":l.stateAttribute,...u,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[f.jsx(Ew,{children:r}),f.jsx(Cw,{scope:n,isInside:!0,children:f.jsx(gw,{id:l.contentId,role:"tooltip",children:o||r})})]})})});Cd.displayName=rn;var Ed="TooltipArrow",Aw=d.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Hr(n);return Tw(Ed,n).isInside?null:f.jsx(vd,{...o,...r,ref:t})});Aw.displayName=Ed;function Pw(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Rw(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Mw(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Dw(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s],l=t[i],u=a.x,c=a.y,h=l.x,p=l.y;c>r!=p>r&&n<(h-u)*(r-c)/(p-c)+u&&(o=!o)}return o}function Nw(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),jw(t)}function jw(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var kw=bd,_w=wd,Ow=Sd,Ad=Cd;const Iw=kw,Lw=_w,Vw=Ow,Pd=d.forwardRef(({className:e,sideOffset:t=4,...n},r)=>f.jsx(Ad,{ref:r,sideOffset:t,className:V("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));Pd.displayName=Ad.displayName;const Fw="sidebar_state",Bw=3600*24*7,$w="20rem",Uw="22rem",zw="3rem",Ww="b",Rd=d.createContext(null);function Yr(){const e=d.useContext(Rd);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const Md=d.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:s,...i},a)=>{const l=Ay(),[u,c]=d.useState(!1),[h,p]=d.useState(e),m=t??h,v=d.useCallback(w=>{const b=typeof w=="function"?w(m):w;n?n(b):p(b),document.cookie=`${Fw}=${b}; path=/; max-age=${Bw}`},[n,m]),g=d.useCallback(()=>l?c(w=>!w):v(w=>!w),[l,v,c]);d.useEffect(()=>{const w=b=>{b.key===Ww&&(b.metaKey||b.ctrlKey)&&(b.preventDefault(),g())};return window.addEventListener("keydown",w),()=>window.removeEventListener("keydown",w)},[g]);const y=m?"expanded":"collapsed",x=d.useMemo(()=>({state:y,open:m,setOpen:v,isMobile:l,openMobile:u,setOpenMobile:c,toggleSidebar:g}),[y,m,v,l,u,c,g]);return f.jsx(Rd.Provider,{value:x,children:f.jsx(Iw,{delayDuration:0,children:f.jsx("div",{style:{"--sidebar-width":$w,"--sidebar-width-icon":zw,...o},className:V("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:a,...i,children:s})})})});Md.displayName="SidebarProvider";const Kw=d.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...s},i)=>{const{isMobile:a,state:l,openMobile:u,setOpenMobile:c}=Yr();return n==="none"?f.jsx("div",{className:V("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...s,children:o}):a?f.jsx(Xx,{open:u,onOpenChange:c,...s,children:f.jsx(Uu,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":Uw},side:e,children:f.jsx("div",{className:"flex h-full w-full flex-col",children:o})})}):f.jsxs("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":l,"data-collapsible":l==="collapsed"?n:"","data-variant":t,"data-side":e,children:[f.jsx("div",{className:V("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),f.jsx("div",{className:V("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...s,children:f.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:o})})]})});Kw.displayName="Sidebar";const Hw=d.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:o}=Yr();return f.jsxs(De,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:V("h-7 w-7",e),onClick:s=>{t?.(s),o()},...n,children:[f.jsx(bh,{}),f.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});Hw.displayName="SidebarTrigger";const Gw=d.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=Yr();return f.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:V("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});Gw.displayName="SidebarRail";const Yw=d.forwardRef(({className:e,...t},n)=>f.jsx("main",{ref:n,className:V("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));Yw.displayName="SidebarInset";const Xw=d.forwardRef(({className:e,...t},n)=>f.jsx(Ir,{ref:n,"data-sidebar":"input",className:V("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));Xw.displayName="SidebarInput";const qw=d.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,"data-sidebar":"header",className:V("flex flex-col gap-2 p-2",e),...t}));qw.displayName="SidebarHeader";const Zw=d.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,"data-sidebar":"footer",className:V("flex flex-col gap-2 p-2",e),...t}));Zw.displayName="SidebarFooter";const Jw=d.forwardRef(({className:e,...t},n)=>f.jsx(Gf,{ref:n,"data-sidebar":"separator",className:V("mx-2 w-auto bg-sidebar-border",e),...t}));Jw.displayName="SidebarSeparator";const Qw=d.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,"data-sidebar":"content",className:V("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));Qw.displayName="SidebarContent";const e0=d.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,"data-sidebar":"group",className:V("relative flex w-full min-w-0 flex-col p-2",e),...t}));e0.displayName="SidebarGroup";const t0=d.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Bn:"div";return f.jsx(o,{ref:r,"data-sidebar":"group-label",className:V("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});t0.displayName="SidebarGroupLabel";const n0=d.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const o=t?Bn:"button";return f.jsx(o,{ref:r,"data-sidebar":"group-action",className:V("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});n0.displayName="SidebarGroupAction";const r0=d.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,"data-sidebar":"group-content",className:V("w-full text-sm",e),...t}));r0.displayName="SidebarGroupContent";const o0=d.forwardRef(({className:e,...t},n)=>f.jsx("ul",{ref:n,"data-sidebar":"menu",className:V("flex w-full min-w-0 flex-col gap-1",e),...t}));o0.displayName="SidebarMenu";const s0=d.forwardRef(({className:e,...t},n)=>f.jsx("li",{ref:n,"data-sidebar":"menu-item",className:V("group/menu-item relative",e),...t}));s0.displayName="SidebarMenuItem";const i0=ms("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!size-8"}},defaultVariants:{variant:"default",size:"default"}}),a0=d.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:s,...i},a)=>{const l=e?Bn:"button",{isMobile:u,state:c}=Yr(),h=f.jsx(l,{ref:a,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:V(i0({variant:n,size:r}),s),...i});return o?(typeof o=="string"&&(o={children:o}),f.jsxs(Lw,{children:[f.jsx(Vw,{asChild:!0,children:h}),f.jsx(Pd,{side:"right",align:"center",hidden:c!=="collapsed"||u,...o})]})):h});a0.displayName="SidebarMenuButton";const l0=d.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},o)=>{const s=t?Bn:"button";return f.jsx(s,{ref:o,"data-sidebar":"menu-action",className:V("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});l0.displayName="SidebarMenuAction";const c0=d.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:V("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));c0.displayName="SidebarMenuBadge";const u0=d.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const o=d.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return f.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:V("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&f.jsx(Qa,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),f.jsx(Qa,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})});u0.displayName="SidebarMenuSkeleton";const d0=d.forwardRef(({className:e,...t},n)=>f.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:V("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));d0.displayName="SidebarMenuSub";const f0=d.forwardRef(({...e},t)=>f.jsx("li",{ref:t,...e}));f0.displayName="SidebarMenuSubItem";const h0=d.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...o},s)=>{const i=e?Bn:"a";return f.jsx(i,{ref:s,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:V("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...o})});h0.displayName="SidebarMenuSubButton";function p0({children:e,isCollapsed:t,onToggle:n}){const{conversations:r,currentConversationId:o,createConversation:s,deleteConversation:i,updateConversationTitle:a,setCurrentConversation:l}=Ey(),[u,c]=d.useState(null),[h,p]=d.useState(""),[m,v]=d.useState(!1),[g,y]=d.useState(!1),[x,w]=d.useState(!1),b=t!==void 0?t:m,S=()=>{s()},C=(O,E)=>{E.stopPropagation(),i(O)},R=(O,E,M)=>{M.stopPropagation(),c(O),p(E)},T=O=>{h.trim()&&a(O,h.trim()),c(null),p("")},P=()=>{c(null),p("")},A=()=>{b?(n?n():v(!1),setTimeout(()=>{y(!1)},150)):(y(!0),setTimeout(()=>{n?n():v(!0)},150))};d.useEffect(()=>{b?y(!0):setTimeout(()=>{y(!1)},300)},[b]),d.useEffect(()=>{const O=()=>{w(window.innerWidth<768)};return O(),window.addEventListener("resize",O),()=>window.removeEventListener("resize",O)},[]),d.useEffect(()=>{x&&!m&&t===void 0&&v(!0)},[x]),d.useEffect(()=>{const O=E=>{E.key==="b"&&(E.ctrlKey||E.metaKey)&&(E.preventDefault(),A())};return window.addEventListener("keydown",O),()=>window.removeEventListener("keydown",O)},[b]);const I=()=>{x&&!b&&A()},F=O=>{const E=O instanceof Date?O:new Date(O);if(isNaN(E.getTime()))return"无效日期";const L=new Date().getTime()-E.getTime(),_=Math.floor(L/(1e3*60*60*24));return _===0?"今天":_===1?"昨天":_<7?`${_}天前`:E.toLocaleDateString("zh-CN")};return f.jsxs(Md,{defaultOpen:!0,children:[x&&!b&&f.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:I}),x&&f.jsx(De,{variant:"outline",size:"sm",className:"fixed top-4 left-4 z-50 md:hidden h-10 w-10 p-0",onClick:A,"aria-label":b?"展开侧边栏":"收起侧边栏",children:f.jsx(fh,{className:"h-4 w-4"})}),f.jsxs("div",{className:V("flex min-h-screen w-full",x?"relative":""),children:[f.jsx("div",{className:V("bg-sidebar border-r border-border transition-all duration-300 ease-in-out z-50",!x&&"flex-shrink-0",!x&&(b?"w-0 overflow-hidden":"w-80"),x&&"fixed top-0 left-0 h-full",x&&(b?"-translate-x-full":"translate-x-0 w-80")),children:f.jsxs("div",{className:"h-full flex flex-col",children:[f.jsx("div",{className:V("border-b px-4 h-16 py-3 transition-opacity duration-150 ease-in-out",g?"opacity-0":"opacity-100"),children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("h2",{className:"text-lg font-semibold",children:"对话历史"}),f.jsx(De,{onClick:S,size:"sm",variant:"outline",className:"h-8 w-8 p-0",children:f.jsx(Th,{className:"h-4 w-4"})})]})}),f.jsx("div",{className:V("flex-1 overflow-auto transition-opacity duration-150 ease-in-out",g?"opacity-0":"opacity-100"),children:f.jsx("div",{className:"p-2",children:r.length===0?f.jsx("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:"暂无对话历史"}):f.jsx("div",{className:"space-y-1",children:r.map(O=>f.jsxs("div",{className:V("group relative flex items-center gap-3 rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",o===O.id&&"bg-accent"),onClick:()=>l(O.id),children:[f.jsx(ph,{className:"h-4 w-4 flex-shrink-0"}),f.jsx("div",{className:"flex-1 min-w-0",children:u===O.id?f.jsxs("div",{className:"flex items-center gap-1",onClick:E=>E.stopPropagation(),children:[f.jsx(Ir,{value:h,onChange:E=>p(E.target.value),className:"h-6 text-xs",onKeyDown:E=>{E.key==="Enter"?T(O.id):E.key==="Escape"&&P()},autoFocus:!0}),f.jsx(De,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>T(O.id),children:f.jsx(Ml,{className:"h-3 w-3"})}),f.jsx(De,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:P,children:f.jsx(Nr,{className:"h-3 w-3"})})]}):f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"font-medium truncate",children:O.title}),f.jsxs("div",{className:"text-xs text-muted-foreground",children:[F(O.updatedAt)," •"," ",O.messages.length," 条消息"]})]})}),u!==O.id&&f.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[f.jsx(De,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:E=>R(O.id,O.title,E),children:f.jsx(Sh,{className:"h-3 w-3"})}),f.jsx(De,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0 text-destructive hover:text-destructive",onClick:E=>C(O.id,E),children:f.jsx(Ah,{className:"h-3 w-3"})})]})]},O.id))})})}),f.jsx("div",{className:V("border-t px-4 py-3 transition-opacity duration-150 ease-in-out",g?"opacity-0":"opacity-100"),children:f.jsxs("div",{className:"text-xs text-muted-foreground",children:["共 ",r.length," 个对话"]})})]})}),f.jsx("main",{className:V("flex-1 flex flex-col min-w-0 w-full relative",x&&"w-full"),children:e})]})]})}var Ro={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var cl;function m0(){return cl||(cl=1,function(e){(function(){var t={}.hasOwnProperty;function n(){for(var s="",i=0;i<arguments.length;i++){var a=arguments[i];a&&(s=o(s,r(a)))}return s}function r(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return n.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var i="";for(var a in s)t.call(s,a)&&s[a]&&(i=o(i,a));return i}function o(s,i){return i?s?s+" "+i:s+i:s}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(Ro)),Ro.exports}var g0=m0();const ls=bl(g0);var ul={};const v0="react-tooltip-core-styles",y0="react-tooltip-base-styles",dl={core:!1,base:!1};function fl({css:e,id:t=y0,type:n="base",ref:r}){var o,s;if(!e||typeof document>"u"||dl[n]||n==="core"&&typeof process<"u"&&(!((o=process==null?void 0:ul)===null||o===void 0)&&o.REACT_TOOLTIP_DISABLE_CORE_STYLES)||n!=="base"&&typeof process<"u"&&(!((s=process==null?void 0:ul)===null||s===void 0)&&s.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;n==="core"&&(t=v0),r||(r={});const{insertAt:i}=r;if(document.getElementById(t))return;const a=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.id=t,l.type="text/css",i==="top"&&a.firstChild?a.insertBefore(l,a.firstChild):a.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e)),dl[n]=!0}const hl=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:n=null,place:r="top",offset:o=10,strategy:s="absolute",middlewares:i=[ed(Number(o)),nd({fallbackAxisSideDirection:"start"}),td({padding:5})],border:a,arrowSize:l=8})=>{if(!e)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};if(t===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:r};const u=i;return n?(u.push(os({element:n,padding:5})),ss(e,t,{placement:r,strategy:s,middleware:u}).then(({x:c,y:h,placement:p,middlewareData:m})=>{var v,g;const y={left:`${c}px`,top:`${h}px`,border:a},{x,y:w}=(v=m.arrow)!==null&&v!==void 0?v:{x:0,y:0},b=(g={top:"bottom",right:"left",bottom:"top",left:"right"}[p.split("-")[0]])!==null&&g!==void 0?g:"bottom",S=a&&{borderBottom:a,borderRight:a};let C=0;if(a){const R=`${a}`.match(/(\d+)px/);C=R?.[1]?Number(R[1]):1}return{tooltipStyles:y,tooltipArrowStyles:{left:x!=null?`${x}px`:"",top:w!=null?`${w}px`:"",right:"",bottom:"",...S,[b]:`-${l/2+C}px`},place:p}})):ss(e,t,{placement:"bottom",strategy:s,middleware:u}).then(({x:c,y:h,placement:p})=>({tooltipStyles:{left:`${c}px`,top:`${h}px`},tooltipArrowStyles:{},place:p}))},pl=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),ml=(e,t,n)=>{let r=null;const o=function(...s){const i=()=>{r=null};!r&&(e.apply(this,s),r=setTimeout(i,t))};return o.cancel=()=>{r&&(clearTimeout(r),r=null)},o},gl=e=>e!==null&&!Array.isArray(e)&&typeof e=="object",cs=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((o,s)=>cs(o,t[s]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!gl(e)||!gl(t))return e===t;const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(o=>cs(e[o],t[o]))},x0=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;const t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(n=>{const r=t.getPropertyValue(n);return r==="auto"||r==="scroll"})},vl=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(x0(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},b0=typeof window<"u"?d.useLayoutEffect:d.useEffect,Re=e=>{e.current&&(clearTimeout(e.current),e.current=null)},w0="DEFAULT_TOOLTIP_ID",S0={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},C0=d.createContext({getTooltipData:()=>S0});function Dd(e=w0){return d.useContext(C0).getTooltipData(e)}var Ht={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},Mo={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const T0=({forwardRef:e,id:t,className:n,classNameArrow:r,variant:o="dark",anchorId:s,anchorSelect:i,place:a="top",offset:l=10,events:u=["hover"],openOnClick:c=!1,positionStrategy:h="absolute",middlewares:p,wrapper:m,delayShow:v=0,delayHide:g=0,float:y=!1,hidden:x=!1,noArrow:w=!1,clickable:b=!1,closeOnEsc:S=!1,closeOnScroll:C=!1,closeOnResize:R=!1,openEvents:T,closeEvents:P,globalCloseEvents:A,imperativeModeOnly:I,style:F,position:O,afterShow:E,afterHide:M,disableTooltip:L,content:_,contentWrapperRef:D,isOpen:j,defaultIsOpen:N=!1,setIsOpen:G,activeAnchor:H,setActiveAnchor:ve,border:Fe,opacity:Be,arrowColor:Qe,arrowSize:ye=8,role:Z="tooltip"})=>{var ke;const le=d.useRef(null),at=d.useRef(null),be=d.useRef(null),$e=d.useRef(null),Ue=d.useRef(null),[lt,Jr]=d.useState({tooltipStyles:{},tooltipArrowStyles:{},place:a}),[we,qn]=d.useState(!1),[yt,xt]=d.useState(!1),[J,un]=d.useState(null),dn=d.useRef(!1),fn=d.useRef(null),{anchorRefs:hn,setActiveAnchor:Zn}=Dd(t),Ot=d.useRef(!1),[ct,pn]=d.useState([]),bt=d.useRef(!1),It=c||u.includes("click"),Qr=It||T?.click||T?.dblclick||T?.mousedown,Lt=T?{...T}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!T&&It&&Object.assign(Lt,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const wt=P?{...P}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!P&&It&&Object.assign(wt,{mouseleave:!1,blur:!1,mouseout:!1});const _e=A?{...A}:{escape:S||!1,scroll:C||!1,resize:R||!1,clickOutsideAnchor:Qr||!1};I&&(Object.assign(Lt,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(wt,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(_e,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),b0(()=>(bt.current=!0,()=>{bt.current=!1}),[]);const re=k=>{bt.current&&(k&&xt(!0),setTimeout(()=>{bt.current&&(G?.(k),j===void 0&&qn(k))},10))};d.useEffect(()=>{if(j===void 0)return()=>null;j&&xt(!0);const k=setTimeout(()=>{qn(j)},10);return()=>{clearTimeout(k)}},[j]),d.useEffect(()=>{if(we!==dn.current)if(Re(Ue),dn.current=we,we)E?.();else{const k=(U=>{const W=U.match(/^([\d.]+)(ms|s)$/);if(!W)return 0;const[,se,fe]=W;return Number(se)*(fe==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));Ue.current=setTimeout(()=>{xt(!1),un(null),M?.()},k+25)}},[we]);const Jn=k=>{Jr(U=>cs(U,k)?U:k)},mn=(k=v)=>{Re(be),yt?re(!0):be.current=setTimeout(()=>{re(!0)},k)},Vt=(k=g)=>{Re($e),$e.current=setTimeout(()=>{Ot.current||re(!1)},k)},gn=k=>{var U;if(!k)return;const W=(U=k.currentTarget)!==null&&U!==void 0?U:k.target;if(!W?.isConnected)return ve(null),void Zn({current:null});v?mn():re(!0),ve(W),Zn({current:W}),Re($e)},Ft=()=>{b?Vt(g||100):g?Vt():re(!1),Re(be)},Bt=({x:k,y:U})=>{var W;const se={getBoundingClientRect:()=>({x:k,y:U,width:0,height:0,top:U,left:k,right:k,bottom:U})};hl({place:(W=J?.place)!==null&&W!==void 0?W:a,offset:l,elementReference:se,tooltipReference:le.current,tooltipArrowReference:at.current,strategy:h,middlewares:p,border:Fe,arrowSize:ye}).then(fe=>{Jn(fe)})},$t=k=>{if(!k)return;const U=k,W={x:U.clientX,y:U.clientY};Bt(W),fn.current=W},vn=k=>{var U;if(!we)return;const W=k.target;W.isConnected&&(!((U=le.current)===null||U===void 0)&&U.contains(W)||[document.querySelector(`[id='${s}']`),...ct].some(se=>se?.contains(W))||(re(!1),Re(be)))},Qn=ml(gn,50),ae=ml(Ft,50),Ee=k=>{ae.cancel(),Qn(k)},B=()=>{Qn.cancel(),ae()},K=d.useCallback(()=>{var k,U;const W=(k=J?.position)!==null&&k!==void 0?k:O;W?Bt(W):y?fn.current&&Bt(fn.current):H?.isConnected&&hl({place:(U=J?.place)!==null&&U!==void 0?U:a,offset:l,elementReference:H,tooltipReference:le.current,tooltipArrowReference:at.current,strategy:h,middlewares:p,border:Fe,arrowSize:ye}).then(se=>{bt.current&&Jn(se)})},[we,H,_,F,a,J?.place,l,h,O,J?.position,y,ye]);d.useEffect(()=>{var k,U;const W=new Set(hn);ct.forEach(Y=>{L?.(Y)||W.add({current:Y})});const se=document.querySelector(`[id='${s}']`);se&&!L?.(se)&&W.add({current:se});const fe=()=>{re(!1)},ze=vl(H),We=vl(le.current);_e.scroll&&(window.addEventListener("scroll",fe),ze?.addEventListener("scroll",fe),We?.addEventListener("scroll",fe));let pe=null;_e.resize?window.addEventListener("resize",fe):H&&le.current&&(pe=Qu(H,le.current,K,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const Ae=Y=>{Y.key==="Escape"&&re(!1)};_e.escape&&window.addEventListener("keydown",Ae),_e.clickOutsideAnchor&&window.addEventListener("click",vn);const X=[],Ut=Y=>!!(Y?.target&&H?.contains(Y.target)),If=Y=>{we&&Ut(Y)||gn(Y)},Lf=Y=>{we&&Ut(Y)&&Ft()},bi=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],wi=["click","dblclick","mousedown","mouseup"];Object.entries(Lt).forEach(([Y,et])=>{et&&(bi.includes(Y)?X.push({event:Y,listener:Ee}):wi.includes(Y)&&X.push({event:Y,listener:If}))}),Object.entries(wt).forEach(([Y,et])=>{et&&(bi.includes(Y)?X.push({event:Y,listener:B}):wi.includes(Y)&&X.push({event:Y,listener:Lf}))}),y&&X.push({event:"pointermove",listener:$t});const Si=()=>{Ot.current=!0},Ci=()=>{Ot.current=!1,Ft()},Ti=b&&(wt.mouseout||wt.mouseleave);return Ti&&((k=le.current)===null||k===void 0||k.addEventListener("mouseover",Si),(U=le.current)===null||U===void 0||U.addEventListener("mouseout",Ci)),X.forEach(({event:Y,listener:et})=>{W.forEach(eo=>{var yn;(yn=eo.current)===null||yn===void 0||yn.addEventListener(Y,et)})}),()=>{var Y,et;_e.scroll&&(window.removeEventListener("scroll",fe),ze?.removeEventListener("scroll",fe),We?.removeEventListener("scroll",fe)),_e.resize?window.removeEventListener("resize",fe):pe?.(),_e.clickOutsideAnchor&&window.removeEventListener("click",vn),_e.escape&&window.removeEventListener("keydown",Ae),Ti&&((Y=le.current)===null||Y===void 0||Y.removeEventListener("mouseover",Si),(et=le.current)===null||et===void 0||et.removeEventListener("mouseout",Ci)),X.forEach(({event:eo,listener:yn})=>{W.forEach(Vf=>{var to;(to=Vf.current)===null||to===void 0||to.removeEventListener(eo,yn)})})}},[H,K,yt,hn,ct,T,P,A,It,v,g]),d.useEffect(()=>{var k,U;let W=(U=(k=J?.anchorSelect)!==null&&k!==void 0?k:i)!==null&&U!==void 0?U:"";!W&&t&&(W=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);const se=new MutationObserver(fe=>{const ze=[],We=[];fe.forEach(pe=>{if(pe.type==="attributes"&&pe.attributeName==="data-tooltip-id"&&(pe.target.getAttribute("data-tooltip-id")===t?ze.push(pe.target):pe.oldValue===t&&We.push(pe.target)),pe.type==="childList"){if(H){const Ae=[...pe.removedNodes].filter(X=>X.nodeType===1);if(W)try{We.push(...Ae.filter(X=>X.matches(W))),We.push(...Ae.flatMap(X=>[...X.querySelectorAll(W)]))}catch{}Ae.some(X=>{var Ut;return!!(!((Ut=X?.contains)===null||Ut===void 0)&&Ut.call(X,H))&&(xt(!1),re(!1),ve(null),Re(be),Re($e),!0)})}if(W)try{const Ae=[...pe.addedNodes].filter(X=>X.nodeType===1);ze.push(...Ae.filter(X=>X.matches(W))),ze.push(...Ae.flatMap(X=>[...X.querySelectorAll(W)]))}catch{}}}),(ze.length||We.length)&&pn(pe=>[...pe.filter(Ae=>!We.includes(Ae)),...ze])});return se.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{se.disconnect()}},[t,i,J?.anchorSelect,H]),d.useEffect(()=>{K()},[K]),d.useEffect(()=>{if(!D?.current)return()=>null;const k=new ResizeObserver(()=>{setTimeout(()=>K())});return k.observe(D.current),()=>{k.disconnect()}},[_,D?.current]),d.useEffect(()=>{var k;const U=document.querySelector(`[id='${s}']`),W=[...ct,U];H&&W.includes(H)||ve((k=ct[0])!==null&&k!==void 0?k:U)},[s,ct,H]),d.useEffect(()=>(N&&re(!0),()=>{Re(be),Re($e)}),[]),d.useEffect(()=>{var k;let U=(k=J?.anchorSelect)!==null&&k!==void 0?k:i;if(!U&&t&&(U=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),U)try{const W=Array.from(document.querySelectorAll(U));pn(W)}catch{pn([])}},[t,i,J?.anchorSelect]),d.useEffect(()=>{be.current&&(Re(be),mn(v))},[v]);const Se=(ke=J?.content)!==null&&ke!==void 0?ke:_,St=we&&Object.keys(lt.tooltipStyles).length>0;return d.useImperativeHandle(e,()=>({open:k=>{if(k?.anchorSelect)try{document.querySelector(k.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${k.anchorSelect}" is not a valid CSS selector`)}un(k??null),k?.delay?mn(k.delay):re(!0)},close:k=>{k?.delay?Vt(k.delay):re(!1)},activeAnchor:H,place:lt.place,isOpen:!!(yt&&!x&&Se&&St)})),yt&&!x&&Se?ue.createElement(m,{id:t,role:Z,className:ls("react-tooltip",Ht.tooltip,Mo.tooltip,Mo[o],n,`react-tooltip__place-${lt.place}`,Ht[St?"show":"closing"],St?"react-tooltip__show":"react-tooltip__closing",h==="fixed"&&Ht.fixed,b&&Ht.clickable),onTransitionEnd:k=>{Re(Ue),we||k.propertyName!=="opacity"||(xt(!1),un(null),M?.())},style:{...F,...lt.tooltipStyles,opacity:Be!==void 0&&St?Be:void 0},ref:le},Se,ue.createElement(m,{className:ls("react-tooltip-arrow",Ht.arrow,Mo.arrow,r,w&&Ht.noArrow),style:{...lt.tooltipArrowStyles,background:Qe?`linear-gradient(to right bottom, transparent 50%, ${Qe} 50%)`:void 0,"--rt-arrow-size":`${ye}px`},ref:at})):null},E0=({content:e})=>ue.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),A0=ue.forwardRef(({id:e,anchorId:t,anchorSelect:n,content:r,html:o,render:s,className:i,classNameArrow:a,variant:l="dark",place:u="top",offset:c=10,wrapper:h="div",children:p=null,events:m=["hover"],openOnClick:v=!1,positionStrategy:g="absolute",middlewares:y,delayShow:x=0,delayHide:w=0,float:b=!1,hidden:S=!1,noArrow:C=!1,clickable:R=!1,closeOnEsc:T=!1,closeOnScroll:P=!1,closeOnResize:A=!1,openEvents:I,closeEvents:F,globalCloseEvents:O,imperativeModeOnly:E=!1,style:M,position:L,isOpen:_,defaultIsOpen:D=!1,disableStyleInjection:j=!1,border:N,opacity:G,arrowColor:H,arrowSize:ve,setIsOpen:Fe,afterShow:Be,afterHide:Qe,disableTooltip:ye,role:Z="tooltip"},ke)=>{const[le,at]=d.useState(r),[be,$e]=d.useState(o),[Ue,lt]=d.useState(u),[Jr,we]=d.useState(l),[qn,yt]=d.useState(c),[xt,J]=d.useState(x),[un,dn]=d.useState(w),[fn,hn]=d.useState(b),[Zn,Ot]=d.useState(S),[ct,pn]=d.useState(h),[bt,It]=d.useState(m),[Qr,Lt]=d.useState(g),[wt,_e]=d.useState(null),[re,Jn]=d.useState(null),mn=d.useRef(j),{anchorRefs:Vt,activeAnchor:gn}=Dd(e),Ft=ae=>ae?.getAttributeNames().reduce((Ee,B)=>{var K;return B.startsWith("data-tooltip-")&&(Ee[B.replace(/^data-tooltip-/,"")]=(K=ae?.getAttribute(B))!==null&&K!==void 0?K:null),Ee},{}),Bt=ae=>{const Ee={place:B=>{var K;lt((K=B)!==null&&K!==void 0?K:u)},content:B=>{at(B??r)},html:B=>{$e(B??o)},variant:B=>{var K;we((K=B)!==null&&K!==void 0?K:l)},offset:B=>{yt(B===null?c:Number(B))},wrapper:B=>{var K;pn((K=B)!==null&&K!==void 0?K:h)},events:B=>{const K=B?.split(" ");It(K??m)},"position-strategy":B=>{var K;Lt((K=B)!==null&&K!==void 0?K:g)},"delay-show":B=>{J(B===null?x:Number(B))},"delay-hide":B=>{dn(B===null?w:Number(B))},float:B=>{hn(B===null?b:B==="true")},hidden:B=>{Ot(B===null?S:B==="true")},"class-name":B=>{_e(B)}};Object.values(Ee).forEach(B=>B(null)),Object.entries(ae).forEach(([B,K])=>{var Se;(Se=Ee[B])===null||Se===void 0||Se.call(Ee,K)})};d.useEffect(()=>{at(r)},[r]),d.useEffect(()=>{$e(o)},[o]),d.useEffect(()=>{lt(u)},[u]),d.useEffect(()=>{we(l)},[l]),d.useEffect(()=>{yt(c)},[c]),d.useEffect(()=>{J(x)},[x]),d.useEffect(()=>{dn(w)},[w]),d.useEffect(()=>{hn(b)},[b]),d.useEffect(()=>{Ot(S)},[S]),d.useEffect(()=>{Lt(g)},[g]),d.useEffect(()=>{mn.current!==j&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[j]),d.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:j==="core",disableBase:j}}))},[]),d.useEffect(()=>{var ae;const Ee=new Set(Vt);let B=n;if(!B&&e&&(B=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),B)try{document.querySelectorAll(B).forEach(U=>{Ee.add({current:U})})}catch{console.warn(`[react-tooltip] "${B}" is not a valid CSS selector`)}const K=document.querySelector(`[id='${t}']`);if(K&&Ee.add({current:K}),!Ee.size)return()=>null;const Se=(ae=re??K)!==null&&ae!==void 0?ae:gn.current,St=new MutationObserver(U=>{U.forEach(W=>{var se;if(!Se||W.type!=="attributes"||!(!((se=W.attributeName)===null||se===void 0)&&se.startsWith("data-tooltip-")))return;const fe=Ft(Se);Bt(fe)})}),k={attributes:!0,childList:!1,subtree:!1};if(Se){const U=Ft(Se);Bt(U),St.observe(Se,k)}return()=>{St.disconnect()}},[Vt,gn,re,t,n]),d.useEffect(()=>{M?.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),N&&!pl("border",`${N}`)&&console.warn(`[react-tooltip] "${N}" is not a valid \`border\`.`),M?.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),G&&!pl("opacity",`${G}`)&&console.warn(`[react-tooltip] "${G}" is not a valid \`opacity\`.`)},[]);let $t=p;const vn=d.useRef(null);if(s){const ae=s({content:re?.getAttribute("data-tooltip-content")||le||null,activeAnchor:re});$t=ae?ue.createElement("div",{ref:vn,className:"react-tooltip-content-wrapper"},ae):null}else le&&($t=le);be&&($t=ue.createElement(E0,{content:be}));const Qn={forwardRef:ke,id:e,anchorId:t,anchorSelect:n,className:ls(i,wt),classNameArrow:a,content:$t,contentWrapperRef:vn,place:Ue,variant:Jr,offset:qn,wrapper:ct,events:bt,openOnClick:v,positionStrategy:Qr,middlewares:y,delayShow:xt,delayHide:un,float:fn,hidden:Zn,noArrow:C,clickable:R,closeOnEsc:T,closeOnScroll:P,closeOnResize:A,openEvents:I,closeEvents:F,globalCloseEvents:O,imperativeModeOnly:E,style:M,position:L,isOpen:_,defaultIsOpen:D,border:N,opacity:G,arrowColor:H,arrowSize:ve,setIsOpen:Fe,afterShow:Be,afterHide:Qe,disableTooltip:ye,activeAnchor:re,setActiveAnchor:ae=>Jn(ae),role:Z};return ue.createElement(T0,{...Qn})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||fl({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||fl({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});const P0=({content:e,side:t="top",children:n})=>{const r=d.useMemo(()=>{const o="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";return Array.from({length:6},()=>o[Math.floor(Math.random()*o.length)]).join("")},[]);return f.jsxs("div",{children:[f.jsx("style",{children:`
          :root {
            --context-card-border: #ebebeb
          }
          .dark {
            --context-card-border: #2e2e2e
          }
        `}),f.jsx("div",{id:r,className:"font-sans",children:n}),f.jsx(A0,{anchorSelect:`#${r}`,place:t,opacity:1,border:"1px solid var(--context-card-border)",className:"!font-sans !text-center !text-base !rounded-lg !bg-background-100 !text-gray-1000",children:e})]})},R0={Trigger:P0};function Nd(e){const t=e+"CollectionProvider",[n,r]=jt(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:y,children:x}=g,w=ue.useRef(null),b=ue.useRef(new Map).current;return f.jsx(o,{scope:y,itemMap:b,collectionRef:w,children:x})};i.displayName=t;const a=e+"CollectionSlot",l=vr(a),u=ue.forwardRef((g,y)=>{const{scope:x,children:w}=g,b=s(a,x),S=ie(y,b.collectionRef);return f.jsx(l,{ref:S,children:w})});u.displayName=a;const c=e+"CollectionItemSlot",h="data-radix-collection-item",p=vr(c),m=ue.forwardRef((g,y)=>{const{scope:x,children:w,...b}=g,S=ue.useRef(null),C=ie(y,S),R=s(c,x);return ue.useEffect(()=>(R.itemMap.set(S,{ref:S,...b}),()=>void R.itemMap.delete(S))),f.jsx(p,{[h]:"",ref:C,children:w})});m.displayName=c;function v(g){const y=s(e+"CollectionConsumer",g);return ue.useCallback(()=>{const w=y.collectionRef.current;if(!w)return[];const b=Array.from(w.querySelectorAll(`[${h}]`));return Array.from(y.itemMap.values()).sort((R,T)=>b.indexOf(R.ref.current)-b.indexOf(T.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:i,Slot:u,ItemSlot:m},v,r]}var M0=d.createContext(void 0);function jd(e){const t=d.useContext(M0);return e||t||"ltr"}var Do="rovingFocusGroup.onEntryFocus",D0={bubbles:!1,cancelable:!0},Gn="RovingFocusGroup",[us,kd,N0]=Nd(Gn),[j0,_d]=jt(Gn,[N0]),[k0,_0]=j0(Gn),Od=d.forwardRef((e,t)=>f.jsx(us.Provider,{scope:e.__scopeRovingFocusGroup,children:f.jsx(us.Slot,{scope:e.__scopeRovingFocusGroup,children:f.jsx(O0,{...e,ref:t})})}));Od.displayName=Gn;var O0=d.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:s,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:c=!1,...h}=e,p=d.useRef(null),m=ie(t,p),v=jd(s),[g,y]=Lr({prop:i,defaultProp:a??null,onChange:l,caller:Gn}),[x,w]=d.useState(!1),b=tt(u),S=kd(n),C=d.useRef(!1),[R,T]=d.useState(0);return d.useEffect(()=>{const P=p.current;if(P)return P.addEventListener(Do,b),()=>P.removeEventListener(Do,b)},[b]),f.jsx(k0,{scope:n,orientation:r,dir:v,loop:o,currentTabStopId:g,onItemFocus:d.useCallback(P=>y(P),[y]),onItemShiftTab:d.useCallback(()=>w(!0),[]),onFocusableItemAdd:d.useCallback(()=>T(P=>P+1),[]),onFocusableItemRemove:d.useCallback(()=>T(P=>P-1),[]),children:f.jsx(te.div,{tabIndex:x||R===0?-1:0,"data-orientation":r,...h,ref:m,style:{outline:"none",...e.style},onMouseDown:z(e.onMouseDown,()=>{C.current=!0}),onFocus:z(e.onFocus,P=>{const A=!C.current;if(P.target===P.currentTarget&&A&&!x){const I=new CustomEvent(Do,D0);if(P.currentTarget.dispatchEvent(I),!I.defaultPrevented){const F=S().filter(_=>_.focusable),O=F.find(_=>_.active),E=F.find(_=>_.id===g),L=[O,E,...F].filter(Boolean).map(_=>_.ref.current);Vd(L,c)}}C.current=!1}),onBlur:z(e.onBlur,()=>w(!1))})})}),Id="RovingFocusGroupItem",Ld=d.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:s,children:i,...a}=e,l=Mt(),u=s||l,c=_0(Id,n),h=c.currentTabStopId===u,p=kd(n),{onFocusableItemAdd:m,onFocusableItemRemove:v,currentTabStopId:g}=c;return d.useEffect(()=>{if(r)return m(),()=>v()},[r,m,v]),f.jsx(us.ItemSlot,{scope:n,id:u,focusable:r,active:o,children:f.jsx(te.span,{tabIndex:h?0:-1,"data-orientation":c.orientation,...a,ref:t,onMouseDown:z(e.onMouseDown,y=>{r?c.onItemFocus(u):y.preventDefault()}),onFocus:z(e.onFocus,()=>c.onItemFocus(u)),onKeyDown:z(e.onKeyDown,y=>{if(y.key==="Tab"&&y.shiftKey){c.onItemShiftTab();return}if(y.target!==y.currentTarget)return;const x=V0(y,c.orientation,c.dir);if(x!==void 0){if(y.metaKey||y.ctrlKey||y.altKey||y.shiftKey)return;y.preventDefault();let b=p().filter(S=>S.focusable).map(S=>S.ref.current);if(x==="last")b.reverse();else if(x==="prev"||x==="next"){x==="prev"&&b.reverse();const S=b.indexOf(y.currentTarget);b=c.loop?F0(b,S+1):b.slice(S+1)}setTimeout(()=>Vd(b))}}),children:typeof i=="function"?i({isCurrentTabStop:h,hasTabStop:g!=null}):i})})});Ld.displayName=Id;var I0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L0(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function V0(e,t,n){const r=L0(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return I0[r]}function Vd(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function F0(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var B0=Od,$0=Ld,ds=["Enter"," "],U0=["ArrowDown","PageUp","Home"],Fd=["ArrowUp","PageDown","End"],z0=[...U0,...Fd],W0={ltr:[...ds,"ArrowRight"],rtl:[...ds,"ArrowLeft"]},K0={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Yn="Menu",[Vn,H0,G0]=Nd(Yn),[kt,Bd]=jt(Yn,[G0,Wr,_d]),Xr=Wr(),$d=_d(),[Y0,_t]=kt(Yn),[X0,Xn]=kt(Yn),Ud=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:i=!0}=e,a=Xr(t),[l,u]=d.useState(null),c=d.useRef(!1),h=tt(s),p=jd(o);return d.useEffect(()=>{const m=()=>{c.current=!0,document.addEventListener("pointerdown",v,{capture:!0,once:!0}),document.addEventListener("pointermove",v,{capture:!0,once:!0})},v=()=>c.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",v,{capture:!0}),document.removeEventListener("pointermove",v,{capture:!0})}},[]),f.jsx(pd,{...a,children:f.jsx(Y0,{scope:t,open:n,onOpenChange:h,content:l,onContentChange:u,children:f.jsx(X0,{scope:t,onClose:d.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:c,dir:p,modal:i,children:r})})})};Ud.displayName=Yn;var q0="MenuAnchor",hi=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Xr(n);return f.jsx(md,{...o,...r,ref:t})});hi.displayName=q0;var pi="MenuPortal",[Z0,zd]=kt(pi,{forceMount:void 0}),Wd=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,s=_t(pi,t);return f.jsx(Z0,{scope:t,forceMount:n,children:f.jsx(it,{present:n||s.open,children:f.jsx(Js,{asChild:!0,container:o,children:r})})})};Wd.displayName=pi;var je="MenuContent",[J0,mi]=kt(je),Kd=d.forwardRef((e,t)=>{const n=zd(je,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=_t(je,e.__scopeMenu),i=Xn(je,e.__scopeMenu);return f.jsx(Vn.Provider,{scope:e.__scopeMenu,children:f.jsx(it,{present:r||s.open,children:f.jsx(Vn.Slot,{scope:e.__scopeMenu,children:i.modal?f.jsx(Q0,{...o,ref:t}):f.jsx(eS,{...o,ref:t})})})})}),Q0=d.forwardRef((e,t)=>{const n=_t(je,e.__scopeMenu),r=d.useRef(null),o=ie(t,r);return d.useEffect(()=>{const s=r.current;if(s)return wu(s)},[]),f.jsx(gi,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:z(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),eS=d.forwardRef((e,t)=>{const n=_t(je,e.__scopeMenu);return f.jsx(gi,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),tS=vr("MenuContent.ScrollLock"),gi=d.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:h,onInteractOutside:p,onDismiss:m,disableOutsideScroll:v,...g}=e,y=_t(je,n),x=Xn(je,n),w=Xr(n),b=$d(n),S=H0(n),[C,R]=d.useState(null),T=d.useRef(null),P=ie(t,T,y.onContentChange),A=d.useRef(0),I=d.useRef(""),F=d.useRef(0),O=d.useRef(null),E=d.useRef("right"),M=d.useRef(0),L=v?Qs:d.Fragment,_=v?{as:tS,allowPinchZoom:!0}:void 0,D=N=>{const G=I.current+N,H=S().filter(Z=>!Z.disabled),ve=document.activeElement,Fe=H.find(Z=>Z.ref.current===ve)?.textValue,Be=H.map(Z=>Z.textValue),Qe=hS(Be,G,Fe),ye=H.find(Z=>Z.textValue===Qe)?.ref.current;(function Z(ke){I.current=ke,window.clearTimeout(A.current),ke!==""&&(A.current=window.setTimeout(()=>Z(""),1e3))})(G),ye&&setTimeout(()=>ye.focus())};d.useEffect(()=>()=>window.clearTimeout(A.current),[]),fu();const j=d.useCallback(N=>E.current===O.current?.side&&mS(N,O.current?.area),[]);return f.jsx(J0,{scope:n,searchRef:I,onItemEnter:d.useCallback(N=>{j(N)&&N.preventDefault()},[j]),onItemLeave:d.useCallback(N=>{j(N)||(T.current?.focus(),R(null))},[j]),onTriggerLeave:d.useCallback(N=>{j(N)&&N.preventDefault()},[j]),pointerGraceTimerRef:F,onPointerGraceIntentChange:d.useCallback(N=>{O.current=N},[]),children:f.jsx(L,{..._,children:f.jsx(Zs,{asChild:!0,trapped:o,onMountAutoFocus:z(s,N=>{N.preventDefault(),T.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:f.jsx(Vr,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:h,onInteractOutside:p,onDismiss:m,children:f.jsx(B0,{asChild:!0,...b,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:C,onCurrentTabStopIdChange:R,onEntryFocus:z(l,N=>{x.isUsingKeyboardRef.current||N.preventDefault()}),preventScrollOnEntryFocus:!0,children:f.jsx(gd,{role:"menu","aria-orientation":"vertical","data-state":lf(y.open),"data-radix-menu-content":"",dir:x.dir,...w,...g,ref:P,style:{outline:"none",...g.style},onKeyDown:z(g.onKeyDown,N=>{const H=N.target.closest("[data-radix-menu-content]")===N.currentTarget,ve=N.ctrlKey||N.altKey||N.metaKey,Fe=N.key.length===1;H&&(N.key==="Tab"&&N.preventDefault(),!ve&&Fe&&D(N.key));const Be=T.current;if(N.target!==Be||!z0.includes(N.key))return;N.preventDefault();const ye=S().filter(Z=>!Z.disabled).map(Z=>Z.ref.current);Fd.includes(N.key)&&ye.reverse(),dS(ye)}),onBlur:z(e.onBlur,N=>{N.currentTarget.contains(N.target)||(window.clearTimeout(A.current),I.current="")}),onPointerMove:z(e.onPointerMove,Fn(N=>{const G=N.target,H=M.current!==N.clientX;if(N.currentTarget.contains(G)&&H){const ve=N.clientX>M.current?"right":"left";E.current=ve,M.current=N.clientX}}))})})})})})})});Kd.displayName=je;var nS="MenuGroup",vi=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return f.jsx(te.div,{role:"group",...r,ref:t})});vi.displayName=nS;var rS="MenuLabel",Hd=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return f.jsx(te.div,{...r,ref:t})});Hd.displayName=rS;var Mr="MenuItem",yl="menu.itemSelect",qr=d.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,s=d.useRef(null),i=Xn(Mr,e.__scopeMenu),a=mi(Mr,e.__scopeMenu),l=ie(t,s),u=d.useRef(!1),c=()=>{const h=s.current;if(!n&&h){const p=new CustomEvent(yl,{bubbles:!0,cancelable:!0});h.addEventListener(yl,m=>r?.(m),{once:!0}),Rl(h,p),p.defaultPrevented?u.current=!1:i.onClose()}};return f.jsx(Gd,{...o,ref:l,disabled:n,onClick:z(e.onClick,c),onPointerDown:h=>{e.onPointerDown?.(h),u.current=!0},onPointerUp:z(e.onPointerUp,h=>{u.current||h.currentTarget?.click()}),onKeyDown:z(e.onKeyDown,h=>{const p=a.searchRef.current!=="";n||p&&h.key===" "||ds.includes(h.key)&&(h.currentTarget.click(),h.preventDefault())})})});qr.displayName=Mr;var Gd=d.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...s}=e,i=mi(Mr,n),a=$d(n),l=d.useRef(null),u=ie(t,l),[c,h]=d.useState(!1),[p,m]=d.useState("");return d.useEffect(()=>{const v=l.current;v&&m((v.textContent??"").trim())},[s.children]),f.jsx(Vn.ItemSlot,{scope:n,disabled:r,textValue:o??p,children:f.jsx($0,{asChild:!0,...a,focusable:!r,children:f.jsx(te.div,{role:"menuitem","data-highlighted":c?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...s,ref:u,onPointerMove:z(e.onPointerMove,Fn(v=>{r?i.onItemLeave(v):(i.onItemEnter(v),v.defaultPrevented||v.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:z(e.onPointerLeave,Fn(v=>i.onItemLeave(v))),onFocus:z(e.onFocus,()=>h(!0)),onBlur:z(e.onBlur,()=>h(!1))})})})}),oS="MenuCheckboxItem",Yd=d.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return f.jsx(Qd,{scope:e.__scopeMenu,checked:n,children:f.jsx(qr,{role:"menuitemcheckbox","aria-checked":Dr(n)?"mixed":n,...o,ref:t,"data-state":xi(n),onSelect:z(o.onSelect,()=>r?.(Dr(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Yd.displayName=oS;var Xd="MenuRadioGroup",[sS,iS]=kt(Xd,{value:void 0,onValueChange:()=>{}}),qd=d.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,s=tt(r);return f.jsx(sS,{scope:e.__scopeMenu,value:n,onValueChange:s,children:f.jsx(vi,{...o,ref:t})})});qd.displayName=Xd;var Zd="MenuRadioItem",Jd=d.forwardRef((e,t)=>{const{value:n,...r}=e,o=iS(Zd,e.__scopeMenu),s=n===o.value;return f.jsx(Qd,{scope:e.__scopeMenu,checked:s,children:f.jsx(qr,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":xi(s),onSelect:z(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});Jd.displayName=Zd;var yi="MenuItemIndicator",[Qd,aS]=kt(yi,{checked:!1}),ef=d.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,s=aS(yi,n);return f.jsx(it,{present:r||Dr(s.checked)||s.checked===!0,children:f.jsx(te.span,{...o,ref:t,"data-state":xi(s.checked)})})});ef.displayName=yi;var lS="MenuSeparator",tf=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return f.jsx(te.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});tf.displayName=lS;var cS="MenuArrow",nf=d.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Xr(n);return f.jsx(vd,{...o,...r,ref:t})});nf.displayName=cS;var uS="MenuSub",[NC,rf]=kt(uS),wn="MenuSubTrigger",of=d.forwardRef((e,t)=>{const n=_t(wn,e.__scopeMenu),r=Xn(wn,e.__scopeMenu),o=rf(wn,e.__scopeMenu),s=mi(wn,e.__scopeMenu),i=d.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},c=d.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return d.useEffect(()=>c,[c]),d.useEffect(()=>{const h=a.current;return()=>{window.clearTimeout(h),l(null)}},[a,l]),f.jsx(hi,{asChild:!0,...u,children:f.jsx(Gd,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":lf(n.open),...e,ref:Pl(t,o.onTriggerChange),onClick:h=>{e.onClick?.(h),!(e.disabled||h.defaultPrevented)&&(h.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:z(e.onPointerMove,Fn(h=>{s.onItemEnter(h),!h.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(s.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),c()},100))})),onPointerLeave:z(e.onPointerLeave,Fn(h=>{c();const p=n.content?.getBoundingClientRect();if(p){const m=n.content?.dataset.side,v=m==="right",g=v?-5:5,y=p[v?"left":"right"],x=p[v?"right":"left"];s.onPointerGraceIntentChange({area:[{x:h.clientX+g,y:h.clientY},{x:y,y:p.top},{x,y:p.top},{x,y:p.bottom},{x:y,y:p.bottom}],side:m}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(h),h.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:z(e.onKeyDown,h=>{const p=s.searchRef.current!=="";e.disabled||p&&h.key===" "||W0[r.dir].includes(h.key)&&(n.onOpenChange(!0),n.content?.focus(),h.preventDefault())})})})});of.displayName=wn;var sf="MenuSubContent",af=d.forwardRef((e,t)=>{const n=zd(je,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,s=_t(je,e.__scopeMenu),i=Xn(je,e.__scopeMenu),a=rf(sf,e.__scopeMenu),l=d.useRef(null),u=ie(t,l);return f.jsx(Vn.Provider,{scope:e.__scopeMenu,children:f.jsx(it,{present:r||s.open,children:f.jsx(Vn.Slot,{scope:e.__scopeMenu,children:f.jsx(gi,{id:a.contentId,"aria-labelledby":a.triggerId,...o,ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:c=>{i.isUsingKeyboardRef.current&&l.current?.focus(),c.preventDefault()},onCloseAutoFocus:c=>c.preventDefault(),onFocusOutside:z(e.onFocusOutside,c=>{c.target!==a.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:z(e.onEscapeKeyDown,c=>{i.onClose(),c.preventDefault()}),onKeyDown:z(e.onKeyDown,c=>{const h=c.currentTarget.contains(c.target),p=K0[i.dir].includes(c.key);h&&p&&(s.onOpenChange(!1),a.trigger?.focus(),c.preventDefault())})})})})})});af.displayName=sf;function lf(e){return e?"open":"closed"}function Dr(e){return e==="indeterminate"}function xi(e){return Dr(e)?"indeterminate":e?"checked":"unchecked"}function dS(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function fS(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function hS(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=fS(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const l=i.find(u=>u.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}function pS(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const a=t[s],l=t[i],u=a.x,c=a.y,h=l.x,p=l.y;c>r!=p>r&&n<(h-u)*(r-c)/(p-c)+u&&(o=!o)}return o}function mS(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return pS(n,t)}function Fn(e){return t=>t.pointerType==="mouse"?e(t):void 0}var gS=Ud,vS=hi,yS=Wd,xS=Kd,bS=vi,wS=Hd,SS=qr,CS=Yd,TS=qd,ES=Jd,AS=ef,PS=tf,RS=nf,MS=of,DS=af,Zr="DropdownMenu",[NS,jC]=jt(Zr,[Bd]),ge=Bd(),[jS,cf]=NS(Zr),uf=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:i,modal:a=!0}=e,l=ge(t),u=d.useRef(null),[c,h]=Lr({prop:o,defaultProp:s??!1,onChange:i,caller:Zr});return f.jsx(jS,{scope:t,triggerId:Mt(),triggerRef:u,contentId:Mt(),open:c,onOpenChange:h,onOpenToggle:d.useCallback(()=>h(p=>!p),[h]),modal:a,children:f.jsx(gS,{...l,open:c,onOpenChange:h,dir:r,modal:a,children:n})})};uf.displayName=Zr;var df="DropdownMenuTrigger",ff=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,s=cf(df,n),i=ge(n);return f.jsx(vS,{asChild:!0,...i,children:f.jsx(te.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Pl(t,s.triggerRef),onPointerDown:z(e.onPointerDown,a=>{!r&&a.button===0&&a.ctrlKey===!1&&(s.onOpenToggle(),s.open||a.preventDefault())}),onKeyDown:z(e.onKeyDown,a=>{r||(["Enter"," "].includes(a.key)&&s.onOpenToggle(),a.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});ff.displayName=df;var kS="DropdownMenuPortal",hf=e=>{const{__scopeDropdownMenu:t,...n}=e,r=ge(t);return f.jsx(yS,{...r,...n})};hf.displayName=kS;var pf="DropdownMenuContent",mf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=cf(pf,n),s=ge(n),i=d.useRef(!1);return f.jsx(xS,{id:o.contentId,"aria-labelledby":o.triggerId,...s,...r,ref:t,onCloseAutoFocus:z(e.onCloseAutoFocus,a=>{i.current||o.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:z(e.onInteractOutside,a=>{const l=a.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,c=l.button===2||u;(!o.modal||c)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});mf.displayName=pf;var _S="DropdownMenuGroup",OS=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(bS,{...o,...r,ref:t})});OS.displayName=_S;var IS="DropdownMenuLabel",gf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(wS,{...o,...r,ref:t})});gf.displayName=IS;var LS="DropdownMenuItem",vf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(SS,{...o,...r,ref:t})});vf.displayName=LS;var VS="DropdownMenuCheckboxItem",yf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(CS,{...o,...r,ref:t})});yf.displayName=VS;var FS="DropdownMenuRadioGroup",BS=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(TS,{...o,...r,ref:t})});BS.displayName=FS;var $S="DropdownMenuRadioItem",xf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(ES,{...o,...r,ref:t})});xf.displayName=$S;var US="DropdownMenuItemIndicator",bf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(AS,{...o,...r,ref:t})});bf.displayName=US;var zS="DropdownMenuSeparator",wf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(PS,{...o,...r,ref:t})});wf.displayName=zS;var WS="DropdownMenuArrow",KS=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(RS,{...o,...r,ref:t})});KS.displayName=WS;var HS="DropdownMenuSubTrigger",Sf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(MS,{...o,...r,ref:t})});Sf.displayName=HS;var GS="DropdownMenuSubContent",Cf=d.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=ge(n);return f.jsx(DS,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Cf.displayName=GS;var YS=uf,XS=ff,qS=hf,Tf=mf,Ef=gf,Af=vf,Pf=yf,Rf=xf,Mf=bf,Df=wf,Nf=Sf,jf=Cf;const ZS=YS,JS=XS,QS=d.forwardRef(({className:e,inset:t,children:n,...r},o)=>f.jsxs(Nf,{ref:o,className:V("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,f.jsx(eh,{className:"ml-auto h-4 w-4"})]}));QS.displayName=Nf.displayName;const eC=d.forwardRef(({className:e,...t},n)=>f.jsx(jf,{ref:n,className:V("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));eC.displayName=jf.displayName;const kf=d.forwardRef(({className:e,sideOffset:t=4,...n},r)=>f.jsx(qS,{children:f.jsx(Tf,{ref:r,sideOffset:t,className:V("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));kf.displayName=Tf.displayName;const fs=d.forwardRef(({className:e,inset:t,...n},r)=>f.jsx(Af,{ref:r,className:V("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));fs.displayName=Af.displayName;const tC=d.forwardRef(({className:e,children:t,checked:n,...r},o)=>f.jsxs(Pf,{ref:o,className:V("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[f.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:f.jsx(Mf,{children:f.jsx(Ml,{className:"h-4 w-4"})})}),t]}));tC.displayName=Pf.displayName;const nC=d.forwardRef(({className:e,children:t,...n},r)=>f.jsxs(Rf,{ref:r,className:V("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[f.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:f.jsx(Mf,{children:f.jsx(oh,{className:"h-2 w-2 fill-current"})})}),t]}));nC.displayName=Rf.displayName;const _f=d.forwardRef(({className:e,inset:t,...n},r)=>f.jsx(Ef,{ref:r,className:V("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));_f.displayName=Ef.displayName;const hs=d.forwardRef(({className:e,...t},n)=>f.jsx(Df,{ref:n,className:V("-mx-1 my-1 h-px bg-muted",e),...t}));hs.displayName=Df.displayName;function rC({className:e}){const t=Sl(),{logout:n}=Al(),{user:r}=Kf(),o=a=>a.split(" ").map(l=>l[0]).join("").toUpperCase().slice(0,2),s=()=>{t({to:"/user"})},i=()=>{n(),t({to:"/auth/login"})};return f.jsxs(ZS,{children:[f.jsx(JS,{asChild:!0,children:f.jsx(De,{variant:"ghost",size:"sm",className:`h-8 w-8 rounded-full p-0 ${e}`,children:f.jsxs(Cl,{className:"h-8 w-8",children:[f.jsx(Tl,{src:r?.avatar||"",alt:r?.name||"用户"}),f.jsx(El,{className:"text-xs",children:o(r?.name||"用户")})]})})}),f.jsxs(kf,{align:"end",className:"w-56",children:[f.jsx(_f,{className:"font-normal",children:f.jsx("div",{className:"flex flex-col space-y-1",children:f.jsx("p",{className:"text-sm font-medium leading-none",children:r?.name||"用户"})})}),f.jsx(hs,{}),f.jsxs(fs,{onClick:s,children:[f.jsx(Xf,{className:"mr-2 h-4 w-4"}),f.jsx("span",{children:"账号管理"})]}),f.jsx(hs,{}),f.jsxs(fs,{onClick:i,children:[f.jsx(uh,{className:"mr-2 h-4 w-4"}),f.jsx("span",{children:"退出登录"})]})]})]})}let ps=null,Sn=null;const Of=async()=>{if(typeof window>"u")throw new Error("File reading is only supported on the client side");ps||(ps=await Ei(()=>import("./index-Cq3MfCug.js").then(e=>e.i),__vite__mapDeps([0,1]))),Sn||(Sn=await Ei(()=>import("./pdf-CtA8PhPd.js"),[]),Sn.GlobalWorkerOptions.workerSrc=`//cdnjs.cloudflare.com/ajax/libs/pdf.js/${Sn.version}/pdf.worker.min.js`)};async function oC(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=o=>{const s=o.target?.result;t(s)},r.onerror=()=>n(new Error("Failed to read text file")),r.readAsText(e,"utf-8")})}async function sC(e){return await Of(),new Promise((t,n)=>{const r=new FileReader;r.onload=async o=>{try{const s=o.target?.result,i=await ps.extractRawText({arrayBuffer:s});t(i.value)}catch(s){n(new Error(`Failed to read Word file: ${s}`))}},r.onerror=()=>n(new Error("Failed to read Word file")),r.readAsArrayBuffer(e)})}async function iC(e){return await Of(),new Promise((t,n)=>{const r=new FileReader;r.onload=async o=>{try{const s=o.target?.result,i=await Sn.getDocument({data:s}).promise;let a="";for(let l=1;l<=i.numPages;l++){const h=(await(await i.getPage(l)).getTextContent()).items.map(p=>p.str).join(" ");a+=`Page ${l}:
${h}

`}t(a)}catch(s){n(new Error(`Failed to read PDF file: ${s}`))}},r.onerror=()=>n(new Error("Failed to read PDF file")),r.readAsArrayBuffer(e)})}async function aC(e){const n=e.name.toLowerCase().split(".").pop();console.log(`Reading file: ${e.name} (${e.size} bytes)`),console.log(`File type: ${e.type}`),console.log(`File extension: ${n}`);try{let r="";if(n==="txt"||e.type==="text/plain")r=await oC(e);else if(n==="docx"||e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document")r=await sC(e);else if(n==="pdf"||e.type==="application/pdf")r=await iC(e);else throw new Error(`Unsupported file type: ${n}. Only TXT, DOCX, and PDF files are supported.`);return console.log(`Successfully read file content (${r.length} characters)`),console.log("File content preview:",r.substring(0,200)+"..."),r}catch(r){throw console.error("Error reading file:",r),r}}function lC(e){const n=e.name.toLowerCase().split(".").pop(),r=["txt","docx","pdf"],o=["text/plain","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/pdf"];return r.includes(n||"")||o.includes(e.type)}function cC(){return f.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[f.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:f.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),f.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:f.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),f.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:f.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}function uC({variant:e="received",layout:t="default",className:n,children:r}){return f.jsx("div",{className:V("flex items-start gap-2 mb-4",e==="sent"&&"flex-row-reverse",n),children:r})}function dC({variant:e="received",isLoading:t,className:n,children:r}){return f.jsx("div",{className:V("rounded-lg p-3",e==="sent"?"bg-primary text-primary-foreground":"bg-muted",n),children:t?f.jsx("div",{className:"flex items-center space-x-2",children:f.jsx(cC,{})}):r})}function fC({src:e,fallback:t="AI",className:n}){return f.jsxs(Cl,{className:V("h-8 w-8",n),children:[e&&f.jsx(Tl,{src:e}),f.jsx(El,{children:t})]})}function hC({icon:e,onClick:t,className:n}){return f.jsx(De,{variant:"ghost",size:"icon",className:V("h-6 w-6",n),onClick:t,children:e})}function pC({className:e,children:t}){return f.jsx("div",{className:V("flex items-center gap-1 mt-2",e),children:t})}function mC({children:e,redirectTo:t="/auth/login"}){const{isAuthenticated:n,isLoading:r}=Al(),o=Sl();return ue.useEffect(()=>{if(!r&&!n){const s=window.location.pathname;s!==t&&localStorage.setItem("redirectAfterLogin",s),o({to:t})}},[r,n,o,t]),r?f.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:f.jsxs("div",{className:"text-center",children:[f.jsx(No,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),f.jsx("p",{className:"text-gray-600",children:"正在验证登录状态..."})]})}):n?f.jsx(f.Fragment,{children:e}):f.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:f.jsxs("div",{className:"text-center",children:[f.jsx(No,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),f.jsx("p",{className:"text-gray-600",children:"正在跳转到登录页面..."})]})})}const xl=["知识库查询","方案改进","计算"];function gC(){const[e,t]=d.useState(xl[0]),[n,r]=d.useState(!1),[o,s]=d.useState([]),[i,a]=d.useState(null),[l,u]=d.useState({}),c="sk-5ff58b88af7343b7bbb388079e1442f2",h="622fbd2ef57c413baafa29527d205414",p="38669993697942e6a8ac1a9f1aa591e0",[m,v]=d.useState(null),[g,y]=d.useState(""),[x,w]=d.useState([]),b=d.useCallback(E=>{v(E),y(""),w([])},[]),S=E=>{w(E),console.log("=== 格式化显示结果 ==="),E.forEach((M,L)=>{console.log(`%c问题 ${L+1}:`,"color: #e74c3c; font-weight: bold;"),console.log(`%c原文: ${M.origin}`,"color: #3498db;"),console.log(`%c问题描述: ${M.issueDes}`,"color: #f39c12;"),console.log(`%c改进建议: ${M.suggestion}`,"color: #27ae60;"),console.log(`%c依据: ${M.reason}`,"color: #9b59b6;"),console.log("---")})},C=async E=>{try{console.log("=== 数据库更新 ==="),console.log(`Token 使用量: ${E}`),console.log("请求次数: +1"),console.log("需要更新用户的 token 余额和请求次数")}catch(M){console.error("更新数据库失败:",M)}},R=async E=>{try{const M=await fetch(`https://dashscope.aliyuncs.com/api/v1/apps/${p}/completion`,{method:"POST",headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"},body:JSON.stringify({input:{prompt:E},parameters:{},debug:{}})});if(!M.ok)throw new Error(`API 请求失败: ${M.status} ${M.statusText}`);const L=await M.json();if(console.log("=== DashScope API 响应 ==="),console.log("完整响应数据:"),console.log(JSON.stringify(L,null,2)),L.output&&L.output.text)try{const _=L.output.text.replace(/```json\n?/,"").replace(/\n?```$/,""),D=JSON.parse(_);if(console.log("=== 解析后的结果 ==="),console.log(D),S(D),L.usage&&L.usage.models&&L.usage.models[0]){const j=L.usage.models[0].input_tokens+L.usage.models[0].output_tokens;await C(j)}}catch(_){console.error("解析 JSON 失败:",_),console.log("原始 text 内容:",L.output.text)}console.log("=== API 响应完成 ===")}catch(M){throw console.error("API 请求失败:",M),M}},T=()=>{r(!n)},P=(E,M)=>{u(L=>{const _=L[E]||new Set,D=new Set(_);return D.has(M)?D.delete(M):D.add(M),{...L,[E]:D}})},A=async(E,M)=>{console.log("=== API调用调试信息 ==="),console.log("API_KEY:",c),console.log("APP_ID:",h),console.log("环境变量 VITE_DASHSCOPE_API_KEY:","sk-5ff58b88af7343b7bbb388079e1442f2"),console.log("环境变量 VITE_YOUR_APP_ID:","622fbd2ef57c413baafa29527d205414"),console.log("========================");const L=`https://dashscope.aliyuncs.com/api/v1/apps/${h}/completion`,_={input:{prompt:E},parameters:{},debug:{}};M&&(_.input.session_id=M);const D=await fetch(L,{method:"POST",headers:{Authorization:`Bearer ${c}`,"Content-Type":"application/json"},body:JSON.stringify(_)});if(!D.ok)throw new Error(`API请求失败: ${D.status} ${D.statusText}`);return await D.json()},I=async E=>{const M={id:Date.now().toString(),content:E,sender:"user",timestamp:new Date};s(_=>[..._,M]);const L={id:(Date.now()+1).toString(),content:"",sender:"ai",timestamp:new Date,isLoading:!0};s(_=>[..._,L]);try{const _=await A(E,i||void 0);_.output.session_id&&a(_.output.session_id);const D={id:L.id,content:_.output.text,sender:"ai",timestamp:new Date,isLoading:!1,docReferences:_.output.doc_references};s(j=>j.map(N=>N.id===L.id?D:N))}catch(_){console.error("API调用失败:",_);const D={id:L.id,content:`抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${_ instanceof Error?_.message:"未知错误"}`,sender:"ai",timestamp:new Date,isLoading:!1};s(j=>j.map(N=>N.id===L.id?D:N))}},F=async E=>{try{await navigator.clipboard.writeText(E),console.log("复制成功")}catch(M){console.error("复制失败:",M);const L=document.createElement("textarea");L.value=E,document.body.appendChild(L),L.select(),document.execCommand("copy"),document.body.removeChild(L)}},O=async()=>{if(!m){alert("请先选择一个文件");return}if(!lC(m)){alert("不支持的文件类型。请选择 TXT、PDF 或 DOCX 文件。");return}try{console.log("=== 开始读取文件 ==="),console.log("文件名:",m.name),console.log("文件大小:",m.size,"bytes"),console.log("文件类型:",m.type);const E=await aC(m);console.log("=== 文件内容 ==="),console.log("内容长度:",E.length,"字符"),console.log("文件内容:"),console.log(E),console.log("=== 文件读取完成 ==="),y(E),console.log("=== 开始发送到 DashScope API ==="),await R(E)}catch(E){console.error("处理失败:",E),y(""),alert(`处理失败: ${E instanceof Error?E.message:"未知错误"}`)}};return f.jsx(p0,{isCollapsed:n,onToggle:T,children:f.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[f.jsx("div",{className:"bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0",children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx(De,{variant:"ghost",size:"sm",onClick:T,className:"h-8 w-8 p-0",title:n?"展开侧边栏 (Ctrl+B)":"收起侧边栏 (Ctrl+B)",children:n?f.jsx(yh,{className:"h-4 w-4"}):f.jsx(gh,{className:"h-4 w-4"})}),f.jsx("div",{className:"flex justify-center flex-1",children:f.jsx("div",{className:"flex w-fit rounded-full bg-muted p-1",children:xl.map(E=>f.jsx(Ty,{text:E,selected:e===E,setSelected:t,discount:E==="方案改进"},E))})}),f.jsx(rC,{className:"ml-4"})]})}),f.jsx("main",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:e==="知识库查询"?f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"flex-1 overflow-y-auto p-6 pb-0 min-h-0",children:o.length===0?f.jsxs("div",{className:"text-center text-gray-500 mt-20",children:[f.jsx("p",{className:"text-lg mb-2",children:"👋 欢迎使用库无忧"}),f.jsx("p",{children:"开始对话，快速检索石化油储行业"}),i&&f.jsxs("p",{className:"text-sm mt-4 text-blue-600",children:["🔗 多轮对话已启用 (会话ID: ",i.slice(0,8),"...)"]})]}):f.jsx("div",{className:"max-w-4xl mx-auto space-y-4",children:o.map(E=>{const M=E.sender==="user"?"sent":"received";return f.jsxs("div",{className:"space-y-2",children:[E.sender==="ai"&&E.docReferences&&E.docReferences.length>0&&f.jsx("div",{className:"space-y-2 mb-2 ml-12",children:f.jsx("div",{className:"flex flex-wrap gap-2",children:E.docReferences.map((L,_)=>{const D=l[E.id]?.has(_)||!1;return f.jsxs("div",{className:"space-y-1",children:[f.jsx(R0.Trigger,{content:L.doc_name,side:"top",children:f.jsx(Yf,{variant:"outline",className:"text-xs cursor-pointer hover:bg-gray-100",onClick:()=>P(E.id,_),children:L.index_id})}),D&&f.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-md border text-sm",children:[f.jsx("div",{className:"font-medium text-gray-700 mb-1",children:L.doc_name}),f.jsx("div",{className:"text-gray-600",children:L.text})]})]},_)})})}),f.jsxs(uC,{variant:M,children:[f.jsx(fC,{src:M==="sent"?"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=64&h=64&q=80&crop=faces&fit=crop":"https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=64&h=64&q=80&crop=faces&fit=crop",fallback:M==="sent"?"用户":"AI"}),f.jsxs("div",{className:"flex-1",children:[f.jsx(dC,{variant:M,isLoading:E.isLoading,children:E.content}),E.sender==="ai"&&!E.isLoading&&f.jsx(pC,{children:f.jsx(hC,{icon:f.jsx(ih,{className:"h-3 w-3"}),onClick:()=>F(E.content)})})]})]})]},E.id)})})}),f.jsx("div",{className:"w-full flex justify-center flex-shrink-0 p-6 pt-0",children:f.jsx("div",{className:"w-full max-w-3xl",children:f.jsx(wy,{onSend:I})})})]}):f.jsx(f.Fragment,{children:f.jsx("div",{className:"flex-1 flex flex-col p-6",children:x.length===0?f.jsxs("div",{className:"flex-1 flex flex-col justify-center items-center",children:[f.jsxs("div",{className:"text-center text-gray-500 mb-6",children:[f.jsx("p",{className:"text-lg mb-2",children:"📄 方案改进"}),f.jsx("p",{children:"上传文件进行智能分析和优化建议"})]}),f.jsxs("div",{className:"w-full max-w-md space-y-4",children:[f.jsx(Cy,{onFileSelect:b,fileContent:g}),f.jsx(De,{onClick:O,disabled:!m,className:"w-full",children:"分析文件内容"})]})]}):f.jsxs("div",{className:"w-full max-w-4xl mx-auto",children:[f.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"📋 分析结果"}),f.jsx(De,{onClick:()=>{w([]),y(""),v(null)},variant:"outline",size:"sm",children:"重新分析"})]}),f.jsx("div",{className:"space-y-6",children:x.map((E,M)=>f.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:[f.jsx("div",{className:"mb-4",children:f.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:["问题 ",M+1]})}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{children:[f.jsx("h4",{className:"text-sm font-semibold text-blue-700 mb-2",children:"📝 原文内容"}),f.jsx("p",{className:"text-gray-700 bg-blue-50 p-3 rounded-md border-l-4 border-blue-400",children:E.origin})]}),f.jsxs("div",{children:[f.jsx("h4",{className:"text-sm font-semibold text-orange-700 mb-2",children:"⚠️ 问题描述"}),f.jsx("p",{className:"text-gray-700 bg-orange-50 p-3 rounded-md border-l-4 border-orange-400",children:E.issueDes})]}),f.jsxs("div",{children:[f.jsx("h4",{className:"text-sm font-semibold text-green-700 mb-2",children:"💡 改进建议"}),f.jsx("p",{className:"text-gray-700 bg-green-50 p-3 rounded-md border-l-4 border-green-400",children:E.suggestion})]}),f.jsxs("div",{children:[f.jsx("h4",{className:"text-sm font-semibold text-purple-700 mb-2",children:"📚 依据说明"}),f.jsx("p",{className:"text-gray-700 bg-purple-50 p-3 rounded-md border-l-4 border-purple-400",children:E.reason})]})]})]},M))})]})})})})]})})}const kC=function(){return f.jsx(mC,{children:f.jsx(gC,{})})};export{kC as component};
