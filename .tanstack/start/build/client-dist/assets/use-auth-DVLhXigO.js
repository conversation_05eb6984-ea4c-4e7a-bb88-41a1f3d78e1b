import{R as E,r as I}from"./main-CXx9abZr.js";const O=e=>{let n;const t=new Set,o=(u,l)=>{const d=typeof u=="function"?u(n):u;if(!Object.is(d,n)){const f=n;n=l??(typeof d!="object"||d===null)?d:Object.assign({},n,d),t.forEach(S=>S(n,f))}},a=()=>n,i={setState:o,getState:a,getInitialState:()=>g,subscribe:u=>(t.add(u),()=>t.delete(u))},g=n=e(o,a,i);return i},_=e=>e?O(e):O,w=e=>e;function H(e,n=w){const t=E.useSyncExternalStore(e.subscribe,()=>n(e.getState()),()=>n(e.getInitialState()));return E.useDebugValue(t),t}const U=e=>{const n=_(e),t=o=>H(n,o);return Object.assign(t,n),t},R=e=>e?U(e):U;function j(e,n){let t;try{t=e()}catch{return}return{getItem:a=>{var r;const m=g=>g===null?null:JSON.parse(g,void 0),i=(r=t.getItem(a))!=null?r:null;return i instanceof Promise?i.then(m):m(i)},setItem:(a,r)=>t.setItem(a,JSON.stringify(r,void 0)),removeItem:a=>t.removeItem(a)}}const b=e=>n=>{try{const t=e(n);return t instanceof Promise?t:{then(o){return b(o)(t)},catch(o){return this}}}catch(t){return{then(o){return this},catch(o){return b(o)(t)}}}},A=(e,n)=>(t,o,a)=>{let r={storage:j(()=>localStorage),partialize:s=>s,version:0,merge:(s,v)=>({...v,...s}),...n},m=!1;const i=new Set,g=new Set;let u=r.storage;if(!u)return e((...s)=>{console.warn(`[zustand persist middleware] Unable to update item '${r.name}', the given storage is currently unavailable.`),t(...s)},o,a);const l=()=>{const s=r.partialize({...o()});return u.setItem(r.name,{state:s,version:r.version})},d=a.setState;a.setState=(s,v)=>{d(s,v),l()};const f=e((...s)=>{t(...s),l()},o,a);a.getInitialState=()=>f;let S;const k=()=>{var s,v;if(!u)return;m=!1,i.forEach(c=>{var h;return c((h=o())!=null?h:f)});const y=((v=r.onRehydrateStorage)==null?void 0:v.call(r,(s=o())!=null?s:f))||void 0;return b(u.getItem.bind(u))(r.name).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==r.version){if(r.migrate){const h=r.migrate(c.state,c.version);return h instanceof Promise?h.then(p=>[!0,p]):[!0,h]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,c.state];return[!1,void 0]}).then(c=>{var h;const[p,T]=c;if(S=r.merge(T,(h=o())!=null?h:f),t(S,!0),p)return l()}).then(()=>{y?.(S,void 0),S=o(),m=!0,g.forEach(c=>c(S))}).catch(c=>{y?.(void 0,c)})};return a.persist={setOptions:s=>{r={...r,...s},s.storage&&(u=s.storage)},clearStorage:()=>{u?.removeItem(r.name)},getOptions:()=>r,rehydrate:()=>k(),hasHydrated:()=>m,onHydrate:s=>(i.add(s),()=>{i.delete(s)}),onFinishHydration:s=>(g.add(s),()=>{g.delete(s)})},r.skipHydration||k(),S||f},J=A,N=R()(J((e,n)=>({user:null,loading:!1,error:null,setUser:t=>{e({user:t,error:null})},setLoading:t=>{e({loading:t})},setError:t=>{e({error:t,loading:!1})},clearUser:()=>{e({user:null,error:null,loading:!1})},updateUser:t=>{const o=n().user;o&&e({user:{...o,...t}})}}),{name:"user-store",partialize:e=>({user:e.user})}));function x(){const[e,n]=I.useState(null),[t,o]=I.useState(!0),[a,r]=I.useState(!1),m=()=>{try{const l=localStorage.getItem("user"),d=localStorage.getItem("authToken");if(l&&d){const f=JSON.parse(l);n(f),r(!0)}else n(null),r(!1)}catch(l){console.error("检查认证状态失败:",l),n(null),r(!1)}finally{o(!1)}},i=(l,d)=>{try{localStorage.setItem("user",JSON.stringify(l)),localStorage.setItem("authToken",d),n(l),r(!0)}catch(f){throw console.error("登录失败:",f),new Error("登录信息保存失败")}},g=()=>{try{localStorage.removeItem("user"),localStorage.removeItem("authToken"),n(null),r(!1)}catch(l){console.error("登出失败:",l)}},u=()=>{try{return localStorage.getItem("authToken")}catch(l){return console.error("获取认证令牌失败:",l),null}};return I.useEffect(()=>{m()},[]),{user:e,isLoading:t,isAuthenticated:a,login:i,logout:g,getAuthToken:u,checkAuthStatus:m}}export{x as a,R as c,J as p,N as u};
