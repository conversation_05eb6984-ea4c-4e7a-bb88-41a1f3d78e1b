const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/user-C4NI_B9l.js","assets/loader-circle-DOZEO2hA.js","assets/separator-DeQipmPh.js","assets/button-DSogGQaG.js","assets/index-DFx3G0N8.js","assets/createLucideIcon-6fKCiQbJ.js","assets/card-CNyreOkC.js","assets/user-CphpYdS2.js","assets/use-auth-DVLhXigO.js","assets/user-2Hxu6S1o.js","assets/mail-lKUE6PZL.js","assets/setting-BniCEFhQ.js","assets/badge-CJmvZTiH.js","assets/index-DplnlYdB.js","assets/index-BCEHtj6m.js","assets/index-BCJK7tzR.js","assets/user-BGDbajKV.js","assets/register-R_X2xmaS.js","assets/lock-vkzT8a76.js","assets/login-DnDHC_si.js","assets/callback-CBP9_-Zy.js"])))=>i.map(i=>d[i]);
function Ly(i,r){for(var c=0;c<r.length;c++){const s=r[c];if(typeof s!="string"&&!Array.isArray(s)){for(const f in s)if(f!=="default"&&!(f in i)){const h=Object.getOwnPropertyDescriptor(s,f);h&&Object.defineProperty(i,f,h.get?h:{enumerable:!0,get:()=>s[f]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}var Ep=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function lv(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}function Rp(i){if(Object.prototype.hasOwnProperty.call(i,"__esModule"))return i;var r=i.default;if(typeof r=="function"){var c=function s(){var f=!1;try{f=this instanceof s}catch{}return f?Reflect.construct(r,arguments,this.constructor):r.apply(this,arguments)};c.prototype=r.prototype}else c={};return Object.defineProperty(c,"__esModule",{value:!0}),Object.keys(i).forEach(function(s){var f=Object.getOwnPropertyDescriptor(i,s);Object.defineProperty(c,s,f.get?f:{enumerable:!0,get:function(){return i[s]}})}),c}var Fs={exports:{}},au={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ah;function By(){if(Ah)return au;Ah=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function c(s,f,h){var y=null;if(h!==void 0&&(y=""+h),f.key!==void 0&&(y=""+f.key),"key"in f){h={};for(var g in f)g!=="key"&&(h[g]=f[g])}else h=f;return f=h.ref,{$$typeof:i,type:s,key:y,ref:f!==void 0?f:null,props:h}}return au.Fragment=r,au.jsx=c,au.jsxs=c,au}var Oh;function Hy(){return Oh||(Oh=1,Fs.exports=By()),Fs.exports}var Q=Hy(),Is={exports:{}},dt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xh;function jy(){if(xh)return dt;xh=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),y=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),_=Symbol.for("react.lazy"),p=Symbol.iterator;function S(E){return E===null||typeof E!="object"?null:(E=p&&E[p]||E["@@iterator"],typeof E=="function"?E:null)}var A={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,z={};function H(E,L,X){this.props=E,this.context=L,this.refs=z,this.updater=X||A}H.prototype.isReactComponent={},H.prototype.setState=function(E,L){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,L,"setState")},H.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function q(){}q.prototype=H.prototype;function lt(E,L,X){this.props=E,this.context=L,this.refs=z,this.updater=X||A}var J=lt.prototype=new q;J.constructor=lt,R(J,H.prototype),J.isPureReactComponent=!0;var k=Array.isArray,K={H:null,A:null,T:null,S:null,V:null},et=Object.prototype.hasOwnProperty;function G(E,L,X,V,P,nt){return X=nt.ref,{$$typeof:i,type:E,key:L,ref:X!==void 0?X:null,props:nt}}function tt(E,L){return G(E.type,L,void 0,void 0,void 0,E.props)}function it(E){return typeof E=="object"&&E!==null&&E.$$typeof===i}function Y(E){var L={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(X){return L[X]})}var ut=/\/+/g;function F(E,L){return typeof E=="object"&&E!==null&&E.key!=null?Y(""+E.key):L.toString(36)}function mt(){}function bt(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(mt,mt):(E.status="pending",E.then(function(L){E.status==="pending"&&(E.status="fulfilled",E.value=L)},function(L){E.status==="pending"&&(E.status="rejected",E.reason=L)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function pt(E,L,X,V,P){var nt=typeof E;(nt==="undefined"||nt==="boolean")&&(E=null);var $=!1;if(E===null)$=!0;else switch(nt){case"bigint":case"string":case"number":$=!0;break;case"object":switch(E.$$typeof){case i:case r:$=!0;break;case _:return $=E._init,pt($(E._payload),L,X,V,P)}}if($)return P=P(E),$=V===""?"."+F(E,0):V,k(P)?(X="",$!=null&&(X=$.replace(ut,"$&/")+"/"),pt(P,L,X,"",function(Rt){return Rt})):P!=null&&(it(P)&&(P=tt(P,X+(P.key==null||E&&E.key===P.key?"":(""+P.key).replace(ut,"$&/")+"/")+$)),L.push(P)),1;$=0;var _t=V===""?".":V+":";if(k(E))for(var ft=0;ft<E.length;ft++)V=E[ft],nt=_t+F(V,ft),$+=pt(V,L,X,nt,P);else if(ft=S(E),typeof ft=="function")for(E=ft.call(E),ft=0;!(V=E.next()).done;)V=V.value,nt=_t+F(V,ft++),$+=pt(V,L,X,nt,P);else if(nt==="object"){if(typeof E.then=="function")return pt(bt(E),L,X,V,P);throw L=String(E),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.")}return $}function x(E,L,X){if(E==null)return E;var V=[],P=0;return pt(E,V,"","",function(nt){return L.call(X,nt,P++)}),V}function w(E){if(E._status===-1){var L=E._result;L=L(),L.then(function(X){(E._status===0||E._status===-1)&&(E._status=1,E._result=X)},function(X){(E._status===0||E._status===-1)&&(E._status=2,E._result=X)}),E._status===-1&&(E._status=0,E._result=L)}if(E._status===1)return E._result.default;throw E._result}var Z=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var L=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(L))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function ot(){}return dt.Children={map:x,forEach:function(E,L,X){x(E,function(){L.apply(this,arguments)},X)},count:function(E){var L=0;return x(E,function(){L++}),L},toArray:function(E){return x(E,function(L){return L})||[]},only:function(E){if(!it(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},dt.Component=H,dt.Fragment=c,dt.Profiler=f,dt.PureComponent=lt,dt.StrictMode=s,dt.Suspense=m,dt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=K,dt.__COMPILER_RUNTIME={__proto__:null,c:function(E){return K.H.useMemoCache(E)}},dt.cache=function(E){return function(){return E.apply(null,arguments)}},dt.cloneElement=function(E,L,X){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var V=R({},E.props),P=E.key,nt=void 0;if(L!=null)for($ in L.ref!==void 0&&(nt=void 0),L.key!==void 0&&(P=""+L.key),L)!et.call(L,$)||$==="key"||$==="__self"||$==="__source"||$==="ref"&&L.ref===void 0||(V[$]=L[$]);var $=arguments.length-2;if($===1)V.children=X;else if(1<$){for(var _t=Array($),ft=0;ft<$;ft++)_t[ft]=arguments[ft+2];V.children=_t}return G(E.type,P,void 0,void 0,nt,V)},dt.createContext=function(E){return E={$$typeof:y,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:h,_context:E},E},dt.createElement=function(E,L,X){var V,P={},nt=null;if(L!=null)for(V in L.key!==void 0&&(nt=""+L.key),L)et.call(L,V)&&V!=="key"&&V!=="__self"&&V!=="__source"&&(P[V]=L[V]);var $=arguments.length-2;if($===1)P.children=X;else if(1<$){for(var _t=Array($),ft=0;ft<$;ft++)_t[ft]=arguments[ft+2];P.children=_t}if(E&&E.defaultProps)for(V in $=E.defaultProps,$)P[V]===void 0&&(P[V]=$[V]);return G(E,nt,void 0,void 0,null,P)},dt.createRef=function(){return{current:null}},dt.forwardRef=function(E){return{$$typeof:g,render:E}},dt.isValidElement=it,dt.lazy=function(E){return{$$typeof:_,_payload:{_status:-1,_result:E},_init:w}},dt.memo=function(E,L){return{$$typeof:v,type:E,compare:L===void 0?null:L}},dt.startTransition=function(E){var L=K.T,X={};K.T=X;try{var V=E(),P=K.S;P!==null&&P(X,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(ot,Z)}catch(nt){Z(nt)}finally{K.T=L}},dt.unstable_useCacheRefresh=function(){return K.H.useCacheRefresh()},dt.use=function(E){return K.H.use(E)},dt.useActionState=function(E,L,X){return K.H.useActionState(E,L,X)},dt.useCallback=function(E,L){return K.H.useCallback(E,L)},dt.useContext=function(E){return K.H.useContext(E)},dt.useDebugValue=function(){},dt.useDeferredValue=function(E,L){return K.H.useDeferredValue(E,L)},dt.useEffect=function(E,L,X){var V=K.H;if(typeof X=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(E,L)},dt.useId=function(){return K.H.useId()},dt.useImperativeHandle=function(E,L,X){return K.H.useImperativeHandle(E,L,X)},dt.useInsertionEffect=function(E,L){return K.H.useInsertionEffect(E,L)},dt.useLayoutEffect=function(E,L){return K.H.useLayoutEffect(E,L)},dt.useMemo=function(E,L){return K.H.useMemo(E,L)},dt.useOptimistic=function(E,L){return K.H.useOptimistic(E,L)},dt.useReducer=function(E,L,X){return K.H.useReducer(E,L,X)},dt.useRef=function(E){return K.H.useRef(E)},dt.useState=function(E){return K.H.useState(E)},dt.useSyncExternalStore=function(E,L,X){return K.H.useSyncExternalStore(E,L,X)},dt.useTransition=function(){return K.H.useTransition()},dt.version="19.1.0",dt}var Dh;function fu(){return Dh||(Dh=1,Is.exports=jy()),Is.exports}var W=fu();const la=lv(W),Tp=Ly({__proto__:null,default:la},[W]);var to={exports:{}},uu={},eo={exports:{}},lo={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zh;function wy(){return zh||(zh=1,function(i){function r(x,w){var Z=x.length;x.push(w);t:for(;0<Z;){var ot=Z-1>>>1,E=x[ot];if(0<f(E,w))x[ot]=w,x[Z]=E,Z=ot;else break t}}function c(x){return x.length===0?null:x[0]}function s(x){if(x.length===0)return null;var w=x[0],Z=x.pop();if(Z!==w){x[0]=Z;t:for(var ot=0,E=x.length,L=E>>>1;ot<L;){var X=2*(ot+1)-1,V=x[X],P=X+1,nt=x[P];if(0>f(V,Z))P<E&&0>f(nt,V)?(x[ot]=nt,x[P]=Z,ot=P):(x[ot]=V,x[X]=Z,ot=X);else if(P<E&&0>f(nt,Z))x[ot]=nt,x[P]=Z,ot=P;else break t}}return w}function f(x,w){var Z=x.sortIndex-w.sortIndex;return Z!==0?Z:x.id-w.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var y=Date,g=y.now();i.unstable_now=function(){return y.now()-g}}var m=[],v=[],_=1,p=null,S=3,A=!1,R=!1,z=!1,H=!1,q=typeof setTimeout=="function"?setTimeout:null,lt=typeof clearTimeout=="function"?clearTimeout:null,J=typeof setImmediate<"u"?setImmediate:null;function k(x){for(var w=c(v);w!==null;){if(w.callback===null)s(v);else if(w.startTime<=x)s(v),w.sortIndex=w.expirationTime,r(m,w);else break;w=c(v)}}function K(x){if(z=!1,k(x),!R)if(c(m)!==null)R=!0,et||(et=!0,F());else{var w=c(v);w!==null&&pt(K,w.startTime-x)}}var et=!1,G=-1,tt=5,it=-1;function Y(){return H?!0:!(i.unstable_now()-it<tt)}function ut(){if(H=!1,et){var x=i.unstable_now();it=x;var w=!0;try{t:{R=!1,z&&(z=!1,lt(G),G=-1),A=!0;var Z=S;try{e:{for(k(x),p=c(m);p!==null&&!(p.expirationTime>x&&Y());){var ot=p.callback;if(typeof ot=="function"){p.callback=null,S=p.priorityLevel;var E=ot(p.expirationTime<=x);if(x=i.unstable_now(),typeof E=="function"){p.callback=E,k(x),w=!0;break e}p===c(m)&&s(m),k(x)}else s(m);p=c(m)}if(p!==null)w=!0;else{var L=c(v);L!==null&&pt(K,L.startTime-x),w=!1}}break t}finally{p=null,S=Z,A=!1}w=void 0}}finally{w?F():et=!1}}}var F;if(typeof J=="function")F=function(){J(ut)};else if(typeof MessageChannel<"u"){var mt=new MessageChannel,bt=mt.port2;mt.port1.onmessage=ut,F=function(){bt.postMessage(null)}}else F=function(){q(ut,0)};function pt(x,w){G=q(function(){x(i.unstable_now())},w)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(x){x.callback=null},i.unstable_forceFrameRate=function(x){0>x||125<x?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):tt=0<x?Math.floor(1e3/x):5},i.unstable_getCurrentPriorityLevel=function(){return S},i.unstable_next=function(x){switch(S){case 1:case 2:case 3:var w=3;break;default:w=S}var Z=S;S=w;try{return x()}finally{S=Z}},i.unstable_requestPaint=function(){H=!0},i.unstable_runWithPriority=function(x,w){switch(x){case 1:case 2:case 3:case 4:case 5:break;default:x=3}var Z=S;S=x;try{return w()}finally{S=Z}},i.unstable_scheduleCallback=function(x,w,Z){var ot=i.unstable_now();switch(typeof Z=="object"&&Z!==null?(Z=Z.delay,Z=typeof Z=="number"&&0<Z?ot+Z:ot):Z=ot,x){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=Z+E,x={id:_++,callback:w,priorityLevel:x,startTime:Z,expirationTime:E,sortIndex:-1},Z>ot?(x.sortIndex=Z,r(v,x),c(m)===null&&x===c(v)&&(z?(lt(G),G=-1):z=!0,pt(K,Z-ot))):(x.sortIndex=E,r(m,x),R||A||(R=!0,et||(et=!0,F()))),x},i.unstable_shouldYield=Y,i.unstable_wrapCallback=function(x){var w=S;return function(){var Z=S;S=w;try{return x.apply(this,arguments)}finally{S=Z}}}}(lo)),lo}var Ch;function qy(){return Ch||(Ch=1,eo.exports=wy()),eo.exports}var no={exports:{}},ne={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uh;function Yy(){if(Uh)return ne;Uh=1;var i=fu();function r(m){var v="https://react.dev/errors/"+m;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var _=2;_<arguments.length;_++)v+="&args[]="+encodeURIComponent(arguments[_])}return"Minified React error #"+m+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(r(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},f=Symbol.for("react.portal");function h(m,v,_){var p=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:f,key:p==null?null:""+p,children:m,containerInfo:v,implementation:_}}var y=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(m,v){if(m==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return ne.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,ne.createPortal=function(m,v){var _=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(r(299));return h(m,v,null,_)},ne.flushSync=function(m){var v=y.T,_=s.p;try{if(y.T=null,s.p=2,m)return m()}finally{y.T=v,s.p=_,s.d.f()}},ne.preconnect=function(m,v){typeof m=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,s.d.C(m,v))},ne.prefetchDNS=function(m){typeof m=="string"&&s.d.D(m)},ne.preinit=function(m,v){if(typeof m=="string"&&v&&typeof v.as=="string"){var _=v.as,p=g(_,v.crossOrigin),S=typeof v.integrity=="string"?v.integrity:void 0,A=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;_==="style"?s.d.S(m,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:p,integrity:S,fetchPriority:A}):_==="script"&&s.d.X(m,{crossOrigin:p,integrity:S,fetchPriority:A,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},ne.preinitModule=function(m,v){if(typeof m=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var _=g(v.as,v.crossOrigin);s.d.M(m,{crossOrigin:_,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&s.d.M(m)},ne.preload=function(m,v){if(typeof m=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var _=v.as,p=g(_,v.crossOrigin);s.d.L(m,_,{crossOrigin:p,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},ne.preloadModule=function(m,v){if(typeof m=="string")if(v){var _=g(v.as,v.crossOrigin);s.d.m(m,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:_,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else s.d.m(m)},ne.requestFormReset=function(m){s.d.r(m)},ne.unstable_batchedUpdates=function(m,v){return m(v)},ne.useFormState=function(m,v,_){return y.H.useFormState(m,v,_)},ne.useFormStatus=function(){return y.H.useHostTransitionStatus()},ne.version="19.1.0",ne}var Nh;function nv(){if(Nh)return no.exports;Nh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),no.exports=Yy(),no.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lh;function Vy(){if(Lh)return uu;Lh=1;var i=qy(),r=fu(),c=nv();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function y(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function g(t){if(h(t)!==t)throw Error(s(188))}function m(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(s(188));return e!==t?null:t}for(var l=t,n=e;;){var a=l.return;if(a===null)break;var u=a.alternate;if(u===null){if(n=a.return,n!==null){l=n;continue}break}if(a.child===u.child){for(u=a.child;u;){if(u===l)return g(a),t;if(u===n)return g(a),e;u=u.sibling}throw Error(s(188))}if(l.return!==n.return)l=a,n=u;else{for(var o=!1,d=a.child;d;){if(d===l){o=!0,l=a,n=u;break}if(d===n){o=!0,n=a,l=u;break}d=d.sibling}if(!o){for(d=u.child;d;){if(d===l){o=!0,l=u,n=a;break}if(d===n){o=!0,n=u,l=a;break}d=d.sibling}if(!o)throw Error(s(189))}}if(l.alternate!==n)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?t:e}function v(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=v(t),e!==null)return e;t=t.sibling}return null}var _=Object.assign,p=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),A=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),H=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),lt=Symbol.for("react.consumer"),J=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),K=Symbol.for("react.suspense"),et=Symbol.for("react.suspense_list"),G=Symbol.for("react.memo"),tt=Symbol.for("react.lazy"),it=Symbol.for("react.activity"),Y=Symbol.for("react.memo_cache_sentinel"),ut=Symbol.iterator;function F(t){return t===null||typeof t!="object"?null:(t=ut&&t[ut]||t["@@iterator"],typeof t=="function"?t:null)}var mt=Symbol.for("react.client.reference");function bt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===mt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case R:return"Fragment";case H:return"Profiler";case z:return"StrictMode";case K:return"Suspense";case et:return"SuspenseList";case it:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case A:return"Portal";case J:return(t.displayName||"Context")+".Provider";case lt:return(t._context.displayName||"Context")+".Consumer";case k:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case G:return e=t.displayName||null,e!==null?e:bt(t.type)||"Memo";case tt:e=t._payload,t=t._init;try{return bt(t(e))}catch{}}return null}var pt=Array.isArray,x=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,w=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z={pending:!1,data:null,method:null,action:null},ot=[],E=-1;function L(t){return{current:t}}function X(t){0>E||(t.current=ot[E],ot[E]=null,E--)}function V(t,e){E++,ot[E]=t.current,t.current=e}var P=L(null),nt=L(null),$=L(null),_t=L(null);function ft(t,e){switch(V($,e),V(nt,t),V(P,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?th(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=th(e),t=eh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}X(P),V(P,t)}function Rt(){X(P),X(nt),X($)}function Lt(t){t.memoizedState!==null&&V(_t,t);var e=P.current,l=eh(e,t.type);e!==l&&(V(nt,t),V(P,l))}function Ht(t){nt.current===t&&(X(P),X(nt)),_t.current===t&&(X(_t),Ia._currentValue=Z)}var Pt=Object.prototype.hasOwnProperty,qe=i.unstable_scheduleCallback,fn=i.unstable_cancelCallback,qi=i.unstable_shouldYield,Yi=i.unstable_requestPaint,_e=i.unstable_now,dn=i.unstable_getCurrentPriorityLevel,ql=i.unstable_ImmediatePriority,ia=i.unstable_UserBlockingPriority,Yl=i.unstable_NormalPriority,Ut=i.unstable_LowPriority,Wt=i.unstable_IdlePriority,Ye=i.log,Bo=i.unstable_setDisableYieldValue,ca=null,re=null;function vl(t){if(typeof Ye=="function"&&Bo(t),re&&typeof re.setStrictMode=="function")try{re.setStrictMode(ca,t)}catch{}}var fe=Math.clz32?Math.clz32:_v,pv=Math.log,Sv=Math.LN2;function _v(t){return t>>>=0,t===0?32:31-(pv(t)/Sv|0)|0}var du=256,hu=4194304;function Vl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function vu(t,e,l){var n=t.pendingLanes;if(n===0)return 0;var a=0,u=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var d=n&134217727;return d!==0?(n=d&~u,n!==0?a=Vl(n):(o&=d,o!==0?a=Vl(o):l||(l=d&~t,l!==0&&(a=Vl(l))))):(d=n&~u,d!==0?a=Vl(d):o!==0?a=Vl(o):l||(l=n&~t,l!==0&&(a=Vl(l)))),a===0?0:e!==0&&e!==a&&(e&u)===0&&(u=a&-a,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:a}function sa(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function bv(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ho(){var t=du;return du<<=1,(du&4194048)===0&&(du=256),t}function jo(){var t=hu;return hu<<=1,(hu&62914560)===0&&(hu=4194304),t}function Vi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function oa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Ev(t,e,l,n,a,u){var o=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var d=t.entanglements,b=t.expirationTimes,D=t.hiddenUpdates;for(l=o&~l;0<l;){var N=31-fe(l),j=1<<N;d[N]=0,b[N]=-1;var C=D[N];if(C!==null)for(D[N]=null,N=0;N<C.length;N++){var U=C[N];U!==null&&(U.lane&=-536870913)}l&=~j}n!==0&&wo(t,n,0),u!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=u&~(o&~e))}function wo(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-fe(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|l&4194090}function qo(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var n=31-fe(l),a=1<<n;a&e|t[n]&e&&(t[n]|=e),l&=~a}}function Gi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Xi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Yo(){var t=w.p;return t!==0?t:(t=window.event,t===void 0?32:_h(t.type))}function Rv(t,e){var l=w.p;try{return w.p=t,e()}finally{w.p=l}}var ml=Math.random().toString(36).slice(2),ee="__reactFiber$"+ml,ue="__reactProps$"+ml,hn="__reactContainer$"+ml,Qi="__reactEvents$"+ml,Tv="__reactListeners$"+ml,Mv="__reactHandles$"+ml,Vo="__reactResources$"+ml,ra="__reactMarker$"+ml;function Zi(t){delete t[ee],delete t[ue],delete t[Qi],delete t[Tv],delete t[Mv]}function vn(t){var e=t[ee];if(e)return e;for(var l=t.parentNode;l;){if(e=l[hn]||l[ee]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=uh(t);t!==null;){if(l=t[ee])return l;t=uh(t)}return e}t=l,l=t.parentNode}return null}function mn(t){if(t=t[ee]||t[hn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fa(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function yn(t){var e=t[Vo];return e||(e=t[Vo]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Kt(t){t[ra]=!0}var Go=new Set,Xo={};function Gl(t,e){gn(t,e),gn(t+"Capture",e)}function gn(t,e){for(Xo[t]=e,t=0;t<e.length;t++)Go.add(e[t])}var Av=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Qo={},Zo={};function Ov(t){return Pt.call(Zo,t)?!0:Pt.call(Qo,t)?!1:Av.test(t)?Zo[t]=!0:(Qo[t]=!0,!1)}function mu(t,e,l){if(Ov(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function yu(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function ke(t,e,l,n){if(n===null)t.removeAttribute(l);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+n)}}var Ki,Ko;function pn(t){if(Ki===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);Ki=e&&e[1]||"",Ko=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Ki+t+Ko}var Ji=!1;function $i(t,e){if(!t||Ji)return"";Ji=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var j=function(){throw Error()};if(Object.defineProperty(j.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(j,[])}catch(U){var C=U}Reflect.construct(t,[],j)}else{try{j.call()}catch(U){C=U}t.call(j.prototype)}}else{try{throw Error()}catch(U){C=U}(j=t())&&typeof j.catch=="function"&&j.catch(function(){})}}catch(U){if(U&&C&&typeof U.stack=="string")return[U.stack,C.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=n.DetermineComponentFrameRoot(),o=u[0],d=u[1];if(o&&d){var b=o.split(`
`),D=d.split(`
`);for(a=n=0;n<b.length&&!b[n].includes("DetermineComponentFrameRoot");)n++;for(;a<D.length&&!D[a].includes("DetermineComponentFrameRoot");)a++;if(n===b.length||a===D.length)for(n=b.length-1,a=D.length-1;1<=n&&0<=a&&b[n]!==D[a];)a--;for(;1<=n&&0<=a;n--,a--)if(b[n]!==D[a]){if(n!==1||a!==1)do if(n--,a--,0>a||b[n]!==D[a]){var N=`
`+b[n].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=n&&0<=a);break}}}finally{Ji=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?pn(l):""}function xv(t){switch(t.tag){case 26:case 27:case 5:return pn(t.type);case 16:return pn("Lazy");case 13:return pn("Suspense");case 19:return pn("SuspenseList");case 0:case 15:return $i(t.type,!1);case 11:return $i(t.type.render,!1);case 1:return $i(t.type,!0);case 31:return pn("Activity");default:return""}}function Jo(t){try{var e="";do e+=xv(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function be(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function $o(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Dv(t){var e=$o(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),n=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var a=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(o){n=""+o,u.call(this,o)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return n},setValue:function(o){n=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function gu(t){t._valueTracker||(t._valueTracker=Dv(t))}function ko(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),n="";return t&&(n=$o(t)?t.checked?"true":"false":t.value),t=n,t!==l?(e.setValue(t),!0):!1}function pu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var zv=/[\n"\\]/g;function Ee(t){return t.replace(zv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function ki(t,e,l,n,a,u,o,d){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+be(e)):t.value!==""+be(e)&&(t.value=""+be(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?Pi(t,o,be(e)):l!=null?Pi(t,o,be(l)):n!=null&&t.removeAttribute("value"),a==null&&u!=null&&(t.defaultChecked=!!u),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+be(d):t.removeAttribute("name")}function Po(t,e,l,n,a,u,o,d){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+be(l):"",e=e!=null?""+be(e):l,d||e===t.value||(t.value=e),t.defaultValue=e}n=n??a,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=d?t.checked:!!n,t.defaultChecked=!!n,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function Pi(t,e,l){e==="number"&&pu(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function Sn(t,e,l,n){if(t=t.options,e){e={};for(var a=0;a<l.length;a++)e["$"+l[a]]=!0;for(l=0;l<t.length;l++)a=e.hasOwnProperty("$"+t[l].value),t[l].selected!==a&&(t[l].selected=a),a&&n&&(t[l].defaultSelected=!0)}else{for(l=""+be(l),e=null,a=0;a<t.length;a++){if(t[a].value===l){t[a].selected=!0,n&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function Wo(t,e,l){if(e!=null&&(e=""+be(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+be(l):""}function Fo(t,e,l,n){if(e==null){if(n!=null){if(l!=null)throw Error(s(92));if(pt(n)){if(1<n.length)throw Error(s(93));n=n[0]}l=n}l==null&&(l=""),e=l}l=be(e),t.defaultValue=l,n=t.textContent,n===l&&n!==""&&n!==null&&(t.value=n)}function _n(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var Cv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Io(t,e,l){var n=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,l):typeof l!="number"||l===0||Cv.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function tr(t,e,l){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,l!=null){for(var n in l)!l.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var a in e)n=e[a],e.hasOwnProperty(a)&&l[a]!==n&&Io(t,a,n)}else for(var u in e)e.hasOwnProperty(u)&&Io(t,u,e[u])}function Wi(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Uv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Nv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Su(t){return Nv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Fi=null;function Ii(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var bn=null,En=null;function er(t){var e=mn(t);if(e&&(t=e.stateNode)){var l=t[ue]||null;t:switch(t=e.stateNode,e.type){case"input":if(ki(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Ee(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var n=l[e];if(n!==t&&n.form===t.form){var a=n[ue]||null;if(!a)throw Error(s(90));ki(n,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<l.length;e++)n=l[e],n.form===t.form&&ko(n)}break t;case"textarea":Wo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&Sn(t,!!l.multiple,e,!1)}}}var tc=!1;function lr(t,e,l){if(tc)return t(e,l);tc=!0;try{var n=t(e);return n}finally{if(tc=!1,(bn!==null||En!==null)&&(ai(),bn&&(e=bn,t=En,En=bn=null,er(e),t)))for(e=0;e<t.length;e++)er(t[e])}}function da(t,e){var l=t.stateNode;if(l===null)return null;var n=l[ue]||null;if(n===null)return null;l=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(s(231,e,typeof l));return l}var Pe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ec=!1;if(Pe)try{var ha={};Object.defineProperty(ha,"passive",{get:function(){ec=!0}}),window.addEventListener("test",ha,ha),window.removeEventListener("test",ha,ha)}catch{ec=!1}var yl=null,lc=null,_u=null;function nr(){if(_u)return _u;var t,e=lc,l=e.length,n,a="value"in yl?yl.value:yl.textContent,u=a.length;for(t=0;t<l&&e[t]===a[t];t++);var o=l-t;for(n=1;n<=o&&e[l-n]===a[u-n];n++);return _u=a.slice(t,1<n?1-n:void 0)}function bu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Eu(){return!0}function ar(){return!1}function ie(t){function e(l,n,a,u,o){this._reactName=l,this._targetInst=a,this.type=n,this.nativeEvent=u,this.target=o,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(l=t[d],this[d]=l?l(u):u[d]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Eu:ar,this.isPropagationStopped=ar,this}return _(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Eu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Eu)},persist:function(){},isPersistent:Eu}),e}var Xl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ru=ie(Xl),va=_({},Xl,{view:0,detail:0}),Lv=ie(va),nc,ac,ma,Tu=_({},va,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ic,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ma&&(ma&&t.type==="mousemove"?(nc=t.screenX-ma.screenX,ac=t.screenY-ma.screenY):ac=nc=0,ma=t),nc)},movementY:function(t){return"movementY"in t?t.movementY:ac}}),ur=ie(Tu),Bv=_({},Tu,{dataTransfer:0}),Hv=ie(Bv),jv=_({},va,{relatedTarget:0}),uc=ie(jv),wv=_({},Xl,{animationName:0,elapsedTime:0,pseudoElement:0}),qv=ie(wv),Yv=_({},Xl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Vv=ie(Yv),Gv=_({},Xl,{data:0}),ir=ie(Gv),Xv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Qv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Zv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Kv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Zv[t])?!!e[t]:!1}function ic(){return Kv}var Jv=_({},va,{key:function(t){if(t.key){var e=Xv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=bu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Qv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ic,charCode:function(t){return t.type==="keypress"?bu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?bu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),$v=ie(Jv),kv=_({},Tu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),cr=ie(kv),Pv=_({},va,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ic}),Wv=ie(Pv),Fv=_({},Xl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Iv=ie(Fv),tm=_({},Tu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),em=ie(tm),lm=_({},Xl,{newState:0,oldState:0}),nm=ie(lm),am=[9,13,27,32],cc=Pe&&"CompositionEvent"in window,ya=null;Pe&&"documentMode"in document&&(ya=document.documentMode);var um=Pe&&"TextEvent"in window&&!ya,sr=Pe&&(!cc||ya&&8<ya&&11>=ya),or=" ",rr=!1;function fr(t,e){switch(t){case"keyup":return am.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dr(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Rn=!1;function im(t,e){switch(t){case"compositionend":return dr(e);case"keypress":return e.which!==32?null:(rr=!0,or);case"textInput":return t=e.data,t===or&&rr?null:t;default:return null}}function cm(t,e){if(Rn)return t==="compositionend"||!cc&&fr(t,e)?(t=nr(),_u=lc=yl=null,Rn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return sr&&e.locale!=="ko"?null:e.data;default:return null}}var sm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hr(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!sm[t.type]:e==="textarea"}function vr(t,e,l,n){bn?En?En.push(n):En=[n]:bn=n,e=ri(e,"onChange"),0<e.length&&(l=new Ru("onChange","change",null,l,n),t.push({event:l,listeners:e}))}var ga=null,pa=null;function om(t){kd(t,0)}function Mu(t){var e=fa(t);if(ko(e))return t}function mr(t,e){if(t==="change")return e}var yr=!1;if(Pe){var sc;if(Pe){var oc="oninput"in document;if(!oc){var gr=document.createElement("div");gr.setAttribute("oninput","return;"),oc=typeof gr.oninput=="function"}sc=oc}else sc=!1;yr=sc&&(!document.documentMode||9<document.documentMode)}function pr(){ga&&(ga.detachEvent("onpropertychange",Sr),pa=ga=null)}function Sr(t){if(t.propertyName==="value"&&Mu(pa)){var e=[];vr(e,pa,t,Ii(t)),lr(om,e)}}function rm(t,e,l){t==="focusin"?(pr(),ga=e,pa=l,ga.attachEvent("onpropertychange",Sr)):t==="focusout"&&pr()}function fm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Mu(pa)}function dm(t,e){if(t==="click")return Mu(e)}function hm(t,e){if(t==="input"||t==="change")return Mu(e)}function vm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var de=typeof Object.is=="function"?Object.is:vm;function Sa(t,e){if(de(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),n=Object.keys(e);if(l.length!==n.length)return!1;for(n=0;n<l.length;n++){var a=l[n];if(!Pt.call(e,a)||!de(t[a],e[a]))return!1}return!0}function _r(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function br(t,e){var l=_r(t);t=0;for(var n;l;){if(l.nodeType===3){if(n=t+l.textContent.length,t<=e&&n>=e)return{node:l,offset:e-t};t=n}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=_r(l)}}function Er(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Er(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Rr(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=pu(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=pu(t.document)}return e}function rc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var mm=Pe&&"documentMode"in document&&11>=document.documentMode,Tn=null,fc=null,_a=null,dc=!1;function Tr(t,e,l){var n=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;dc||Tn==null||Tn!==pu(n)||(n=Tn,"selectionStart"in n&&rc(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),_a&&Sa(_a,n)||(_a=n,n=ri(fc,"onSelect"),0<n.length&&(e=new Ru("onSelect","select",null,e,l),t.push({event:e,listeners:n}),e.target=Tn)))}function Ql(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Mn={animationend:Ql("Animation","AnimationEnd"),animationiteration:Ql("Animation","AnimationIteration"),animationstart:Ql("Animation","AnimationStart"),transitionrun:Ql("Transition","TransitionRun"),transitionstart:Ql("Transition","TransitionStart"),transitioncancel:Ql("Transition","TransitionCancel"),transitionend:Ql("Transition","TransitionEnd")},hc={},Mr={};Pe&&(Mr=document.createElement("div").style,"AnimationEvent"in window||(delete Mn.animationend.animation,delete Mn.animationiteration.animation,delete Mn.animationstart.animation),"TransitionEvent"in window||delete Mn.transitionend.transition);function Zl(t){if(hc[t])return hc[t];if(!Mn[t])return t;var e=Mn[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Mr)return hc[t]=e[l];return t}var Ar=Zl("animationend"),Or=Zl("animationiteration"),xr=Zl("animationstart"),ym=Zl("transitionrun"),gm=Zl("transitionstart"),pm=Zl("transitioncancel"),Dr=Zl("transitionend"),zr=new Map,vc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");vc.push("scrollEnd");function Le(t,e){zr.set(t,e),Gl(e,[t])}var Cr=new WeakMap;function Re(t,e){if(typeof t=="object"&&t!==null){var l=Cr.get(t);return l!==void 0?l:(e={value:t,source:e,stack:Jo(e)},Cr.set(t,e),e)}return{value:t,source:e,stack:Jo(e)}}var Te=[],An=0,mc=0;function Au(){for(var t=An,e=mc=An=0;e<t;){var l=Te[e];Te[e++]=null;var n=Te[e];Te[e++]=null;var a=Te[e];Te[e++]=null;var u=Te[e];if(Te[e++]=null,n!==null&&a!==null){var o=n.pending;o===null?a.next=a:(a.next=o.next,o.next=a),n.pending=a}u!==0&&Ur(l,a,u)}}function Ou(t,e,l,n){Te[An++]=t,Te[An++]=e,Te[An++]=l,Te[An++]=n,mc|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function yc(t,e,l,n){return Ou(t,e,l,n),xu(t)}function On(t,e){return Ou(t,null,null,e),xu(t)}function Ur(t,e,l){t.lanes|=l;var n=t.alternate;n!==null&&(n.lanes|=l);for(var a=!1,u=t.return;u!==null;)u.childLanes|=l,n=u.alternate,n!==null&&(n.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(a=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,a&&e!==null&&(a=31-fe(l),t=u.hiddenUpdates,n=t[a],n===null?t[a]=[e]:n.push(e),e.lane=l|536870912),u):null}function xu(t){if(50<Za)throw Za=0,Es=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var xn={};function Sm(t,e,l,n){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function he(t,e,l,n){return new Sm(t,e,l,n)}function gc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function We(t,e){var l=t.alternate;return l===null?(l=he(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Nr(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Du(t,e,l,n,a,u){var o=0;if(n=t,typeof t=="function")gc(t)&&(o=1);else if(typeof t=="string")o=by(t,l,P.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case it:return t=he(31,l,e,a),t.elementType=it,t.lanes=u,t;case R:return Kl(l.children,a,u,e);case z:o=8,a|=24;break;case H:return t=he(12,l,e,a|2),t.elementType=H,t.lanes=u,t;case K:return t=he(13,l,e,a),t.elementType=K,t.lanes=u,t;case et:return t=he(19,l,e,a),t.elementType=et,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case q:case J:o=10;break t;case lt:o=9;break t;case k:o=11;break t;case G:o=14;break t;case tt:o=16,n=null;break t}o=29,l=Error(s(130,t===null?"null":typeof t,"")),n=null}return e=he(o,l,e,a),e.elementType=t,e.type=n,e.lanes=u,e}function Kl(t,e,l,n){return t=he(7,t,n,e),t.lanes=l,t}function pc(t,e,l){return t=he(6,t,null,e),t.lanes=l,t}function Sc(t,e,l){return e=he(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Dn=[],zn=0,zu=null,Cu=0,Me=[],Ae=0,Jl=null,Fe=1,Ie="";function $l(t,e){Dn[zn++]=Cu,Dn[zn++]=zu,zu=t,Cu=e}function Lr(t,e,l){Me[Ae++]=Fe,Me[Ae++]=Ie,Me[Ae++]=Jl,Jl=t;var n=Fe;t=Ie;var a=32-fe(n)-1;n&=~(1<<a),l+=1;var u=32-fe(e)+a;if(30<u){var o=a-a%5;u=(n&(1<<o)-1).toString(32),n>>=o,a-=o,Fe=1<<32-fe(e)+a|l<<a|n,Ie=u+t}else Fe=1<<u|l<<a|n,Ie=t}function _c(t){t.return!==null&&($l(t,1),Lr(t,1,0))}function bc(t){for(;t===zu;)zu=Dn[--zn],Dn[zn]=null,Cu=Dn[--zn],Dn[zn]=null;for(;t===Jl;)Jl=Me[--Ae],Me[Ae]=null,Ie=Me[--Ae],Me[Ae]=null,Fe=Me[--Ae],Me[Ae]=null}var ae=null,jt=null,Tt=!1,kl=null,Ve=!1,Ec=Error(s(519));function Pl(t){var e=Error(s(418,""));throw Ra(Re(e,t)),Ec}function Br(t){var e=t.stateNode,l=t.type,n=t.memoizedProps;switch(e[ee]=t,e[ue]=n,l){case"dialog":gt("cancel",e),gt("close",e);break;case"iframe":case"object":case"embed":gt("load",e);break;case"video":case"audio":for(l=0;l<Ja.length;l++)gt(Ja[l],e);break;case"source":gt("error",e);break;case"img":case"image":case"link":gt("error",e),gt("load",e);break;case"details":gt("toggle",e);break;case"input":gt("invalid",e),Po(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),gu(e);break;case"select":gt("invalid",e);break;case"textarea":gt("invalid",e),Fo(e,n.value,n.defaultValue,n.children),gu(e)}l=n.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||n.suppressHydrationWarning===!0||Id(e.textContent,l)?(n.popover!=null&&(gt("beforetoggle",e),gt("toggle",e)),n.onScroll!=null&&gt("scroll",e),n.onScrollEnd!=null&&gt("scrollend",e),n.onClick!=null&&(e.onclick=fi),e=!0):e=!1,e||Pl(t)}function Hr(t){for(ae=t.return;ae;)switch(ae.tag){case 5:case 13:Ve=!1;return;case 27:case 3:Ve=!0;return;default:ae=ae.return}}function ba(t){if(t!==ae)return!1;if(!Tt)return Hr(t),Tt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||ws(t.type,t.memoizedProps)),l=!l),l&&jt&&Pl(t),Hr(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){jt=He(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}jt=null}}else e===27?(e=jt,Ul(t.type)?(t=Gs,Gs=null,jt=t):jt=e):jt=ae?He(t.stateNode.nextSibling):null;return!0}function Ea(){jt=ae=null,Tt=!1}function jr(){var t=kl;return t!==null&&(oe===null?oe=t:oe.push.apply(oe,t),kl=null),t}function Ra(t){kl===null?kl=[t]:kl.push(t)}var Rc=L(null),Wl=null,tl=null;function gl(t,e,l){V(Rc,e._currentValue),e._currentValue=l}function el(t){t._currentValue=Rc.current,X(Rc)}function Tc(t,e,l){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===l)break;t=t.return}}function Mc(t,e,l,n){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var u=a.dependencies;if(u!==null){var o=a.child;u=u.firstContext;t:for(;u!==null;){var d=u;u=a;for(var b=0;b<e.length;b++)if(d.context===e[b]){u.lanes|=l,d=u.alternate,d!==null&&(d.lanes|=l),Tc(u.return,l,t),n||(o=null);break t}u=d.next}}else if(a.tag===18){if(o=a.return,o===null)throw Error(s(341));o.lanes|=l,u=o.alternate,u!==null&&(u.lanes|=l),Tc(o,l,t),o=null}else o=a.child;if(o!==null)o.return=a;else for(o=a;o!==null;){if(o===t){o=null;break}if(a=o.sibling,a!==null){a.return=o.return,o=a;break}o=o.return}a=o}}function Ta(t,e,l,n){t=null;for(var a=e,u=!1;a!==null;){if(!u){if((a.flags&524288)!==0)u=!0;else if((a.flags&262144)!==0)break}if(a.tag===10){var o=a.alternate;if(o===null)throw Error(s(387));if(o=o.memoizedProps,o!==null){var d=a.type;de(a.pendingProps.value,o.value)||(t!==null?t.push(d):t=[d])}}else if(a===_t.current){if(o=a.alternate,o===null)throw Error(s(387));o.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push(Ia):t=[Ia])}a=a.return}t!==null&&Mc(e,t,l,n),e.flags|=262144}function Uu(t){for(t=t.firstContext;t!==null;){if(!de(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Fl(t){Wl=t,tl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function le(t){return wr(Wl,t)}function Nu(t,e){return Wl===null&&Fl(t),wr(t,e)}function wr(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},tl===null){if(t===null)throw Error(s(308));tl=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else tl=tl.next=e;return l}var _m=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},bm=i.unstable_scheduleCallback,Em=i.unstable_NormalPriority,Qt={$$typeof:J,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ac(){return{controller:new _m,data:new Map,refCount:0}}function Ma(t){t.refCount--,t.refCount===0&&bm(Em,function(){t.controller.abort()})}var Aa=null,Oc=0,Cn=0,Un=null;function Rm(t,e){if(Aa===null){var l=Aa=[];Oc=0,Cn=Ds(),Un={status:"pending",value:void 0,then:function(n){l.push(n)}}}return Oc++,e.then(qr,qr),e}function qr(){if(--Oc===0&&Aa!==null){Un!==null&&(Un.status="fulfilled");var t=Aa;Aa=null,Cn=0,Un=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Tm(t,e){var l=[],n={status:"pending",value:null,reason:null,then:function(a){l.push(a)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var a=0;a<l.length;a++)(0,l[a])(e)},function(a){for(n.status="rejected",n.reason=a,a=0;a<l.length;a++)(0,l[a])(void 0)}),n}var Yr=x.S;x.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Rm(t,e),Yr!==null&&Yr(t,e)};var Il=L(null);function xc(){var t=Il.current;return t!==null?t:Ct.pooledCache}function Lu(t,e){e===null?V(Il,Il.current):V(Il,e.pool)}function Vr(){var t=xc();return t===null?null:{parent:Qt._currentValue,pool:t}}var Oa=Error(s(460)),Gr=Error(s(474)),Bu=Error(s(542)),Dc={then:function(){}};function Xr(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Hu(){}function Qr(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(Hu,Hu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Kr(t),t;default:if(typeof e.status=="string")e.then(Hu,Hu);else{if(t=Ct,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=n}},function(n){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Kr(t),t}throw xa=e,Oa}}var xa=null;function Zr(){if(xa===null)throw Error(s(459));var t=xa;return xa=null,t}function Kr(t){if(t===Oa||t===Bu)throw Error(s(483))}var pl=!1;function zc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Cc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Sl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function _l(t,e,l){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,(Mt&2)!==0){var a=n.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),n.pending=e,e=xu(t),Ur(t,null,l),e}return Ou(t,n,e,l),xu(t)}function Da(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,qo(t,l)}}function Uc(t,e){var l=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,l===n)){var a=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var o={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?a=u=o:u=u.next=o,l=l.next}while(l!==null);u===null?a=u=e:u=u.next=e}else a=u=e;l={baseState:n.baseState,firstBaseUpdate:a,lastBaseUpdate:u,shared:n.shared,callbacks:n.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var Nc=!1;function za(){if(Nc){var t=Un;if(t!==null)throw t}}function Ca(t,e,l,n){Nc=!1;var a=t.updateQueue;pl=!1;var u=a.firstBaseUpdate,o=a.lastBaseUpdate,d=a.shared.pending;if(d!==null){a.shared.pending=null;var b=d,D=b.next;b.next=null,o===null?u=D:o.next=D,o=b;var N=t.alternate;N!==null&&(N=N.updateQueue,d=N.lastBaseUpdate,d!==o&&(d===null?N.firstBaseUpdate=D:d.next=D,N.lastBaseUpdate=b))}if(u!==null){var j=a.baseState;o=0,N=D=b=null,d=u;do{var C=d.lane&-536870913,U=C!==d.lane;if(U?(St&C)===C:(n&C)===C){C!==0&&C===Cn&&(Nc=!0),N!==null&&(N=N.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var rt=t,ct=d;C=e;var Dt=l;switch(ct.tag){case 1:if(rt=ct.payload,typeof rt=="function"){j=rt.call(Dt,j,C);break t}j=rt;break t;case 3:rt.flags=rt.flags&-65537|128;case 0:if(rt=ct.payload,C=typeof rt=="function"?rt.call(Dt,j,C):rt,C==null)break t;j=_({},j,C);break t;case 2:pl=!0}}C=d.callback,C!==null&&(t.flags|=64,U&&(t.flags|=8192),U=a.callbacks,U===null?a.callbacks=[C]:U.push(C))}else U={lane:C,tag:d.tag,payload:d.payload,callback:d.callback,next:null},N===null?(D=N=U,b=j):N=N.next=U,o|=C;if(d=d.next,d===null){if(d=a.shared.pending,d===null)break;U=d,d=U.next,U.next=null,a.lastBaseUpdate=U,a.shared.pending=null}}while(!0);N===null&&(b=j),a.baseState=b,a.firstBaseUpdate=D,a.lastBaseUpdate=N,u===null&&(a.shared.lanes=0),xl|=o,t.lanes=o,t.memoizedState=j}}function Jr(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function $r(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)Jr(l[t],e)}var Nn=L(null),ju=L(0);function kr(t,e){t=sl,V(ju,t),V(Nn,e),sl=t|e.baseLanes}function Lc(){V(ju,sl),V(Nn,Nn.current)}function Bc(){sl=ju.current,X(Nn),X(ju)}var bl=0,ht=null,Ot=null,Gt=null,wu=!1,Ln=!1,tn=!1,qu=0,Ua=0,Bn=null,Mm=0;function qt(){throw Error(s(321))}function Hc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!de(t[l],e[l]))return!1;return!0}function jc(t,e,l,n,a,u){return bl=u,ht=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,x.H=t===null||t.memoizedState===null?Nf:Lf,tn=!1,u=l(n,a),tn=!1,Ln&&(u=Wr(e,l,n,a)),Pr(t),u}function Pr(t){x.H=Zu;var e=Ot!==null&&Ot.next!==null;if(bl=0,Gt=Ot=ht=null,wu=!1,Ua=0,Bn=null,e)throw Error(s(300));t===null||Jt||(t=t.dependencies,t!==null&&Uu(t)&&(Jt=!0))}function Wr(t,e,l,n){ht=t;var a=0;do{if(Ln&&(Bn=null),Ua=0,Ln=!1,25<=a)throw Error(s(301));if(a+=1,Gt=Ot=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}x.H=Um,u=e(l,n)}while(Ln);return u}function Am(){var t=x.H,e=t.useState()[0];return e=typeof e.then=="function"?Na(e):e,t=t.useState()[0],(Ot!==null?Ot.memoizedState:null)!==t&&(ht.flags|=1024),e}function wc(){var t=qu!==0;return qu=0,t}function qc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function Yc(t){if(wu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}wu=!1}bl=0,Gt=Ot=ht=null,Ln=!1,Ua=qu=0,Bn=null}function ce(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Gt===null?ht.memoizedState=Gt=t:Gt=Gt.next=t,Gt}function Xt(){if(Ot===null){var t=ht.alternate;t=t!==null?t.memoizedState:null}else t=Ot.next;var e=Gt===null?ht.memoizedState:Gt.next;if(e!==null)Gt=e,Ot=t;else{if(t===null)throw ht.alternate===null?Error(s(467)):Error(s(310));Ot=t,t={memoizedState:Ot.memoizedState,baseState:Ot.baseState,baseQueue:Ot.baseQueue,queue:Ot.queue,next:null},Gt===null?ht.memoizedState=Gt=t:Gt=Gt.next=t}return Gt}function Vc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Na(t){var e=Ua;return Ua+=1,Bn===null&&(Bn=[]),t=Qr(Bn,t,e),e=ht,(Gt===null?e.memoizedState:Gt.next)===null&&(e=e.alternate,x.H=e===null||e.memoizedState===null?Nf:Lf),t}function Yu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Na(t);if(t.$$typeof===J)return le(t)}throw Error(s(438,String(t)))}function Gc(t){var e=null,l=ht.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var n=ht.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=Vc(),ht.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),n=0;n<t;n++)l[n]=Y;return e.index++,l}function ll(t,e){return typeof e=="function"?e(t):e}function Vu(t){var e=Xt();return Xc(e,Ot,t)}function Xc(t,e,l){var n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=l;var a=t.baseQueue,u=n.pending;if(u!==null){if(a!==null){var o=a.next;a.next=u.next,u.next=o}e.baseQueue=a=u,n.pending=null}if(u=t.baseState,a===null)t.memoizedState=u;else{e=a.next;var d=o=null,b=null,D=e,N=!1;do{var j=D.lane&-536870913;if(j!==D.lane?(St&j)===j:(bl&j)===j){var C=D.revertLane;if(C===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),j===Cn&&(N=!0);else if((bl&C)===C){D=D.next,C===Cn&&(N=!0);continue}else j={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},b===null?(d=b=j,o=u):b=b.next=j,ht.lanes|=C,xl|=C;j=D.action,tn&&l(u,j),u=D.hasEagerState?D.eagerState:l(u,j)}else C={lane:j,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},b===null?(d=b=C,o=u):b=b.next=C,ht.lanes|=j,xl|=j;D=D.next}while(D!==null&&D!==e);if(b===null?o=u:b.next=d,!de(u,t.memoizedState)&&(Jt=!0,N&&(l=Un,l!==null)))throw l;t.memoizedState=u,t.baseState=o,t.baseQueue=b,n.lastRenderedState=u}return a===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function Qc(t){var e=Xt(),l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=t;var n=l.dispatch,a=l.pending,u=e.memoizedState;if(a!==null){l.pending=null;var o=a=a.next;do u=t(u,o.action),o=o.next;while(o!==a);de(u,e.memoizedState)||(Jt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,n]}function Fr(t,e,l){var n=ht,a=Xt(),u=Tt;if(u){if(l===void 0)throw Error(s(407));l=l()}else l=e();var o=!de((Ot||a).memoizedState,l);o&&(a.memoizedState=l,Jt=!0),a=a.queue;var d=ef.bind(null,n,a,t);if(La(2048,8,d,[t]),a.getSnapshot!==e||o||Gt!==null&&Gt.memoizedState.tag&1){if(n.flags|=2048,Hn(9,Gu(),tf.bind(null,n,a,l,e),null),Ct===null)throw Error(s(349));u||(bl&124)!==0||Ir(n,e,l)}return l}function Ir(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=ht.updateQueue,e===null?(e=Vc(),ht.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function tf(t,e,l,n){e.value=l,e.getSnapshot=n,lf(e)&&nf(t)}function ef(t,e,l){return l(function(){lf(e)&&nf(t)})}function lf(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!de(t,l)}catch{return!0}}function nf(t){var e=On(t,2);e!==null&&pe(e,t,2)}function Zc(t){var e=ce();if(typeof t=="function"){var l=t;if(t=l(),tn){vl(!0);try{l()}finally{vl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ll,lastRenderedState:t},e}function af(t,e,l,n){return t.baseState=l,Xc(t,Ot,typeof n=="function"?n:ll)}function Om(t,e,l,n,a){if(Qu(t))throw Error(s(485));if(t=e.action,t!==null){var u={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){u.listeners.push(o)}};x.T!==null?l(!0):u.isTransition=!1,n(u),l=e.pending,l===null?(u.next=e.pending=u,uf(e,u)):(u.next=l.next,e.pending=l.next=u)}}function uf(t,e){var l=e.action,n=e.payload,a=t.state;if(e.isTransition){var u=x.T,o={};x.T=o;try{var d=l(a,n),b=x.S;b!==null&&b(o,d),cf(t,e,d)}catch(D){Kc(t,e,D)}finally{x.T=u}}else try{u=l(a,n),cf(t,e,u)}catch(D){Kc(t,e,D)}}function cf(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(n){sf(t,e,n)},function(n){return Kc(t,e,n)}):sf(t,e,l)}function sf(t,e,l){e.status="fulfilled",e.value=l,of(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,uf(t,l)))}function Kc(t,e,l){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=l,of(e),e=e.next;while(e!==n)}t.action=null}function of(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function rf(t,e){return e}function ff(t,e){if(Tt){var l=Ct.formState;if(l!==null){t:{var n=ht;if(Tt){if(jt){e:{for(var a=jt,u=Ve;a.nodeType!==8;){if(!u){a=null;break e}if(a=He(a.nextSibling),a===null){a=null;break e}}u=a.data,a=u==="F!"||u==="F"?a:null}if(a){jt=He(a.nextSibling),n=a.data==="F!";break t}}Pl(n)}n=!1}n&&(e=l[0])}}return l=ce(),l.memoizedState=l.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:rf,lastRenderedState:e},l.queue=n,l=zf.bind(null,ht,n),n.dispatch=l,n=Zc(!1),u=Wc.bind(null,ht,!1,n.queue),n=ce(),a={state:e,dispatch:null,action:t,pending:null},n.queue=a,l=Om.bind(null,ht,a,u,l),a.dispatch=l,n.memoizedState=t,[e,l,!1]}function df(t){var e=Xt();return hf(e,Ot,t)}function hf(t,e,l){if(e=Xc(t,e,rf)[0],t=Vu(ll)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=Na(e)}catch(o){throw o===Oa?Bu:o}else n=e;e=Xt();var a=e.queue,u=a.dispatch;return l!==e.memoizedState&&(ht.flags|=2048,Hn(9,Gu(),xm.bind(null,a,l),null)),[n,u,t]}function xm(t,e){t.action=e}function vf(t){var e=Xt(),l=Ot;if(l!==null)return hf(e,l,t);Xt(),e=e.memoizedState,l=Xt();var n=l.queue.dispatch;return l.memoizedState=t,[e,n,!1]}function Hn(t,e,l,n){return t={tag:t,create:l,deps:n,inst:e,next:null},e=ht.updateQueue,e===null&&(e=Vc(),ht.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(n=l.next,l.next=t,t.next=n,e.lastEffect=t),t}function Gu(){return{destroy:void 0,resource:void 0}}function mf(){return Xt().memoizedState}function Xu(t,e,l,n){var a=ce();n=n===void 0?null:n,ht.flags|=t,a.memoizedState=Hn(1|e,Gu(),l,n)}function La(t,e,l,n){var a=Xt();n=n===void 0?null:n;var u=a.memoizedState.inst;Ot!==null&&n!==null&&Hc(n,Ot.memoizedState.deps)?a.memoizedState=Hn(e,u,l,n):(ht.flags|=t,a.memoizedState=Hn(1|e,u,l,n))}function yf(t,e){Xu(8390656,8,t,e)}function gf(t,e){La(2048,8,t,e)}function pf(t,e){return La(4,2,t,e)}function Sf(t,e){return La(4,4,t,e)}function _f(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function bf(t,e,l){l=l!=null?l.concat([t]):null,La(4,4,_f.bind(null,e,t),l)}function Jc(){}function Ef(t,e){var l=Xt();e=e===void 0?null:e;var n=l.memoizedState;return e!==null&&Hc(e,n[1])?n[0]:(l.memoizedState=[t,e],t)}function Rf(t,e){var l=Xt();e=e===void 0?null:e;var n=l.memoizedState;if(e!==null&&Hc(e,n[1]))return n[0];if(n=t(),tn){vl(!0);try{t()}finally{vl(!1)}}return l.memoizedState=[n,e],n}function $c(t,e,l){return l===void 0||(bl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Ad(),ht.lanes|=t,xl|=t,l)}function Tf(t,e,l,n){return de(l,e)?l:Nn.current!==null?(t=$c(t,l,n),de(t,e)||(Jt=!0),t):(bl&42)===0?(Jt=!0,t.memoizedState=l):(t=Ad(),ht.lanes|=t,xl|=t,e)}function Mf(t,e,l,n,a){var u=w.p;w.p=u!==0&&8>u?u:8;var o=x.T,d={};x.T=d,Wc(t,!1,e,l);try{var b=a(),D=x.S;if(D!==null&&D(d,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var N=Tm(b,n);Ba(t,e,N,ge(t))}else Ba(t,e,n,ge(t))}catch(j){Ba(t,e,{then:function(){},status:"rejected",reason:j},ge())}finally{w.p=u,x.T=o}}function Dm(){}function kc(t,e,l,n){if(t.tag!==5)throw Error(s(476));var a=Af(t).queue;Mf(t,a,e,Z,l===null?Dm:function(){return Of(t),l(n)})}function Af(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:Z,baseState:Z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ll,lastRenderedState:Z},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ll,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Of(t){var e=Af(t).next.queue;Ba(t,e,{},ge())}function Pc(){return le(Ia)}function xf(){return Xt().memoizedState}function Df(){return Xt().memoizedState}function zm(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=ge();t=Sl(l);var n=_l(e,t,l);n!==null&&(pe(n,e,l),Da(n,e,l)),e={cache:Ac()},t.payload=e;return}e=e.return}}function Cm(t,e,l){var n=ge();l={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Qu(t)?Cf(e,l):(l=yc(t,e,l,n),l!==null&&(pe(l,t,n),Uf(l,e,n)))}function zf(t,e,l){var n=ge();Ba(t,e,l,n)}function Ba(t,e,l,n){var a={lane:n,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Qu(t))Cf(e,a);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var o=e.lastRenderedState,d=u(o,l);if(a.hasEagerState=!0,a.eagerState=d,de(d,o))return Ou(t,e,a,0),Ct===null&&Au(),!1}catch{}finally{}if(l=yc(t,e,a,n),l!==null)return pe(l,t,n),Uf(l,e,n),!0}return!1}function Wc(t,e,l,n){if(n={lane:2,revertLane:Ds(),action:n,hasEagerState:!1,eagerState:null,next:null},Qu(t)){if(e)throw Error(s(479))}else e=yc(t,l,n,2),e!==null&&pe(e,t,2)}function Qu(t){var e=t.alternate;return t===ht||e!==null&&e===ht}function Cf(t,e){Ln=wu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Uf(t,e,l){if((l&4194048)!==0){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,qo(t,l)}}var Zu={readContext:le,use:Yu,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},Nf={readContext:le,use:Yu,useCallback:function(t,e){return ce().memoizedState=[t,e===void 0?null:e],t},useContext:le,useEffect:yf,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Xu(4194308,4,_f.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Xu(4194308,4,t,e)},useInsertionEffect:function(t,e){Xu(4,2,t,e)},useMemo:function(t,e){var l=ce();e=e===void 0?null:e;var n=t();if(tn){vl(!0);try{t()}finally{vl(!1)}}return l.memoizedState=[n,e],n},useReducer:function(t,e,l){var n=ce();if(l!==void 0){var a=l(e);if(tn){vl(!0);try{l(e)}finally{vl(!1)}}}else a=e;return n.memoizedState=n.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},n.queue=t,t=t.dispatch=Cm.bind(null,ht,t),[n.memoizedState,t]},useRef:function(t){var e=ce();return t={current:t},e.memoizedState=t},useState:function(t){t=Zc(t);var e=t.queue,l=zf.bind(null,ht,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:Jc,useDeferredValue:function(t,e){var l=ce();return $c(l,t,e)},useTransition:function(){var t=Zc(!1);return t=Mf.bind(null,ht,t.queue,!0,!1),ce().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var n=ht,a=ce();if(Tt){if(l===void 0)throw Error(s(407));l=l()}else{if(l=e(),Ct===null)throw Error(s(349));(St&124)!==0||Ir(n,e,l)}a.memoizedState=l;var u={value:l,getSnapshot:e};return a.queue=u,yf(ef.bind(null,n,u,t),[t]),n.flags|=2048,Hn(9,Gu(),tf.bind(null,n,u,l,e),null),l},useId:function(){var t=ce(),e=Ct.identifierPrefix;if(Tt){var l=Ie,n=Fe;l=(n&~(1<<32-fe(n)-1)).toString(32)+l,e="«"+e+"R"+l,l=qu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=Mm++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Pc,useFormState:ff,useActionState:ff,useOptimistic:function(t){var e=ce();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=Wc.bind(null,ht,!0,l),l.dispatch=e,[t,e]},useMemoCache:Gc,useCacheRefresh:function(){return ce().memoizedState=zm.bind(null,ht)}},Lf={readContext:le,use:Yu,useCallback:Ef,useContext:le,useEffect:gf,useImperativeHandle:bf,useInsertionEffect:pf,useLayoutEffect:Sf,useMemo:Rf,useReducer:Vu,useRef:mf,useState:function(){return Vu(ll)},useDebugValue:Jc,useDeferredValue:function(t,e){var l=Xt();return Tf(l,Ot.memoizedState,t,e)},useTransition:function(){var t=Vu(ll)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:Na(t),e]},useSyncExternalStore:Fr,useId:xf,useHostTransitionStatus:Pc,useFormState:df,useActionState:df,useOptimistic:function(t,e){var l=Xt();return af(l,Ot,t,e)},useMemoCache:Gc,useCacheRefresh:Df},Um={readContext:le,use:Yu,useCallback:Ef,useContext:le,useEffect:gf,useImperativeHandle:bf,useInsertionEffect:pf,useLayoutEffect:Sf,useMemo:Rf,useReducer:Qc,useRef:mf,useState:function(){return Qc(ll)},useDebugValue:Jc,useDeferredValue:function(t,e){var l=Xt();return Ot===null?$c(l,t,e):Tf(l,Ot.memoizedState,t,e)},useTransition:function(){var t=Qc(ll)[0],e=Xt().memoizedState;return[typeof t=="boolean"?t:Na(t),e]},useSyncExternalStore:Fr,useId:xf,useHostTransitionStatus:Pc,useFormState:vf,useActionState:vf,useOptimistic:function(t,e){var l=Xt();return Ot!==null?af(l,Ot,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:Gc,useCacheRefresh:Df},jn=null,Ha=0;function Ku(t){var e=Ha;return Ha+=1,jn===null&&(jn=[]),Qr(jn,t,e)}function ja(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ju(t,e){throw e.$$typeof===p?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Bf(t){var e=t._init;return e(t._payload)}function Hf(t){function e(M,T){if(t){var O=M.deletions;O===null?(M.deletions=[T],M.flags|=16):O.push(T)}}function l(M,T){if(!t)return null;for(;T!==null;)e(M,T),T=T.sibling;return null}function n(M){for(var T=new Map;M!==null;)M.key!==null?T.set(M.key,M):T.set(M.index,M),M=M.sibling;return T}function a(M,T){return M=We(M,T),M.index=0,M.sibling=null,M}function u(M,T,O){return M.index=O,t?(O=M.alternate,O!==null?(O=O.index,O<T?(M.flags|=67108866,T):O):(M.flags|=67108866,T)):(M.flags|=1048576,T)}function o(M){return t&&M.alternate===null&&(M.flags|=67108866),M}function d(M,T,O,B){return T===null||T.tag!==6?(T=pc(O,M.mode,B),T.return=M,T):(T=a(T,O),T.return=M,T)}function b(M,T,O,B){var I=O.type;return I===R?N(M,T,O.props.children,B,O.key):T!==null&&(T.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===tt&&Bf(I)===T.type)?(T=a(T,O.props),ja(T,O),T.return=M,T):(T=Du(O.type,O.key,O.props,null,M.mode,B),ja(T,O),T.return=M,T)}function D(M,T,O,B){return T===null||T.tag!==4||T.stateNode.containerInfo!==O.containerInfo||T.stateNode.implementation!==O.implementation?(T=Sc(O,M.mode,B),T.return=M,T):(T=a(T,O.children||[]),T.return=M,T)}function N(M,T,O,B,I){return T===null||T.tag!==7?(T=Kl(O,M.mode,B,I),T.return=M,T):(T=a(T,O),T.return=M,T)}function j(M,T,O){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return T=pc(""+T,M.mode,O),T.return=M,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case S:return O=Du(T.type,T.key,T.props,null,M.mode,O),ja(O,T),O.return=M,O;case A:return T=Sc(T,M.mode,O),T.return=M,T;case tt:var B=T._init;return T=B(T._payload),j(M,T,O)}if(pt(T)||F(T))return T=Kl(T,M.mode,O,null),T.return=M,T;if(typeof T.then=="function")return j(M,Ku(T),O);if(T.$$typeof===J)return j(M,Nu(M,T),O);Ju(M,T)}return null}function C(M,T,O,B){var I=T!==null?T.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return I!==null?null:d(M,T,""+O,B);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case S:return O.key===I?b(M,T,O,B):null;case A:return O.key===I?D(M,T,O,B):null;case tt:return I=O._init,O=I(O._payload),C(M,T,O,B)}if(pt(O)||F(O))return I!==null?null:N(M,T,O,B,null);if(typeof O.then=="function")return C(M,T,Ku(O),B);if(O.$$typeof===J)return C(M,T,Nu(M,O),B);Ju(M,O)}return null}function U(M,T,O,B,I){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return M=M.get(O)||null,d(T,M,""+B,I);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case S:return M=M.get(B.key===null?O:B.key)||null,b(T,M,B,I);case A:return M=M.get(B.key===null?O:B.key)||null,D(T,M,B,I);case tt:var vt=B._init;return B=vt(B._payload),U(M,T,O,B,I)}if(pt(B)||F(B))return M=M.get(O)||null,N(T,M,B,I,null);if(typeof B.then=="function")return U(M,T,O,Ku(B),I);if(B.$$typeof===J)return U(M,T,O,Nu(T,B),I);Ju(T,B)}return null}function rt(M,T,O,B){for(var I=null,vt=null,at=T,st=T=0,kt=null;at!==null&&st<O.length;st++){at.index>st?(kt=at,at=null):kt=at.sibling;var Et=C(M,at,O[st],B);if(Et===null){at===null&&(at=kt);break}t&&at&&Et.alternate===null&&e(M,at),T=u(Et,T,st),vt===null?I=Et:vt.sibling=Et,vt=Et,at=kt}if(st===O.length)return l(M,at),Tt&&$l(M,st),I;if(at===null){for(;st<O.length;st++)at=j(M,O[st],B),at!==null&&(T=u(at,T,st),vt===null?I=at:vt.sibling=at,vt=at);return Tt&&$l(M,st),I}for(at=n(at);st<O.length;st++)kt=U(at,M,st,O[st],B),kt!==null&&(t&&kt.alternate!==null&&at.delete(kt.key===null?st:kt.key),T=u(kt,T,st),vt===null?I=kt:vt.sibling=kt,vt=kt);return t&&at.forEach(function(jl){return e(M,jl)}),Tt&&$l(M,st),I}function ct(M,T,O,B){if(O==null)throw Error(s(151));for(var I=null,vt=null,at=T,st=T=0,kt=null,Et=O.next();at!==null&&!Et.done;st++,Et=O.next()){at.index>st?(kt=at,at=null):kt=at.sibling;var jl=C(M,at,Et.value,B);if(jl===null){at===null&&(at=kt);break}t&&at&&jl.alternate===null&&e(M,at),T=u(jl,T,st),vt===null?I=jl:vt.sibling=jl,vt=jl,at=kt}if(Et.done)return l(M,at),Tt&&$l(M,st),I;if(at===null){for(;!Et.done;st++,Et=O.next())Et=j(M,Et.value,B),Et!==null&&(T=u(Et,T,st),vt===null?I=Et:vt.sibling=Et,vt=Et);return Tt&&$l(M,st),I}for(at=n(at);!Et.done;st++,Et=O.next())Et=U(at,M,st,Et.value,B),Et!==null&&(t&&Et.alternate!==null&&at.delete(Et.key===null?st:Et.key),T=u(Et,T,st),vt===null?I=Et:vt.sibling=Et,vt=Et);return t&&at.forEach(function(Ny){return e(M,Ny)}),Tt&&$l(M,st),I}function Dt(M,T,O,B){if(typeof O=="object"&&O!==null&&O.type===R&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case S:t:{for(var I=O.key;T!==null;){if(T.key===I){if(I=O.type,I===R){if(T.tag===7){l(M,T.sibling),B=a(T,O.props.children),B.return=M,M=B;break t}}else if(T.elementType===I||typeof I=="object"&&I!==null&&I.$$typeof===tt&&Bf(I)===T.type){l(M,T.sibling),B=a(T,O.props),ja(B,O),B.return=M,M=B;break t}l(M,T);break}else e(M,T);T=T.sibling}O.type===R?(B=Kl(O.props.children,M.mode,B,O.key),B.return=M,M=B):(B=Du(O.type,O.key,O.props,null,M.mode,B),ja(B,O),B.return=M,M=B)}return o(M);case A:t:{for(I=O.key;T!==null;){if(T.key===I)if(T.tag===4&&T.stateNode.containerInfo===O.containerInfo&&T.stateNode.implementation===O.implementation){l(M,T.sibling),B=a(T,O.children||[]),B.return=M,M=B;break t}else{l(M,T);break}else e(M,T);T=T.sibling}B=Sc(O,M.mode,B),B.return=M,M=B}return o(M);case tt:return I=O._init,O=I(O._payload),Dt(M,T,O,B)}if(pt(O))return rt(M,T,O,B);if(F(O)){if(I=F(O),typeof I!="function")throw Error(s(150));return O=I.call(O),ct(M,T,O,B)}if(typeof O.then=="function")return Dt(M,T,Ku(O),B);if(O.$$typeof===J)return Dt(M,T,Nu(M,O),B);Ju(M,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,T!==null&&T.tag===6?(l(M,T.sibling),B=a(T,O),B.return=M,M=B):(l(M,T),B=pc(O,M.mode,B),B.return=M,M=B),o(M)):l(M,T)}return function(M,T,O,B){try{Ha=0;var I=Dt(M,T,O,B);return jn=null,I}catch(at){if(at===Oa||at===Bu)throw at;var vt=he(29,at,null,M.mode);return vt.lanes=B,vt.return=M,vt}finally{}}}var wn=Hf(!0),jf=Hf(!1),Oe=L(null),Ge=null;function El(t){var e=t.alternate;V(Zt,Zt.current&1),V(Oe,t),Ge===null&&(e===null||Nn.current!==null||e.memoizedState!==null)&&(Ge=t)}function wf(t){if(t.tag===22){if(V(Zt,Zt.current),V(Oe,t),Ge===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ge=t)}}else Rl()}function Rl(){V(Zt,Zt.current),V(Oe,Oe.current)}function nl(t){X(Oe),Ge===t&&(Ge=null),X(Zt)}var Zt=L(0);function $u(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Vs(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Fc(t,e,l,n){e=t.memoizedState,l=l(n,e),l=l==null?e:_({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Ic={enqueueSetState:function(t,e,l){t=t._reactInternals;var n=ge(),a=Sl(n);a.payload=e,l!=null&&(a.callback=l),e=_l(t,a,n),e!==null&&(pe(e,t,n),Da(e,t,n))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var n=ge(),a=Sl(n);a.tag=1,a.payload=e,l!=null&&(a.callback=l),e=_l(t,a,n),e!==null&&(pe(e,t,n),Da(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=ge(),n=Sl(l);n.tag=2,e!=null&&(n.callback=e),e=_l(t,n,l),e!==null&&(pe(e,t,l),Da(e,t,l))}};function qf(t,e,l,n,a,u,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,u,o):e.prototype&&e.prototype.isPureReactComponent?!Sa(l,n)||!Sa(a,u):!0}function Yf(t,e,l,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,n),e.state!==t&&Ic.enqueueReplaceState(e,e.state,null)}function en(t,e){var l=e;if("ref"in e){l={};for(var n in e)n!=="ref"&&(l[n]=e[n])}if(t=t.defaultProps){l===e&&(l=_({},l));for(var a in t)l[a]===void 0&&(l[a]=t[a])}return l}var ku=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Vf(t){ku(t)}function Gf(t){console.error(t)}function Xf(t){ku(t)}function Pu(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function Qf(t,e,l){try{var n=t.onCaughtError;n(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function ts(t,e,l){return l=Sl(l),l.tag=3,l.payload={element:null},l.callback=function(){Pu(t,e)},l}function Zf(t){return t=Sl(t),t.tag=3,t}function Kf(t,e,l,n){var a=l.type.getDerivedStateFromError;if(typeof a=="function"){var u=n.value;t.payload=function(){return a(u)},t.callback=function(){Qf(e,l,n)}}var o=l.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){Qf(e,l,n),typeof a!="function"&&(Dl===null?Dl=new Set([this]):Dl.add(this));var d=n.stack;this.componentDidCatch(n.value,{componentStack:d!==null?d:""})})}function Nm(t,e,l,n,a){if(l.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=l.alternate,e!==null&&Ta(e,l,a,!0),l=Oe.current,l!==null){switch(l.tag){case 13:return Ge===null?Ts():l.alternate===null&&wt===0&&(wt=3),l.flags&=-257,l.flags|=65536,l.lanes=a,n===Dc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([n]):e.add(n),As(t,n,a)),!1;case 22:return l.flags|=65536,n===Dc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([n]):l.add(n)),As(t,n,a)),!1}throw Error(s(435,l.tag))}return As(t,n,a),Ts(),!1}if(Tt)return e=Oe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=a,n!==Ec&&(t=Error(s(422),{cause:n}),Ra(Re(t,l)))):(n!==Ec&&(e=Error(s(423),{cause:n}),Ra(Re(e,l))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,n=Re(n,l),a=ts(t.stateNode,n,a),Uc(t,a),wt!==4&&(wt=2)),!1;var u=Error(s(520),{cause:n});if(u=Re(u,l),Qa===null?Qa=[u]:Qa.push(u),wt!==4&&(wt=2),e===null)return!0;n=Re(n,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=a&-a,l.lanes|=t,t=ts(l.stateNode,n,t),Uc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Dl===null||!Dl.has(u))))return l.flags|=65536,a&=-a,l.lanes|=a,a=Zf(a),Kf(a,t,l,n),Uc(l,a),!1}l=l.return}while(l!==null);return!1}var Jf=Error(s(461)),Jt=!1;function Ft(t,e,l,n){e.child=t===null?jf(e,null,l,n):wn(e,t.child,l,n)}function $f(t,e,l,n,a){l=l.render;var u=e.ref;if("ref"in n){var o={};for(var d in n)d!=="ref"&&(o[d]=n[d])}else o=n;return Fl(e),n=jc(t,e,l,o,u,a),d=wc(),t!==null&&!Jt?(qc(t,e,a),al(t,e,a)):(Tt&&d&&_c(e),e.flags|=1,Ft(t,e,n,a),e.child)}function kf(t,e,l,n,a){if(t===null){var u=l.type;return typeof u=="function"&&!gc(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,Pf(t,e,u,n,a)):(t=Du(l.type,null,n,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!ss(t,a)){var o=u.memoizedProps;if(l=l.compare,l=l!==null?l:Sa,l(o,n)&&t.ref===e.ref)return al(t,e,a)}return e.flags|=1,t=We(u,n),t.ref=e.ref,t.return=e,e.child=t}function Pf(t,e,l,n,a){if(t!==null){var u=t.memoizedProps;if(Sa(u,n)&&t.ref===e.ref)if(Jt=!1,e.pendingProps=n=u,ss(t,a))(t.flags&131072)!==0&&(Jt=!0);else return e.lanes=t.lanes,al(t,e,a)}return es(t,e,l,n,a)}function Wf(t,e,l){var n=e.pendingProps,a=n.children,u=t!==null?t.memoizedState:null;if(n.mode==="hidden"){if((e.flags&128)!==0){if(n=u!==null?u.baseLanes|l:l,t!==null){for(a=e.child=t.child,u=0;a!==null;)u=u|a.lanes|a.childLanes,a=a.sibling;e.childLanes=u&~n}else e.childLanes=0,e.child=null;return Ff(t,e,n,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Lu(e,u!==null?u.cachePool:null),u!==null?kr(e,u):Lc(),wf(e);else return e.lanes=e.childLanes=536870912,Ff(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(Lu(e,u.cachePool),kr(e,u),Rl(),e.memoizedState=null):(t!==null&&Lu(e,null),Lc(),Rl());return Ft(t,e,a,l),e.child}function Ff(t,e,l,n){var a=xc();return a=a===null?null:{parent:Qt._currentValue,pool:a},e.memoizedState={baseLanes:l,cachePool:a},t!==null&&Lu(e,null),Lc(),wf(e),t!==null&&Ta(t,e,n,!0),null}function Wu(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function es(t,e,l,n,a){return Fl(e),l=jc(t,e,l,n,void 0,a),n=wc(),t!==null&&!Jt?(qc(t,e,a),al(t,e,a)):(Tt&&n&&_c(e),e.flags|=1,Ft(t,e,l,a),e.child)}function If(t,e,l,n,a,u){return Fl(e),e.updateQueue=null,l=Wr(e,n,l,a),Pr(t),n=wc(),t!==null&&!Jt?(qc(t,e,u),al(t,e,u)):(Tt&&n&&_c(e),e.flags|=1,Ft(t,e,l,u),e.child)}function td(t,e,l,n,a){if(Fl(e),e.stateNode===null){var u=xn,o=l.contextType;typeof o=="object"&&o!==null&&(u=le(o)),u=new l(n,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=Ic,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=n,u.state=e.memoizedState,u.refs={},zc(e),o=l.contextType,u.context=typeof o=="object"&&o!==null?le(o):xn,u.state=e.memoizedState,o=l.getDerivedStateFromProps,typeof o=="function"&&(Fc(e,l,o,n),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(o=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),o!==u.state&&Ic.enqueueReplaceState(u,u.state,null),Ca(e,n,u,a),za(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){u=e.stateNode;var d=e.memoizedProps,b=en(l,d);u.props=b;var D=u.context,N=l.contextType;o=xn,typeof N=="object"&&N!==null&&(o=le(N));var j=l.getDerivedStateFromProps;N=typeof j=="function"||typeof u.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,N||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(d||D!==o)&&Yf(e,u,n,o),pl=!1;var C=e.memoizedState;u.state=C,Ca(e,n,u,a),za(),D=e.memoizedState,d||C!==D||pl?(typeof j=="function"&&(Fc(e,l,j,n),D=e.memoizedState),(b=pl||qf(e,l,b,n,C,D,o))?(N||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=D),u.props=n,u.state=D,u.context=o,n=b):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{u=e.stateNode,Cc(t,e),o=e.memoizedProps,N=en(l,o),u.props=N,j=e.pendingProps,C=u.context,D=l.contextType,b=xn,typeof D=="object"&&D!==null&&(b=le(D)),d=l.getDerivedStateFromProps,(D=typeof d=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o!==j||C!==b)&&Yf(e,u,n,b),pl=!1,C=e.memoizedState,u.state=C,Ca(e,n,u,a),za();var U=e.memoizedState;o!==j||C!==U||pl||t!==null&&t.dependencies!==null&&Uu(t.dependencies)?(typeof d=="function"&&(Fc(e,l,d,n),U=e.memoizedState),(N=pl||qf(e,l,N,n,C,U,b)||t!==null&&t.dependencies!==null&&Uu(t.dependencies))?(D||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(n,U,b),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(n,U,b)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=U),u.props=n,u.state=U,u.context=b,n=N):(typeof u.componentDidUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),n=!1)}return u=n,Wu(t,e),n=(e.flags&128)!==0,u||n?(u=e.stateNode,l=n&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&n?(e.child=wn(e,t.child,null,a),e.child=wn(e,null,l,a)):Ft(t,e,l,a),e.memoizedState=u.state,t=e.child):t=al(t,e,a),t}function ed(t,e,l,n){return Ea(),e.flags|=256,Ft(t,e,l,n),e.child}var ls={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ns(t){return{baseLanes:t,cachePool:Vr()}}function as(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=xe),t}function ld(t,e,l){var n=e.pendingProps,a=!1,u=(e.flags&128)!==0,o;if((o=u)||(o=t!==null&&t.memoizedState===null?!1:(Zt.current&2)!==0),o&&(a=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(Tt){if(a?El(e):Rl(),Tt){var d=jt,b;if(b=d){t:{for(b=d,d=Ve;b.nodeType!==8;){if(!d){d=null;break t}if(b=He(b.nextSibling),b===null){d=null;break t}}d=b}d!==null?(e.memoizedState={dehydrated:d,treeContext:Jl!==null?{id:Fe,overflow:Ie}:null,retryLane:536870912,hydrationErrors:null},b=he(18,null,null,0),b.stateNode=d,b.return=e,e.child=b,ae=e,jt=null,b=!0):b=!1}b||Pl(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return Vs(d)?e.lanes=32:e.lanes=536870912,null;nl(e)}return d=n.children,n=n.fallback,a?(Rl(),a=e.mode,d=Fu({mode:"hidden",children:d},a),n=Kl(n,a,l,null),d.return=e,n.return=e,d.sibling=n,e.child=d,a=e.child,a.memoizedState=ns(l),a.childLanes=as(t,o,l),e.memoizedState=ls,n):(El(e),us(e,d))}if(b=t.memoizedState,b!==null&&(d=b.dehydrated,d!==null)){if(u)e.flags&256?(El(e),e.flags&=-257,e=is(t,e,l)):e.memoizedState!==null?(Rl(),e.child=t.child,e.flags|=128,e=null):(Rl(),a=n.fallback,d=e.mode,n=Fu({mode:"visible",children:n.children},d),a=Kl(a,d,l,null),a.flags|=2,n.return=e,a.return=e,n.sibling=a,e.child=n,wn(e,t.child,null,l),n=e.child,n.memoizedState=ns(l),n.childLanes=as(t,o,l),e.memoizedState=ls,e=a);else if(El(e),Vs(d)){if(o=d.nextSibling&&d.nextSibling.dataset,o)var D=o.dgst;o=D,n=Error(s(419)),n.stack="",n.digest=o,Ra({value:n,source:null,stack:null}),e=is(t,e,l)}else if(Jt||Ta(t,e,l,!1),o=(l&t.childLanes)!==0,Jt||o){if(o=Ct,o!==null&&(n=l&-l,n=(n&42)!==0?1:Gi(n),n=(n&(o.suspendedLanes|l))!==0?0:n,n!==0&&n!==b.retryLane))throw b.retryLane=n,On(t,n),pe(o,t,n),Jf;d.data==="$?"||Ts(),e=is(t,e,l)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=b.treeContext,jt=He(d.nextSibling),ae=e,Tt=!0,kl=null,Ve=!1,t!==null&&(Me[Ae++]=Fe,Me[Ae++]=Ie,Me[Ae++]=Jl,Fe=t.id,Ie=t.overflow,Jl=e),e=us(e,n.children),e.flags|=4096);return e}return a?(Rl(),a=n.fallback,d=e.mode,b=t.child,D=b.sibling,n=We(b,{mode:"hidden",children:n.children}),n.subtreeFlags=b.subtreeFlags&65011712,D!==null?a=We(D,a):(a=Kl(a,d,l,null),a.flags|=2),a.return=e,n.return=e,n.sibling=a,e.child=n,n=a,a=e.child,d=t.child.memoizedState,d===null?d=ns(l):(b=d.cachePool,b!==null?(D=Qt._currentValue,b=b.parent!==D?{parent:D,pool:D}:b):b=Vr(),d={baseLanes:d.baseLanes|l,cachePool:b}),a.memoizedState=d,a.childLanes=as(t,o,l),e.memoizedState=ls,n):(El(e),l=t.child,t=l.sibling,l=We(l,{mode:"visible",children:n.children}),l.return=e,l.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=l,e.memoizedState=null,l)}function us(t,e){return e=Fu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Fu(t,e){return t=he(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function is(t,e,l){return wn(e,t.child,null,l),t=us(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function nd(t,e,l){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),Tc(t.return,e,l)}function cs(t,e,l,n,a){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:l,tailMode:a}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=n,u.tail=l,u.tailMode=a)}function ad(t,e,l){var n=e.pendingProps,a=n.revealOrder,u=n.tail;if(Ft(t,e,n.children,l),n=Zt.current,(n&2)!==0)n=n&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&nd(t,l,e);else if(t.tag===19)nd(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}n&=1}switch(V(Zt,n),a){case"forwards":for(l=e.child,a=null;l!==null;)t=l.alternate,t!==null&&$u(t)===null&&(a=l),l=l.sibling;l=a,l===null?(a=e.child,e.child=null):(a=l.sibling,l.sibling=null),cs(e,!1,a,l,u);break;case"backwards":for(l=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&$u(t)===null){e.child=a;break}t=a.sibling,a.sibling=l,l=a,a=t}cs(e,!0,l,null,u);break;case"together":cs(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function al(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),xl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(Ta(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,l=We(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=We(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function ss(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Uu(t)))}function Lm(t,e,l){switch(e.tag){case 3:ft(e,e.stateNode.containerInfo),gl(e,Qt,t.memoizedState.cache),Ea();break;case 27:case 5:Lt(e);break;case 4:ft(e,e.stateNode.containerInfo);break;case 10:gl(e,e.type,e.memoizedProps.value);break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(El(e),e.flags|=128,null):(l&e.child.childLanes)!==0?ld(t,e,l):(El(e),t=al(t,e,l),t!==null?t.sibling:null);El(e);break;case 19:var a=(t.flags&128)!==0;if(n=(l&e.childLanes)!==0,n||(Ta(t,e,l,!1),n=(l&e.childLanes)!==0),a){if(n)return ad(t,e,l);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),V(Zt,Zt.current),n)break;return null;case 22:case 23:return e.lanes=0,Wf(t,e,l);case 24:gl(e,Qt,t.memoizedState.cache)}return al(t,e,l)}function ud(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Jt=!0;else{if(!ss(t,l)&&(e.flags&128)===0)return Jt=!1,Lm(t,e,l);Jt=(t.flags&131072)!==0}else Jt=!1,Tt&&(e.flags&1048576)!==0&&Lr(e,Cu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var n=e.elementType,a=n._init;if(n=a(n._payload),e.type=n,typeof n=="function")gc(n)?(t=en(n,t),e.tag=1,e=td(null,e,n,t,l)):(e.tag=0,e=es(null,e,n,t,l));else{if(n!=null){if(a=n.$$typeof,a===k){e.tag=11,e=$f(null,e,n,t,l);break t}else if(a===G){e.tag=14,e=kf(null,e,n,t,l);break t}}throw e=bt(n)||n,Error(s(306,e,""))}}return e;case 0:return es(t,e,e.type,e.pendingProps,l);case 1:return n=e.type,a=en(n,e.pendingProps),td(t,e,n,a,l);case 3:t:{if(ft(e,e.stateNode.containerInfo),t===null)throw Error(s(387));n=e.pendingProps;var u=e.memoizedState;a=u.element,Cc(t,e),Ca(e,n,null,l);var o=e.memoizedState;if(n=o.cache,gl(e,Qt,n),n!==u.cache&&Mc(e,[Qt],l,!0),za(),n=o.element,u.isDehydrated)if(u={element:n,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=ed(t,e,n,l);break t}else if(n!==a){a=Re(Error(s(424)),e),Ra(a),e=ed(t,e,n,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(jt=He(t.firstChild),ae=e,Tt=!0,kl=null,Ve=!0,l=jf(e,null,n,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Ea(),n===a){e=al(t,e,l);break t}Ft(t,e,n,l)}e=e.child}return e;case 26:return Wu(t,e),t===null?(l=oh(e.type,null,e.pendingProps,null))?e.memoizedState=l:Tt||(l=e.type,t=e.pendingProps,n=di($.current).createElement(l),n[ee]=e,n[ue]=t,te(n,l,t),Kt(n),e.stateNode=n):e.memoizedState=oh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Lt(e),t===null&&Tt&&(n=e.stateNode=ih(e.type,e.pendingProps,$.current),ae=e,Ve=!0,a=jt,Ul(e.type)?(Gs=a,jt=He(n.firstChild)):jt=a),Ft(t,e,e.pendingProps.children,l),Wu(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Tt&&((a=n=jt)&&(n=sy(n,e.type,e.pendingProps,Ve),n!==null?(e.stateNode=n,ae=e,jt=He(n.firstChild),Ve=!1,a=!0):a=!1),a||Pl(e)),Lt(e),a=e.type,u=e.pendingProps,o=t!==null?t.memoizedProps:null,n=u.children,ws(a,u)?n=null:o!==null&&ws(a,o)&&(e.flags|=32),e.memoizedState!==null&&(a=jc(t,e,Am,null,null,l),Ia._currentValue=a),Wu(t,e),Ft(t,e,n,l),e.child;case 6:return t===null&&Tt&&((t=l=jt)&&(l=oy(l,e.pendingProps,Ve),l!==null?(e.stateNode=l,ae=e,jt=null,t=!0):t=!1),t||Pl(e)),null;case 13:return ld(t,e,l);case 4:return ft(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=wn(e,null,n,l):Ft(t,e,n,l),e.child;case 11:return $f(t,e,e.type,e.pendingProps,l);case 7:return Ft(t,e,e.pendingProps,l),e.child;case 8:return Ft(t,e,e.pendingProps.children,l),e.child;case 12:return Ft(t,e,e.pendingProps.children,l),e.child;case 10:return n=e.pendingProps,gl(e,e.type,n.value),Ft(t,e,n.children,l),e.child;case 9:return a=e.type._context,n=e.pendingProps.children,Fl(e),a=le(a),n=n(a),e.flags|=1,Ft(t,e,n,l),e.child;case 14:return kf(t,e,e.type,e.pendingProps,l);case 15:return Pf(t,e,e.type,e.pendingProps,l);case 19:return ad(t,e,l);case 31:return n=e.pendingProps,l=e.mode,n={mode:n.mode,children:n.children},t===null?(l=Fu(n,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=We(t.child,n),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return Wf(t,e,l);case 24:return Fl(e),n=le(Qt),t===null?(a=xc(),a===null&&(a=Ct,u=Ac(),a.pooledCache=u,u.refCount++,u!==null&&(a.pooledCacheLanes|=l),a=u),e.memoizedState={parent:n,cache:a},zc(e),gl(e,Qt,a)):((t.lanes&l)!==0&&(Cc(t,e),Ca(e,null,null,l),za()),a=t.memoizedState,u=e.memoizedState,a.parent!==n?(a={parent:n,cache:n},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),gl(e,Qt,n)):(n=u.cache,gl(e,Qt,n),n!==a.cache&&Mc(e,[Qt],l,!0))),Ft(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function ul(t){t.flags|=4}function id(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!vh(e)){if(e=Oe.current,e!==null&&((St&4194048)===St?Ge!==null:(St&62914560)!==St&&(St&536870912)===0||e!==Ge))throw xa=Dc,Gr;t.flags|=8192}}function Iu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?jo():536870912,t.lanes|=e,Gn|=e)}function wa(t,e){if(!Tt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var n=null;l!==null;)l.alternate!==null&&(n=l),l=l.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function Bt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,n=0;if(e)for(var a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags&65011712,n|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags,n|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=n,t.childLanes=l,e}function Bm(t,e,l){var n=e.pendingProps;switch(bc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Bt(e),null;case 1:return Bt(e),null;case 3:return l=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),el(Qt),Rt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(ba(e)?ul(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,jr())),Bt(e),null;case 26:return l=e.memoizedState,t===null?(ul(e),l!==null?(Bt(e),id(e,l)):(Bt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(ul(e),Bt(e),id(e,l)):(Bt(e),e.flags&=-16777217):(t.memoizedProps!==n&&ul(e),Bt(e),e.flags&=-16777217),null;case 27:Ht(e),l=$.current;var a=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==n&&ul(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Bt(e),null}t=P.current,ba(e)?Br(e):(t=ih(a,n,l),e.stateNode=t,ul(e))}return Bt(e),null;case 5:if(Ht(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&ul(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Bt(e),null}if(t=P.current,ba(e))Br(e);else{switch(a=di($.current),t){case 1:t=a.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=a.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=a.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=a.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof n.is=="string"?a.createElement("select",{is:n.is}):a.createElement("select"),n.multiple?t.multiple=!0:n.size&&(t.size=n.size);break;default:t=typeof n.is=="string"?a.createElement(l,{is:n.is}):a.createElement(l)}}t[ee]=e,t[ue]=n;t:for(a=e.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;a=a.return}a.sibling.return=a.return,a=a.sibling}e.stateNode=t;t:switch(te(t,l,n),l){case"button":case"input":case"select":case"textarea":t=!!n.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&ul(e)}}return Bt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&ul(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(s(166));if(t=$.current,ba(e)){if(t=e.stateNode,l=e.memoizedProps,n=null,a=ae,a!==null)switch(a.tag){case 27:case 5:n=a.memoizedProps}t[ee]=e,t=!!(t.nodeValue===l||n!==null&&n.suppressHydrationWarning===!0||Id(t.nodeValue,l)),t||Pl(e)}else t=di(t).createTextNode(n),t[ee]=e,e.stateNode=t}return Bt(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=ba(e),n!==null&&n.dehydrated!==null){if(t===null){if(!a)throw Error(s(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(s(317));a[ee]=e}else Ea(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Bt(e),a=!1}else a=jr(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(nl(e),e):(nl(e),null)}if(nl(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=n!==null,t=t!==null&&t.memoizedState!==null,l){n=e.child,a=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(a=n.alternate.memoizedState.cachePool.pool);var u=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(u=n.memoizedState.cachePool.pool),u!==a&&(n.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Iu(e,e.updateQueue),Bt(e),null;case 4:return Rt(),t===null&&Ns(e.stateNode.containerInfo),Bt(e),null;case 10:return el(e.type),Bt(e),null;case 19:if(X(Zt),a=e.memoizedState,a===null)return Bt(e),null;if(n=(e.flags&128)!==0,u=a.rendering,u===null)if(n)wa(a,!1);else{if(wt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=$u(t),u!==null){for(e.flags|=128,wa(a,!1),t=u.updateQueue,e.updateQueue=t,Iu(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Nr(l,t),l=l.sibling;return V(Zt,Zt.current&1|2),e.child}t=t.sibling}a.tail!==null&&_e()>li&&(e.flags|=128,n=!0,wa(a,!1),e.lanes=4194304)}else{if(!n)if(t=$u(u),t!==null){if(e.flags|=128,n=!0,t=t.updateQueue,e.updateQueue=t,Iu(e,t),wa(a,!0),a.tail===null&&a.tailMode==="hidden"&&!u.alternate&&!Tt)return Bt(e),null}else 2*_e()-a.renderingStartTime>li&&l!==536870912&&(e.flags|=128,n=!0,wa(a,!1),e.lanes=4194304);a.isBackwards?(u.sibling=e.child,e.child=u):(t=a.last,t!==null?t.sibling=u:e.child=u,a.last=u)}return a.tail!==null?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=_e(),e.sibling=null,t=Zt.current,V(Zt,n?t&1|2:t&1),e):(Bt(e),null);case 22:case 23:return nl(e),Bc(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?(l&536870912)!==0&&(e.flags&128)===0&&(Bt(e),e.subtreeFlags&6&&(e.flags|=8192)):Bt(e),l=e.updateQueue,l!==null&&Iu(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==l&&(e.flags|=2048),t!==null&&X(Il),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),el(Qt),Bt(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function Hm(t,e){switch(bc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return el(Qt),Rt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Ht(e),null;case 13:if(nl(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));Ea()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return X(Zt),null;case 4:return Rt(),null;case 10:return el(e.type),null;case 22:case 23:return nl(e),Bc(),t!==null&&X(Il),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return el(Qt),null;case 25:return null;default:return null}}function cd(t,e){switch(bc(e),e.tag){case 3:el(Qt),Rt();break;case 26:case 27:case 5:Ht(e);break;case 4:Rt();break;case 13:nl(e);break;case 19:X(Zt);break;case 10:el(e.type);break;case 22:case 23:nl(e),Bc(),t!==null&&X(Il);break;case 24:el(Qt)}}function qa(t,e){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var a=n.next;l=a;do{if((l.tag&t)===t){n=void 0;var u=l.create,o=l.inst;n=u(),o.destroy=n}l=l.next}while(l!==a)}}catch(d){zt(e,e.return,d)}}function Tl(t,e,l){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var u=a.next;n=u;do{if((n.tag&t)===t){var o=n.inst,d=o.destroy;if(d!==void 0){o.destroy=void 0,a=e;var b=l,D=d;try{D()}catch(N){zt(a,b,N)}}}n=n.next}while(n!==u)}}catch(N){zt(e,e.return,N)}}function sd(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{$r(e,l)}catch(n){zt(t,t.return,n)}}}function od(t,e,l){l.props=en(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(n){zt(t,e,n)}}function Ya(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof l=="function"?t.refCleanup=l(n):l.current=n}}catch(a){zt(t,e,a)}}function Xe(t,e){var l=t.ref,n=t.refCleanup;if(l!==null)if(typeof n=="function")try{n()}catch(a){zt(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(a){zt(t,e,a)}else l.current=null}function rd(t){var e=t.type,l=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break t;case"img":l.src?n.src=l.src:l.srcSet&&(n.srcset=l.srcSet)}}catch(a){zt(t,t.return,a)}}function os(t,e,l){try{var n=t.stateNode;ny(n,t.type,l,e),n[ue]=e}catch(a){zt(t,t.return,a)}}function fd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Ul(t.type)||t.tag===4}function rs(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||fd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Ul(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function fs(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=fi));else if(n!==4&&(n===27&&Ul(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(fs(t,e,l),t=t.sibling;t!==null;)fs(t,e,l),t=t.sibling}function ti(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(n!==4&&(n===27&&Ul(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(ti(t,e,l),t=t.sibling;t!==null;)ti(t,e,l),t=t.sibling}function dd(t){var e=t.stateNode,l=t.memoizedProps;try{for(var n=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);te(e,n,l),e[ee]=t,e[ue]=l}catch(u){zt(t,t.return,u)}}var il=!1,Yt=!1,ds=!1,hd=typeof WeakSet=="function"?WeakSet:Set,$t=null;function jm(t,e){if(t=t.containerInfo,Hs=pi,t=Rr(t),rc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var n=l.getSelection&&l.getSelection();if(n&&n.rangeCount!==0){l=n.anchorNode;var a=n.anchorOffset,u=n.focusNode;n=n.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var o=0,d=-1,b=-1,D=0,N=0,j=t,C=null;e:for(;;){for(var U;j!==l||a!==0&&j.nodeType!==3||(d=o+a),j!==u||n!==0&&j.nodeType!==3||(b=o+n),j.nodeType===3&&(o+=j.nodeValue.length),(U=j.firstChild)!==null;)C=j,j=U;for(;;){if(j===t)break e;if(C===l&&++D===a&&(d=o),C===u&&++N===n&&(b=o),(U=j.nextSibling)!==null)break;j=C,C=j.parentNode}j=U}l=d===-1||b===-1?null:{start:d,end:b}}else l=null}l=l||{start:0,end:0}}else l=null;for(js={focusedElem:t,selectionRange:l},pi=!1,$t=e;$t!==null;)if(e=$t,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,$t=t;else for(;$t!==null;){switch(e=$t,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,a=u.memoizedProps,u=u.memoizedState,n=l.stateNode;try{var rt=en(l.type,a,l.elementType===l.type);t=n.getSnapshotBeforeUpdate(rt,u),n.__reactInternalSnapshotBeforeUpdate=t}catch(ct){zt(l,l.return,ct)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)Ys(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Ys(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,$t=t;break}$t=e.return}}function vd(t,e,l){var n=l.flags;switch(l.tag){case 0:case 11:case 15:Ml(t,l),n&4&&qa(5,l);break;case 1:if(Ml(t,l),n&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(o){zt(l,l.return,o)}else{var a=en(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){zt(l,l.return,o)}}n&64&&sd(l),n&512&&Ya(l,l.return);break;case 3:if(Ml(t,l),n&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{$r(t,e)}catch(o){zt(l,l.return,o)}}break;case 27:e===null&&n&4&&dd(l);case 26:case 5:Ml(t,l),e===null&&n&4&&rd(l),n&512&&Ya(l,l.return);break;case 12:Ml(t,l);break;case 13:Ml(t,l),n&4&&gd(t,l),n&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Km.bind(null,l),ry(t,l))));break;case 22:if(n=l.memoizedState!==null||il,!n){e=e!==null&&e.memoizedState!==null||Yt,a=il;var u=Yt;il=n,(Yt=e)&&!u?Al(t,l,(l.subtreeFlags&8772)!==0):Ml(t,l),il=a,Yt=u}break;case 30:break;default:Ml(t,l)}}function md(t){var e=t.alternate;e!==null&&(t.alternate=null,md(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Zi(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Nt=null,se=!1;function cl(t,e,l){for(l=l.child;l!==null;)yd(t,e,l),l=l.sibling}function yd(t,e,l){if(re&&typeof re.onCommitFiberUnmount=="function")try{re.onCommitFiberUnmount(ca,l)}catch{}switch(l.tag){case 26:Yt||Xe(l,e),cl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Yt||Xe(l,e);var n=Nt,a=se;Ul(l.type)&&(Nt=l.stateNode,se=!1),cl(t,e,l),ka(l.stateNode),Nt=n,se=a;break;case 5:Yt||Xe(l,e);case 6:if(n=Nt,a=se,Nt=null,cl(t,e,l),Nt=n,se=a,Nt!==null)if(se)try{(Nt.nodeType===9?Nt.body:Nt.nodeName==="HTML"?Nt.ownerDocument.body:Nt).removeChild(l.stateNode)}catch(u){zt(l,e,u)}else try{Nt.removeChild(l.stateNode)}catch(u){zt(l,e,u)}break;case 18:Nt!==null&&(se?(t=Nt,ah(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),nu(t)):ah(Nt,l.stateNode));break;case 4:n=Nt,a=se,Nt=l.stateNode.containerInfo,se=!0,cl(t,e,l),Nt=n,se=a;break;case 0:case 11:case 14:case 15:Yt||Tl(2,l,e),Yt||Tl(4,l,e),cl(t,e,l);break;case 1:Yt||(Xe(l,e),n=l.stateNode,typeof n.componentWillUnmount=="function"&&od(l,e,n)),cl(t,e,l);break;case 21:cl(t,e,l);break;case 22:Yt=(n=Yt)||l.memoizedState!==null,cl(t,e,l),Yt=n;break;default:cl(t,e,l)}}function gd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{nu(t)}catch(l){zt(e,e.return,l)}}function wm(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new hd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new hd),e;default:throw Error(s(435,t.tag))}}function hs(t,e){var l=wm(t);e.forEach(function(n){var a=Jm.bind(null,t,n);l.has(n)||(l.add(n),n.then(a,a))})}function ve(t,e){var l=e.deletions;if(l!==null)for(var n=0;n<l.length;n++){var a=l[n],u=t,o=e,d=o;t:for(;d!==null;){switch(d.tag){case 27:if(Ul(d.type)){Nt=d.stateNode,se=!1;break t}break;case 5:Nt=d.stateNode,se=!1;break t;case 3:case 4:Nt=d.stateNode.containerInfo,se=!0;break t}d=d.return}if(Nt===null)throw Error(s(160));yd(u,o,a),Nt=null,se=!1,u=a.alternate,u!==null&&(u.return=null),a.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)pd(e,t),e=e.sibling}var Be=null;function pd(t,e){var l=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ve(e,t),me(t),n&4&&(Tl(3,t,t.return),qa(3,t),Tl(5,t,t.return));break;case 1:ve(e,t),me(t),n&512&&(Yt||l===null||Xe(l,l.return)),n&64&&il&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?n:l.concat(n))));break;case 26:var a=Be;if(ve(e,t),me(t),n&512&&(Yt||l===null||Xe(l,l.return)),n&4){var u=l!==null?l.memoizedState:null;if(n=t.memoizedState,l===null)if(n===null)if(t.stateNode===null){t:{n=t.type,l=t.memoizedProps,a=a.ownerDocument||a;e:switch(n){case"title":u=a.getElementsByTagName("title")[0],(!u||u[ra]||u[ee]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=a.createElement(n),a.head.insertBefore(u,a.querySelector("head > title"))),te(u,n,l),u[ee]=t,Kt(u),n=u;break t;case"link":var o=dh("link","href",a).get(n+(l.href||""));if(o){for(var d=0;d<o.length;d++)if(u=o[d],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){o.splice(d,1);break e}}u=a.createElement(n),te(u,n,l),a.head.appendChild(u);break;case"meta":if(o=dh("meta","content",a).get(n+(l.content||""))){for(d=0;d<o.length;d++)if(u=o[d],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){o.splice(d,1);break e}}u=a.createElement(n),te(u,n,l),a.head.appendChild(u);break;default:throw Error(s(468,n))}u[ee]=t,Kt(u),n=u}t.stateNode=n}else hh(a,t.type,t.stateNode);else t.stateNode=fh(a,n,t.memoizedProps);else u!==n?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,n===null?hh(a,t.type,t.stateNode):fh(a,n,t.memoizedProps)):n===null&&t.stateNode!==null&&os(t,t.memoizedProps,l.memoizedProps)}break;case 27:ve(e,t),me(t),n&512&&(Yt||l===null||Xe(l,l.return)),l!==null&&n&4&&os(t,t.memoizedProps,l.memoizedProps);break;case 5:if(ve(e,t),me(t),n&512&&(Yt||l===null||Xe(l,l.return)),t.flags&32){a=t.stateNode;try{_n(a,"")}catch(U){zt(t,t.return,U)}}n&4&&t.stateNode!=null&&(a=t.memoizedProps,os(t,a,l!==null?l.memoizedProps:a)),n&1024&&(ds=!0);break;case 6:if(ve(e,t),me(t),n&4){if(t.stateNode===null)throw Error(s(162));n=t.memoizedProps,l=t.stateNode;try{l.nodeValue=n}catch(U){zt(t,t.return,U)}}break;case 3:if(mi=null,a=Be,Be=hi(e.containerInfo),ve(e,t),Be=a,me(t),n&4&&l!==null&&l.memoizedState.isDehydrated)try{nu(e.containerInfo)}catch(U){zt(t,t.return,U)}ds&&(ds=!1,Sd(t));break;case 4:n=Be,Be=hi(t.stateNode.containerInfo),ve(e,t),me(t),Be=n;break;case 12:ve(e,t),me(t);break;case 13:ve(e,t),me(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Ss=_e()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,hs(t,n)));break;case 22:a=t.memoizedState!==null;var b=l!==null&&l.memoizedState!==null,D=il,N=Yt;if(il=D||a,Yt=N||b,ve(e,t),Yt=N,il=D,me(t),n&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(l===null||b||il||Yt||ln(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){b=l=e;try{if(u=b.stateNode,a)o=u.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{d=b.stateNode;var j=b.memoizedProps.style,C=j!=null&&j.hasOwnProperty("display")?j.display:null;d.style.display=C==null||typeof C=="boolean"?"":(""+C).trim()}}catch(U){zt(b,b.return,U)}}}else if(e.tag===6){if(l===null){b=e;try{b.stateNode.nodeValue=a?"":b.memoizedProps}catch(U){zt(b,b.return,U)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(l=n.retryQueue,l!==null&&(n.retryQueue=null,hs(t,l))));break;case 19:ve(e,t),me(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,hs(t,n)));break;case 30:break;case 21:break;default:ve(e,t),me(t)}}function me(t){var e=t.flags;if(e&2){try{for(var l,n=t.return;n!==null;){if(fd(n)){l=n;break}n=n.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var a=l.stateNode,u=rs(t);ti(t,u,a);break;case 5:var o=l.stateNode;l.flags&32&&(_n(o,""),l.flags&=-33);var d=rs(t);ti(t,d,o);break;case 3:case 4:var b=l.stateNode.containerInfo,D=rs(t);fs(t,D,b);break;default:throw Error(s(161))}}catch(N){zt(t,t.return,N)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Sd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Sd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ml(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)vd(t,e.alternate,e),e=e.sibling}function ln(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Tl(4,e,e.return),ln(e);break;case 1:Xe(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&od(e,e.return,l),ln(e);break;case 27:ka(e.stateNode);case 26:case 5:Xe(e,e.return),ln(e);break;case 22:e.memoizedState===null&&ln(e);break;case 30:ln(e);break;default:ln(e)}t=t.sibling}}function Al(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,a=t,u=e,o=u.flags;switch(u.tag){case 0:case 11:case 15:Al(a,u,l),qa(4,u);break;case 1:if(Al(a,u,l),n=u,a=n.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(D){zt(n,n.return,D)}if(n=u,a=n.updateQueue,a!==null){var d=n.stateNode;try{var b=a.shared.hiddenCallbacks;if(b!==null)for(a.shared.hiddenCallbacks=null,a=0;a<b.length;a++)Jr(b[a],d)}catch(D){zt(n,n.return,D)}}l&&o&64&&sd(u),Ya(u,u.return);break;case 27:dd(u);case 26:case 5:Al(a,u,l),l&&n===null&&o&4&&rd(u),Ya(u,u.return);break;case 12:Al(a,u,l);break;case 13:Al(a,u,l),l&&o&4&&gd(a,u);break;case 22:u.memoizedState===null&&Al(a,u,l),Ya(u,u.return);break;case 30:break;default:Al(a,u,l)}e=e.sibling}}function vs(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&Ma(l))}function ms(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ma(t))}function Qe(t,e,l,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)_d(t,e,l,n),e=e.sibling}function _d(t,e,l,n){var a=e.flags;switch(e.tag){case 0:case 11:case 15:Qe(t,e,l,n),a&2048&&qa(9,e);break;case 1:Qe(t,e,l,n);break;case 3:Qe(t,e,l,n),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ma(t)));break;case 12:if(a&2048){Qe(t,e,l,n),t=e.stateNode;try{var u=e.memoizedProps,o=u.id,d=u.onPostCommit;typeof d=="function"&&d(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(b){zt(e,e.return,b)}}else Qe(t,e,l,n);break;case 13:Qe(t,e,l,n);break;case 23:break;case 22:u=e.stateNode,o=e.alternate,e.memoizedState!==null?u._visibility&2?Qe(t,e,l,n):Va(t,e):u._visibility&2?Qe(t,e,l,n):(u._visibility|=2,qn(t,e,l,n,(e.subtreeFlags&10256)!==0)),a&2048&&vs(o,e);break;case 24:Qe(t,e,l,n),a&2048&&ms(e.alternate,e);break;default:Qe(t,e,l,n)}}function qn(t,e,l,n,a){for(a=a&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,o=e,d=l,b=n,D=o.flags;switch(o.tag){case 0:case 11:case 15:qn(u,o,d,b,a),qa(8,o);break;case 23:break;case 22:var N=o.stateNode;o.memoizedState!==null?N._visibility&2?qn(u,o,d,b,a):Va(u,o):(N._visibility|=2,qn(u,o,d,b,a)),a&&D&2048&&vs(o.alternate,o);break;case 24:qn(u,o,d,b,a),a&&D&2048&&ms(o.alternate,o);break;default:qn(u,o,d,b,a)}e=e.sibling}}function Va(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,n=e,a=n.flags;switch(n.tag){case 22:Va(l,n),a&2048&&vs(n.alternate,n);break;case 24:Va(l,n),a&2048&&ms(n.alternate,n);break;default:Va(l,n)}e=e.sibling}}var Ga=8192;function Yn(t){if(t.subtreeFlags&Ga)for(t=t.child;t!==null;)bd(t),t=t.sibling}function bd(t){switch(t.tag){case 26:Yn(t),t.flags&Ga&&t.memoizedState!==null&&Ry(Be,t.memoizedState,t.memoizedProps);break;case 5:Yn(t);break;case 3:case 4:var e=Be;Be=hi(t.stateNode.containerInfo),Yn(t),Be=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ga,Ga=16777216,Yn(t),Ga=e):Yn(t));break;default:Yn(t)}}function Ed(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Xa(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];$t=n,Td(n,t)}Ed(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Rd(t),t=t.sibling}function Rd(t){switch(t.tag){case 0:case 11:case 15:Xa(t),t.flags&2048&&Tl(9,t,t.return);break;case 3:Xa(t);break;case 12:Xa(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ei(t)):Xa(t);break;default:Xa(t)}}function ei(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];$t=n,Td(n,t)}Ed(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Tl(8,e,e.return),ei(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,ei(e));break;default:ei(e)}t=t.sibling}}function Td(t,e){for(;$t!==null;){var l=$t;switch(l.tag){case 0:case 11:case 15:Tl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var n=l.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Ma(l.memoizedState.cache)}if(n=l.child,n!==null)n.return=l,$t=n;else t:for(l=t;$t!==null;){n=$t;var a=n.sibling,u=n.return;if(md(n),n===l){$t=null;break t}if(a!==null){a.return=u,$t=a;break t}$t=u}}}var qm={getCacheForType:function(t){var e=le(Qt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Ym=typeof WeakMap=="function"?WeakMap:Map,Mt=0,Ct=null,yt=null,St=0,At=0,ye=null,Ol=!1,Vn=!1,ys=!1,sl=0,wt=0,xl=0,nn=0,gs=0,xe=0,Gn=0,Qa=null,oe=null,ps=!1,Ss=0,li=1/0,ni=null,Dl=null,It=0,zl=null,Xn=null,Qn=0,_s=0,bs=null,Md=null,Za=0,Es=null;function ge(){if((Mt&2)!==0&&St!==0)return St&-St;if(x.T!==null){var t=Cn;return t!==0?t:Ds()}return Yo()}function Ad(){xe===0&&(xe=(St&536870912)===0||Tt?Ho():536870912);var t=Oe.current;return t!==null&&(t.flags|=32),xe}function pe(t,e,l){(t===Ct&&(At===2||At===9)||t.cancelPendingCommit!==null)&&(Zn(t,0),Cl(t,St,xe,!1)),oa(t,l),((Mt&2)===0||t!==Ct)&&(t===Ct&&((Mt&2)===0&&(nn|=l),wt===4&&Cl(t,St,xe,!1)),Ze(t))}function Od(t,e,l){if((Mt&6)!==0)throw Error(s(327));var n=!l&&(e&124)===0&&(e&t.expiredLanes)===0||sa(t,e),a=n?Xm(t,e):Ms(t,e,!0),u=n;do{if(a===0){Vn&&!n&&Cl(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!Vm(l)){a=Ms(t,e,!1),u=!1;continue}if(a===2){if(u=e,t.errorRecoveryDisabledLanes&u)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var d=t;a=Qa;var b=d.current.memoizedState.isDehydrated;if(b&&(Zn(d,o).flags|=256),o=Ms(d,o,!1),o!==2){if(ys&&!b){d.errorRecoveryDisabledLanes|=u,nn|=u,a=4;break t}u=oe,oe=a,u!==null&&(oe===null?oe=u:oe.push.apply(oe,u))}a=o}if(u=!1,a!==2)continue}}if(a===1){Zn(t,0),Cl(t,e,0,!0);break}t:{switch(n=t,u=a,u){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:Cl(n,e,xe,!Ol);break t;case 2:oe=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(a=Ss+300-_e(),10<a)){if(Cl(n,e,xe,!Ol),vu(n,0,!0)!==0)break t;n.timeoutHandle=lh(xd.bind(null,n,l,oe,ni,ps,e,xe,nn,Gn,Ol,u,2,-0,0),a);break t}xd(n,l,oe,ni,ps,e,xe,nn,Gn,Ol,u,0,-0,0)}}break}while(!0);Ze(t)}function xd(t,e,l,n,a,u,o,d,b,D,N,j,C,U){if(t.timeoutHandle=-1,j=e.subtreeFlags,(j&8192||(j&16785408)===16785408)&&(Fa={stylesheets:null,count:0,unsuspend:Ey},bd(e),j=Ty(),j!==null)){t.cancelPendingCommit=j(Bd.bind(null,t,e,u,l,n,a,o,d,b,N,1,C,U)),Cl(t,u,o,!D);return}Bd(t,e,u,l,n,a,o,d,b)}function Vm(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var n=0;n<l.length;n++){var a=l[n],u=a.getSnapshot;a=a.value;try{if(!de(u(),a))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Cl(t,e,l,n){e&=~gs,e&=~nn,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var a=e;0<a;){var u=31-fe(a),o=1<<u;n[u]=-1,a&=~o}l!==0&&wo(t,l,e)}function ai(){return(Mt&6)===0?(Ka(0),!1):!0}function Rs(){if(yt!==null){if(At===0)var t=yt.return;else t=yt,tl=Wl=null,Yc(t),jn=null,Ha=0,t=yt;for(;t!==null;)cd(t.alternate,t),t=t.return;yt=null}}function Zn(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,uy(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Rs(),Ct=t,yt=l=We(t.current,null),St=e,At=0,ye=null,Ol=!1,Vn=sa(t,e),ys=!1,Gn=xe=gs=nn=xl=wt=0,oe=Qa=null,ps=!1,(e&8)!==0&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var a=31-fe(n),u=1<<a;e|=t[a],n&=~u}return sl=e,Au(),l}function Dd(t,e){ht=null,x.H=Zu,e===Oa||e===Bu?(e=Zr(),At=3):e===Gr?(e=Zr(),At=4):At=e===Jf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ye=e,yt===null&&(wt=1,Pu(t,Re(e,t.current)))}function zd(){var t=x.H;return x.H=Zu,t===null?Zu:t}function Cd(){var t=x.A;return x.A=qm,t}function Ts(){wt=4,Ol||(St&4194048)!==St&&Oe.current!==null||(Vn=!0),(xl&134217727)===0&&(nn&134217727)===0||Ct===null||Cl(Ct,St,xe,!1)}function Ms(t,e,l){var n=Mt;Mt|=2;var a=zd(),u=Cd();(Ct!==t||St!==e)&&(ni=null,Zn(t,e)),e=!1;var o=wt;t:do try{if(At!==0&&yt!==null){var d=yt,b=ye;switch(At){case 8:Rs(),o=6;break t;case 3:case 2:case 9:case 6:Oe.current===null&&(e=!0);var D=At;if(At=0,ye=null,Kn(t,d,b,D),l&&Vn){o=0;break t}break;default:D=At,At=0,ye=null,Kn(t,d,b,D)}}Gm(),o=wt;break}catch(N){Dd(t,N)}while(!0);return e&&t.shellSuspendCounter++,tl=Wl=null,Mt=n,x.H=a,x.A=u,yt===null&&(Ct=null,St=0,Au()),o}function Gm(){for(;yt!==null;)Ud(yt)}function Xm(t,e){var l=Mt;Mt|=2;var n=zd(),a=Cd();Ct!==t||St!==e?(ni=null,li=_e()+500,Zn(t,e)):Vn=sa(t,e);t:do try{if(At!==0&&yt!==null){e=yt;var u=ye;e:switch(At){case 1:At=0,ye=null,Kn(t,e,u,1);break;case 2:case 9:if(Xr(u)){At=0,ye=null,Nd(e);break}e=function(){At!==2&&At!==9||Ct!==t||(At=7),Ze(t)},u.then(e,e);break t;case 3:At=7;break t;case 4:At=5;break t;case 7:Xr(u)?(At=0,ye=null,Nd(e)):(At=0,ye=null,Kn(t,e,u,7));break;case 5:var o=null;switch(yt.tag){case 26:o=yt.memoizedState;case 5:case 27:var d=yt;if(!o||vh(o)){At=0,ye=null;var b=d.sibling;if(b!==null)yt=b;else{var D=d.return;D!==null?(yt=D,ui(D)):yt=null}break e}}At=0,ye=null,Kn(t,e,u,5);break;case 6:At=0,ye=null,Kn(t,e,u,6);break;case 8:Rs(),wt=6;break t;default:throw Error(s(462))}}Qm();break}catch(N){Dd(t,N)}while(!0);return tl=Wl=null,x.H=n,x.A=a,Mt=l,yt!==null?0:(Ct=null,St=0,Au(),wt)}function Qm(){for(;yt!==null&&!qi();)Ud(yt)}function Ud(t){var e=ud(t.alternate,t,sl);t.memoizedProps=t.pendingProps,e===null?ui(t):yt=e}function Nd(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=If(l,e,e.pendingProps,e.type,void 0,St);break;case 11:e=If(l,e,e.pendingProps,e.type.render,e.ref,St);break;case 5:Yc(e);default:cd(l,e),e=yt=Nr(e,sl),e=ud(l,e,sl)}t.memoizedProps=t.pendingProps,e===null?ui(t):yt=e}function Kn(t,e,l,n){tl=Wl=null,Yc(e),jn=null,Ha=0;var a=e.return;try{if(Nm(t,a,e,l,St)){wt=1,Pu(t,Re(l,t.current)),yt=null;return}}catch(u){if(a!==null)throw yt=a,u;wt=1,Pu(t,Re(l,t.current)),yt=null;return}e.flags&32768?(Tt||n===1?t=!0:Vn||(St&536870912)!==0?t=!1:(Ol=t=!0,(n===2||n===9||n===3||n===6)&&(n=Oe.current,n!==null&&n.tag===13&&(n.flags|=16384))),Ld(e,t)):ui(e)}function ui(t){var e=t;do{if((e.flags&32768)!==0){Ld(e,Ol);return}t=e.return;var l=Bm(e.alternate,e,sl);if(l!==null){yt=l;return}if(e=e.sibling,e!==null){yt=e;return}yt=e=t}while(e!==null);wt===0&&(wt=5)}function Ld(t,e){do{var l=Hm(t.alternate,t);if(l!==null){l.flags&=32767,yt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){yt=t;return}yt=t=l}while(t!==null);wt=6,yt=null}function Bd(t,e,l,n,a,u,o,d,b){t.cancelPendingCommit=null;do ii();while(It!==0);if((Mt&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(u=e.lanes|e.childLanes,u|=mc,Ev(t,l,u,o,d,b),t===Ct&&(yt=Ct=null,St=0),Xn=e,zl=t,Qn=l,_s=u,bs=a,Md=n,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,$m(Yl,function(){return Yd(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||n){n=x.T,x.T=null,a=w.p,w.p=2,o=Mt,Mt|=4;try{jm(t,e,l)}finally{Mt=o,w.p=a,x.T=n}}It=1,Hd(),jd(),wd()}}function Hd(){if(It===1){It=0;var t=zl,e=Xn,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=x.T,x.T=null;var n=w.p;w.p=2;var a=Mt;Mt|=4;try{pd(e,t);var u=js,o=Rr(t.containerInfo),d=u.focusedElem,b=u.selectionRange;if(o!==d&&d&&d.ownerDocument&&Er(d.ownerDocument.documentElement,d)){if(b!==null&&rc(d)){var D=b.start,N=b.end;if(N===void 0&&(N=D),"selectionStart"in d)d.selectionStart=D,d.selectionEnd=Math.min(N,d.value.length);else{var j=d.ownerDocument||document,C=j&&j.defaultView||window;if(C.getSelection){var U=C.getSelection(),rt=d.textContent.length,ct=Math.min(b.start,rt),Dt=b.end===void 0?ct:Math.min(b.end,rt);!U.extend&&ct>Dt&&(o=Dt,Dt=ct,ct=o);var M=br(d,ct),T=br(d,Dt);if(M&&T&&(U.rangeCount!==1||U.anchorNode!==M.node||U.anchorOffset!==M.offset||U.focusNode!==T.node||U.focusOffset!==T.offset)){var O=j.createRange();O.setStart(M.node,M.offset),U.removeAllRanges(),ct>Dt?(U.addRange(O),U.extend(T.node,T.offset)):(O.setEnd(T.node,T.offset),U.addRange(O))}}}}for(j=[],U=d;U=U.parentNode;)U.nodeType===1&&j.push({element:U,left:U.scrollLeft,top:U.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<j.length;d++){var B=j[d];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}pi=!!Hs,js=Hs=null}finally{Mt=a,w.p=n,x.T=l}}t.current=e,It=2}}function jd(){if(It===2){It=0;var t=zl,e=Xn,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=x.T,x.T=null;var n=w.p;w.p=2;var a=Mt;Mt|=4;try{vd(t,e.alternate,e)}finally{Mt=a,w.p=n,x.T=l}}It=3}}function wd(){if(It===4||It===3){It=0,Yi();var t=zl,e=Xn,l=Qn,n=Md;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?It=5:(It=0,Xn=zl=null,qd(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&(Dl=null),Xi(l),e=e.stateNode,re&&typeof re.onCommitFiberRoot=="function")try{re.onCommitFiberRoot(ca,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=x.T,a=w.p,w.p=2,x.T=null;try{for(var u=t.onRecoverableError,o=0;o<n.length;o++){var d=n[o];u(d.value,{componentStack:d.stack})}}finally{x.T=e,w.p=a}}(Qn&3)!==0&&ii(),Ze(t),a=t.pendingLanes,(l&4194090)!==0&&(a&42)!==0?t===Es?Za++:(Za=0,Es=t):Za=0,Ka(0)}}function qd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ma(e)))}function ii(t){return Hd(),jd(),wd(),Yd()}function Yd(){if(It!==5)return!1;var t=zl,e=_s;_s=0;var l=Xi(Qn),n=x.T,a=w.p;try{w.p=32>l?32:l,x.T=null,l=bs,bs=null;var u=zl,o=Qn;if(It=0,Xn=zl=null,Qn=0,(Mt&6)!==0)throw Error(s(331));var d=Mt;if(Mt|=4,Rd(u.current),_d(u,u.current,o,l),Mt=d,Ka(0,!1),re&&typeof re.onPostCommitFiberRoot=="function")try{re.onPostCommitFiberRoot(ca,u)}catch{}return!0}finally{w.p=a,x.T=n,qd(t,e)}}function Vd(t,e,l){e=Re(l,e),e=ts(t.stateNode,e,2),t=_l(t,e,2),t!==null&&(oa(t,2),Ze(t))}function zt(t,e,l){if(t.tag===3)Vd(t,t,l);else for(;e!==null;){if(e.tag===3){Vd(e,t,l);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Dl===null||!Dl.has(n))){t=Re(l,t),l=Zf(2),n=_l(e,l,2),n!==null&&(Kf(l,n,e,t),oa(n,2),Ze(n));break}}e=e.return}}function As(t,e,l){var n=t.pingCache;if(n===null){n=t.pingCache=new Ym;var a=new Set;n.set(e,a)}else a=n.get(e),a===void 0&&(a=new Set,n.set(e,a));a.has(l)||(ys=!0,a.add(l),t=Zm.bind(null,t,e,l),e.then(t,t))}function Zm(t,e,l){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Ct===t&&(St&l)===l&&(wt===4||wt===3&&(St&62914560)===St&&300>_e()-Ss?(Mt&2)===0&&Zn(t,0):gs|=l,Gn===St&&(Gn=0)),Ze(t)}function Gd(t,e){e===0&&(e=jo()),t=On(t,e),t!==null&&(oa(t,e),Ze(t))}function Km(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Gd(t,l)}function Jm(t,e){var l=0;switch(t.tag){case 13:var n=t.stateNode,a=t.memoizedState;a!==null&&(l=a.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(s(314))}n!==null&&n.delete(e),Gd(t,l)}function $m(t,e){return qe(t,e)}var ci=null,Jn=null,Os=!1,si=!1,xs=!1,an=0;function Ze(t){t!==Jn&&t.next===null&&(Jn===null?ci=Jn=t:Jn=Jn.next=t),si=!0,Os||(Os=!0,Pm())}function Ka(t,e){if(!xs&&si){xs=!0;do for(var l=!1,n=ci;n!==null;){if(t!==0){var a=n.pendingLanes;if(a===0)var u=0;else{var o=n.suspendedLanes,d=n.pingedLanes;u=(1<<31-fe(42|t)+1)-1,u&=a&~(o&~d),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,Kd(n,u))}else u=St,u=vu(n,n===Ct?u:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(u&3)===0||sa(n,u)||(l=!0,Kd(n,u));n=n.next}while(l);xs=!1}}function km(){Xd()}function Xd(){si=Os=!1;var t=0;an!==0&&(ay()&&(t=an),an=0);for(var e=_e(),l=null,n=ci;n!==null;){var a=n.next,u=Qd(n,e);u===0?(n.next=null,l===null?ci=a:l.next=a,a===null&&(Jn=l)):(l=n,(t!==0||(u&3)!==0)&&(si=!0)),n=a}Ka(t)}function Qd(t,e){for(var l=t.suspendedLanes,n=t.pingedLanes,a=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var o=31-fe(u),d=1<<o,b=a[o];b===-1?((d&l)===0||(d&n)!==0)&&(a[o]=bv(d,e)):b<=e&&(t.expiredLanes|=d),u&=~d}if(e=Ct,l=St,l=vu(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,l===0||t===e&&(At===2||At===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&fn(n),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||sa(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(n!==null&&fn(n),Xi(l)){case 2:case 8:l=ia;break;case 32:l=Yl;break;case 268435456:l=Wt;break;default:l=Yl}return n=Zd.bind(null,t),l=qe(l,n),t.callbackPriority=e,t.callbackNode=l,e}return n!==null&&n!==null&&fn(n),t.callbackPriority=2,t.callbackNode=null,2}function Zd(t,e){if(It!==0&&It!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(ii()&&t.callbackNode!==l)return null;var n=St;return n=vu(t,t===Ct?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:(Od(t,n,e),Qd(t,_e()),t.callbackNode!=null&&t.callbackNode===l?Zd.bind(null,t):null)}function Kd(t,e){if(ii())return null;Od(t,e,!0)}function Pm(){iy(function(){(Mt&6)!==0?qe(ql,km):Xd()})}function Ds(){return an===0&&(an=Ho()),an}function Jd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Su(""+t)}function $d(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Wm(t,e,l,n,a){if(e==="submit"&&l&&l.stateNode===a){var u=Jd((a[ue]||null).action),o=n.submitter;o&&(e=(e=o[ue]||null)?Jd(e.formAction):o.getAttribute("formAction"),e!==null&&(u=e,o=null));var d=new Ru("action","action",null,n,a);t.push({event:d,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(an!==0){var b=o?$d(a,o):new FormData(a);kc(l,{pending:!0,data:b,method:a.method,action:u},null,b)}}else typeof u=="function"&&(d.preventDefault(),b=o?$d(a,o):new FormData(a),kc(l,{pending:!0,data:b,method:a.method,action:u},u,b))},currentTarget:a}]})}}for(var zs=0;zs<vc.length;zs++){var Cs=vc[zs],Fm=Cs.toLowerCase(),Im=Cs[0].toUpperCase()+Cs.slice(1);Le(Fm,"on"+Im)}Le(Ar,"onAnimationEnd"),Le(Or,"onAnimationIteration"),Le(xr,"onAnimationStart"),Le("dblclick","onDoubleClick"),Le("focusin","onFocus"),Le("focusout","onBlur"),Le(ym,"onTransitionRun"),Le(gm,"onTransitionStart"),Le(pm,"onTransitionCancel"),Le(Dr,"onTransitionEnd"),gn("onMouseEnter",["mouseout","mouseover"]),gn("onMouseLeave",["mouseout","mouseover"]),gn("onPointerEnter",["pointerout","pointerover"]),gn("onPointerLeave",["pointerout","pointerover"]),Gl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Gl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Gl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Gl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Gl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Gl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ja="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ty=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ja));function kd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var n=t[l],a=n.event;n=n.listeners;t:{var u=void 0;if(e)for(var o=n.length-1;0<=o;o--){var d=n[o],b=d.instance,D=d.currentTarget;if(d=d.listener,b!==u&&a.isPropagationStopped())break t;u=d,a.currentTarget=D;try{u(a)}catch(N){ku(N)}a.currentTarget=null,u=b}else for(o=0;o<n.length;o++){if(d=n[o],b=d.instance,D=d.currentTarget,d=d.listener,b!==u&&a.isPropagationStopped())break t;u=d,a.currentTarget=D;try{u(a)}catch(N){ku(N)}a.currentTarget=null,u=b}}}}function gt(t,e){var l=e[Qi];l===void 0&&(l=e[Qi]=new Set);var n=t+"__bubble";l.has(n)||(Pd(e,t,2,!1),l.add(n))}function Us(t,e,l){var n=0;e&&(n|=4),Pd(l,t,n,e)}var oi="_reactListening"+Math.random().toString(36).slice(2);function Ns(t){if(!t[oi]){t[oi]=!0,Go.forEach(function(l){l!=="selectionchange"&&(ty.has(l)||Us(l,!1,t),Us(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[oi]||(e[oi]=!0,Us("selectionchange",!1,e))}}function Pd(t,e,l,n){switch(_h(e)){case 2:var a=Oy;break;case 8:a=xy;break;default:a=Js}l=a.bind(null,e,l,t),a=void 0,!ec||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),n?a!==void 0?t.addEventListener(e,l,{capture:!0,passive:a}):t.addEventListener(e,l,!0):a!==void 0?t.addEventListener(e,l,{passive:a}):t.addEventListener(e,l,!1)}function Ls(t,e,l,n,a){var u=n;if((e&1)===0&&(e&2)===0&&n!==null)t:for(;;){if(n===null)return;var o=n.tag;if(o===3||o===4){var d=n.stateNode.containerInfo;if(d===a)break;if(o===4)for(o=n.return;o!==null;){var b=o.tag;if((b===3||b===4)&&o.stateNode.containerInfo===a)return;o=o.return}for(;d!==null;){if(o=vn(d),o===null)return;if(b=o.tag,b===5||b===6||b===26||b===27){n=u=o;continue t}d=d.parentNode}}n=n.return}lr(function(){var D=u,N=Ii(l),j=[];t:{var C=zr.get(t);if(C!==void 0){var U=Ru,rt=t;switch(t){case"keypress":if(bu(l)===0)break t;case"keydown":case"keyup":U=$v;break;case"focusin":rt="focus",U=uc;break;case"focusout":rt="blur",U=uc;break;case"beforeblur":case"afterblur":U=uc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":U=ur;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":U=Hv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":U=Wv;break;case Ar:case Or:case xr:U=qv;break;case Dr:U=Iv;break;case"scroll":case"scrollend":U=Lv;break;case"wheel":U=em;break;case"copy":case"cut":case"paste":U=Vv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":U=cr;break;case"toggle":case"beforetoggle":U=nm}var ct=(e&4)!==0,Dt=!ct&&(t==="scroll"||t==="scrollend"),M=ct?C!==null?C+"Capture":null:C;ct=[];for(var T=D,O;T!==null;){var B=T;if(O=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||O===null||M===null||(B=da(T,M),B!=null&&ct.push($a(T,B,O))),Dt)break;T=T.return}0<ct.length&&(C=new U(C,rt,null,l,N),j.push({event:C,listeners:ct}))}}if((e&7)===0){t:{if(C=t==="mouseover"||t==="pointerover",U=t==="mouseout"||t==="pointerout",C&&l!==Fi&&(rt=l.relatedTarget||l.fromElement)&&(vn(rt)||rt[hn]))break t;if((U||C)&&(C=N.window===N?N:(C=N.ownerDocument)?C.defaultView||C.parentWindow:window,U?(rt=l.relatedTarget||l.toElement,U=D,rt=rt?vn(rt):null,rt!==null&&(Dt=h(rt),ct=rt.tag,rt!==Dt||ct!==5&&ct!==27&&ct!==6)&&(rt=null)):(U=null,rt=D),U!==rt)){if(ct=ur,B="onMouseLeave",M="onMouseEnter",T="mouse",(t==="pointerout"||t==="pointerover")&&(ct=cr,B="onPointerLeave",M="onPointerEnter",T="pointer"),Dt=U==null?C:fa(U),O=rt==null?C:fa(rt),C=new ct(B,T+"leave",U,l,N),C.target=Dt,C.relatedTarget=O,B=null,vn(N)===D&&(ct=new ct(M,T+"enter",rt,l,N),ct.target=O,ct.relatedTarget=Dt,B=ct),Dt=B,U&&rt)e:{for(ct=U,M=rt,T=0,O=ct;O;O=$n(O))T++;for(O=0,B=M;B;B=$n(B))O++;for(;0<T-O;)ct=$n(ct),T--;for(;0<O-T;)M=$n(M),O--;for(;T--;){if(ct===M||M!==null&&ct===M.alternate)break e;ct=$n(ct),M=$n(M)}ct=null}else ct=null;U!==null&&Wd(j,C,U,ct,!1),rt!==null&&Dt!==null&&Wd(j,Dt,rt,ct,!0)}}t:{if(C=D?fa(D):window,U=C.nodeName&&C.nodeName.toLowerCase(),U==="select"||U==="input"&&C.type==="file")var I=mr;else if(hr(C))if(yr)I=hm;else{I=fm;var vt=rm}else U=C.nodeName,!U||U.toLowerCase()!=="input"||C.type!=="checkbox"&&C.type!=="radio"?D&&Wi(D.elementType)&&(I=mr):I=dm;if(I&&(I=I(t,D))){vr(j,I,l,N);break t}vt&&vt(t,C,D),t==="focusout"&&D&&C.type==="number"&&D.memoizedProps.value!=null&&Pi(C,"number",C.value)}switch(vt=D?fa(D):window,t){case"focusin":(hr(vt)||vt.contentEditable==="true")&&(Tn=vt,fc=D,_a=null);break;case"focusout":_a=fc=Tn=null;break;case"mousedown":dc=!0;break;case"contextmenu":case"mouseup":case"dragend":dc=!1,Tr(j,l,N);break;case"selectionchange":if(mm)break;case"keydown":case"keyup":Tr(j,l,N)}var at;if(cc)t:{switch(t){case"compositionstart":var st="onCompositionStart";break t;case"compositionend":st="onCompositionEnd";break t;case"compositionupdate":st="onCompositionUpdate";break t}st=void 0}else Rn?fr(t,l)&&(st="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(st="onCompositionStart");st&&(sr&&l.locale!=="ko"&&(Rn||st!=="onCompositionStart"?st==="onCompositionEnd"&&Rn&&(at=nr()):(yl=N,lc="value"in yl?yl.value:yl.textContent,Rn=!0)),vt=ri(D,st),0<vt.length&&(st=new ir(st,t,null,l,N),j.push({event:st,listeners:vt}),at?st.data=at:(at=dr(l),at!==null&&(st.data=at)))),(at=um?im(t,l):cm(t,l))&&(st=ri(D,"onBeforeInput"),0<st.length&&(vt=new ir("onBeforeInput","beforeinput",null,l,N),j.push({event:vt,listeners:st}),vt.data=at)),Wm(j,t,D,l,N)}kd(j,e)})}function $a(t,e,l){return{instance:t,listener:e,currentTarget:l}}function ri(t,e){for(var l=e+"Capture",n=[];t!==null;){var a=t,u=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||u===null||(a=da(t,l),a!=null&&n.unshift($a(t,a,u)),a=da(t,e),a!=null&&n.push($a(t,a,u))),t.tag===3)return n;t=t.return}return[]}function $n(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Wd(t,e,l,n,a){for(var u=e._reactName,o=[];l!==null&&l!==n;){var d=l,b=d.alternate,D=d.stateNode;if(d=d.tag,b!==null&&b===n)break;d!==5&&d!==26&&d!==27||D===null||(b=D,a?(D=da(l,u),D!=null&&o.unshift($a(l,D,b))):a||(D=da(l,u),D!=null&&o.push($a(l,D,b)))),l=l.return}o.length!==0&&t.push({event:e,listeners:o})}var ey=/\r\n?/g,ly=/\u0000|\uFFFD/g;function Fd(t){return(typeof t=="string"?t:""+t).replace(ey,`
`).replace(ly,"")}function Id(t,e){return e=Fd(e),Fd(t)===e}function fi(){}function xt(t,e,l,n,a,u){switch(l){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||_n(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&_n(t,""+n);break;case"className":yu(t,"class",n);break;case"tabIndex":yu(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":yu(t,l,n);break;case"style":tr(t,n,u);break;case"data":if(e!=="object"){yu(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Su(""+n),t.setAttribute(l,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&xt(t,e,"name",a.name,a,null),xt(t,e,"formEncType",a.formEncType,a,null),xt(t,e,"formMethod",a.formMethod,a,null),xt(t,e,"formTarget",a.formTarget,a,null)):(xt(t,e,"encType",a.encType,a,null),xt(t,e,"method",a.method,a,null),xt(t,e,"target",a.target,a,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Su(""+n),t.setAttribute(l,n);break;case"onClick":n!=null&&(t.onclick=fi);break;case"onScroll":n!=null&&gt("scroll",t);break;case"onScrollEnd":n!=null&&gt("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}l=Su(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""+n):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":n===!0?t.setAttribute(l,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,n):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(l,n):t.removeAttribute(l);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(l):t.setAttribute(l,n);break;case"popover":gt("beforetoggle",t),gt("toggle",t),mu(t,"popover",n);break;case"xlinkActuate":ke(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":ke(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":ke(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":ke(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":ke(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":ke(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":ke(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":ke(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":ke(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":mu(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Uv.get(l)||l,mu(t,l,n))}}function Bs(t,e,l,n,a,u){switch(l){case"style":tr(t,n,u);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"children":typeof n=="string"?_n(t,n):(typeof n=="number"||typeof n=="bigint")&&_n(t,""+n);break;case"onScroll":n!=null&&gt("scroll",t);break;case"onScrollEnd":n!=null&&gt("scrollend",t);break;case"onClick":n!=null&&(t.onclick=fi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Xo.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(a=l.endsWith("Capture"),e=l.slice(2,a?l.length-7:void 0),u=t[ue]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,a),typeof n=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,n,a);break t}l in t?t[l]=n:n===!0?t.setAttribute(l,""):mu(t,l,n)}}}function te(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":gt("error",t),gt("load",t);var n=!1,a=!1,u;for(u in l)if(l.hasOwnProperty(u)){var o=l[u];if(o!=null)switch(u){case"src":n=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:xt(t,e,u,o,l,null)}}a&&xt(t,e,"srcSet",l.srcSet,l,null),n&&xt(t,e,"src",l.src,l,null);return;case"input":gt("invalid",t);var d=u=o=a=null,b=null,D=null;for(n in l)if(l.hasOwnProperty(n)){var N=l[n];if(N!=null)switch(n){case"name":a=N;break;case"type":o=N;break;case"checked":b=N;break;case"defaultChecked":D=N;break;case"value":u=N;break;case"defaultValue":d=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(s(137,e));break;default:xt(t,e,n,N,l,null)}}Po(t,u,d,b,D,o,a,!1),gu(t);return;case"select":gt("invalid",t),n=o=u=null;for(a in l)if(l.hasOwnProperty(a)&&(d=l[a],d!=null))switch(a){case"value":u=d;break;case"defaultValue":o=d;break;case"multiple":n=d;default:xt(t,e,a,d,l,null)}e=u,l=o,t.multiple=!!n,e!=null?Sn(t,!!n,e,!1):l!=null&&Sn(t,!!n,l,!0);return;case"textarea":gt("invalid",t),u=a=n=null;for(o in l)if(l.hasOwnProperty(o)&&(d=l[o],d!=null))switch(o){case"value":n=d;break;case"defaultValue":a=d;break;case"children":u=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(s(91));break;default:xt(t,e,o,d,l,null)}Fo(t,n,a,u),gu(t);return;case"option":for(b in l)if(l.hasOwnProperty(b)&&(n=l[b],n!=null))switch(b){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:xt(t,e,b,n,l,null)}return;case"dialog":gt("beforetoggle",t),gt("toggle",t),gt("cancel",t),gt("close",t);break;case"iframe":case"object":gt("load",t);break;case"video":case"audio":for(n=0;n<Ja.length;n++)gt(Ja[n],t);break;case"image":gt("error",t),gt("load",t);break;case"details":gt("toggle",t);break;case"embed":case"source":case"link":gt("error",t),gt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in l)if(l.hasOwnProperty(D)&&(n=l[D],n!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:xt(t,e,D,n,l,null)}return;default:if(Wi(e)){for(N in l)l.hasOwnProperty(N)&&(n=l[N],n!==void 0&&Bs(t,e,N,n,l,void 0));return}}for(d in l)l.hasOwnProperty(d)&&(n=l[d],n!=null&&xt(t,e,d,n,l,null))}function ny(t,e,l,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,u=null,o=null,d=null,b=null,D=null,N=null;for(U in l){var j=l[U];if(l.hasOwnProperty(U)&&j!=null)switch(U){case"checked":break;case"value":break;case"defaultValue":b=j;default:n.hasOwnProperty(U)||xt(t,e,U,null,n,j)}}for(var C in n){var U=n[C];if(j=l[C],n.hasOwnProperty(C)&&(U!=null||j!=null))switch(C){case"type":u=U;break;case"name":a=U;break;case"checked":D=U;break;case"defaultChecked":N=U;break;case"value":o=U;break;case"defaultValue":d=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(s(137,e));break;default:U!==j&&xt(t,e,C,U,n,j)}}ki(t,o,d,b,D,N,u,a);return;case"select":U=o=d=C=null;for(u in l)if(b=l[u],l.hasOwnProperty(u)&&b!=null)switch(u){case"value":break;case"multiple":U=b;default:n.hasOwnProperty(u)||xt(t,e,u,null,n,b)}for(a in n)if(u=n[a],b=l[a],n.hasOwnProperty(a)&&(u!=null||b!=null))switch(a){case"value":C=u;break;case"defaultValue":d=u;break;case"multiple":o=u;default:u!==b&&xt(t,e,a,u,n,b)}e=d,l=o,n=U,C!=null?Sn(t,!!l,C,!1):!!n!=!!l&&(e!=null?Sn(t,!!l,e,!0):Sn(t,!!l,l?[]:"",!1));return;case"textarea":U=C=null;for(d in l)if(a=l[d],l.hasOwnProperty(d)&&a!=null&&!n.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:xt(t,e,d,null,n,a)}for(o in n)if(a=n[o],u=l[o],n.hasOwnProperty(o)&&(a!=null||u!=null))switch(o){case"value":C=a;break;case"defaultValue":U=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(s(91));break;default:a!==u&&xt(t,e,o,a,n,u)}Wo(t,C,U);return;case"option":for(var rt in l)if(C=l[rt],l.hasOwnProperty(rt)&&C!=null&&!n.hasOwnProperty(rt))switch(rt){case"selected":t.selected=!1;break;default:xt(t,e,rt,null,n,C)}for(b in n)if(C=n[b],U=l[b],n.hasOwnProperty(b)&&C!==U&&(C!=null||U!=null))switch(b){case"selected":t.selected=C&&typeof C!="function"&&typeof C!="symbol";break;default:xt(t,e,b,C,n,U)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ct in l)C=l[ct],l.hasOwnProperty(ct)&&C!=null&&!n.hasOwnProperty(ct)&&xt(t,e,ct,null,n,C);for(D in n)if(C=n[D],U=l[D],n.hasOwnProperty(D)&&C!==U&&(C!=null||U!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(s(137,e));break;default:xt(t,e,D,C,n,U)}return;default:if(Wi(e)){for(var Dt in l)C=l[Dt],l.hasOwnProperty(Dt)&&C!==void 0&&!n.hasOwnProperty(Dt)&&Bs(t,e,Dt,void 0,n,C);for(N in n)C=n[N],U=l[N],!n.hasOwnProperty(N)||C===U||C===void 0&&U===void 0||Bs(t,e,N,C,n,U);return}}for(var M in l)C=l[M],l.hasOwnProperty(M)&&C!=null&&!n.hasOwnProperty(M)&&xt(t,e,M,null,n,C);for(j in n)C=n[j],U=l[j],!n.hasOwnProperty(j)||C===U||C==null&&U==null||xt(t,e,j,C,n,U)}var Hs=null,js=null;function di(t){return t.nodeType===9?t:t.ownerDocument}function th(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function eh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ws(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var qs=null;function ay(){var t=window.event;return t&&t.type==="popstate"?t===qs?!1:(qs=t,!0):(qs=null,!1)}var lh=typeof setTimeout=="function"?setTimeout:void 0,uy=typeof clearTimeout=="function"?clearTimeout:void 0,nh=typeof Promise=="function"?Promise:void 0,iy=typeof queueMicrotask=="function"?queueMicrotask:typeof nh<"u"?function(t){return nh.resolve(null).then(t).catch(cy)}:lh;function cy(t){setTimeout(function(){throw t})}function Ul(t){return t==="head"}function ah(t,e){var l=e,n=0,a=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<n&&8>n){l=n;var o=t.ownerDocument;if(l&1&&ka(o.documentElement),l&2&&ka(o.body),l&4)for(l=o.head,ka(l),o=l.firstChild;o;){var d=o.nextSibling,b=o.nodeName;o[ra]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&o.rel.toLowerCase()==="stylesheet"||l.removeChild(o),o=d}}if(a===0){t.removeChild(u),nu(e);return}a--}else l==="$"||l==="$?"||l==="$!"?a++:n=l.charCodeAt(0)-48;else n=0;l=u}while(l);nu(e)}function Ys(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Ys(l),Zi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function sy(t,e,l,n){for(;t.nodeType===1;){var a=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[ra])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=He(t.nextSibling),t===null)break}return null}function oy(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=He(t.nextSibling),t===null))return null;return t}function Vs(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function ry(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var n=function(){e(),l.removeEventListener("DOMContentLoaded",n)};l.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function He(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Gs=null;function uh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function ih(t,e,l){switch(e=di(l),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function ka(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Zi(t)}var De=new Map,ch=new Set;function hi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var ol=w.d;w.d={f:fy,r:dy,D:hy,C:vy,L:my,m:yy,X:py,S:gy,M:Sy};function fy(){var t=ol.f(),e=ai();return t||e}function dy(t){var e=mn(t);e!==null&&e.tag===5&&e.type==="form"?Of(e):ol.r(t)}var kn=typeof document>"u"?null:document;function sh(t,e,l){var n=kn;if(n&&typeof e=="string"&&e){var a=Ee(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof l=="string"&&(a+='[crossorigin="'+l+'"]'),ch.has(a)||(ch.add(a),t={rel:t,crossOrigin:l,href:e},n.querySelector(a)===null&&(e=n.createElement("link"),te(e,"link",t),Kt(e),n.head.appendChild(e)))}}function hy(t){ol.D(t),sh("dns-prefetch",t,null)}function vy(t,e){ol.C(t,e),sh("preconnect",t,e)}function my(t,e,l){ol.L(t,e,l);var n=kn;if(n&&t&&e){var a='link[rel="preload"][as="'+Ee(e)+'"]';e==="image"&&l&&l.imageSrcSet?(a+='[imagesrcset="'+Ee(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(a+='[imagesizes="'+Ee(l.imageSizes)+'"]')):a+='[href="'+Ee(t)+'"]';var u=a;switch(e){case"style":u=Pn(t);break;case"script":u=Wn(t)}De.has(u)||(t=_({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),De.set(u,t),n.querySelector(a)!==null||e==="style"&&n.querySelector(Pa(u))||e==="script"&&n.querySelector(Wa(u))||(e=n.createElement("link"),te(e,"link",t),Kt(e),n.head.appendChild(e)))}}function yy(t,e){ol.m(t,e);var l=kn;if(l&&t){var n=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+Ee(n)+'"][href="'+Ee(t)+'"]',u=a;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Wn(t)}if(!De.has(u)&&(t=_({rel:"modulepreload",href:t},e),De.set(u,t),l.querySelector(a)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Wa(u)))return}n=l.createElement("link"),te(n,"link",t),Kt(n),l.head.appendChild(n)}}}function gy(t,e,l){ol.S(t,e,l);var n=kn;if(n&&t){var a=yn(n).hoistableStyles,u=Pn(t);e=e||"default";var o=a.get(u);if(!o){var d={loading:0,preload:null};if(o=n.querySelector(Pa(u)))d.loading=5;else{t=_({rel:"stylesheet",href:t,"data-precedence":e},l),(l=De.get(u))&&Xs(t,l);var b=o=n.createElement("link");Kt(b),te(b,"link",t),b._p=new Promise(function(D,N){b.onload=D,b.onerror=N}),b.addEventListener("load",function(){d.loading|=1}),b.addEventListener("error",function(){d.loading|=2}),d.loading|=4,vi(o,e,n)}o={type:"stylesheet",instance:o,count:1,state:d},a.set(u,o)}}}function py(t,e){ol.X(t,e);var l=kn;if(l&&t){var n=yn(l).hoistableScripts,a=Wn(t),u=n.get(a);u||(u=l.querySelector(Wa(a)),u||(t=_({src:t,async:!0},e),(e=De.get(a))&&Qs(t,e),u=l.createElement("script"),Kt(u),te(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},n.set(a,u))}}function Sy(t,e){ol.M(t,e);var l=kn;if(l&&t){var n=yn(l).hoistableScripts,a=Wn(t),u=n.get(a);u||(u=l.querySelector(Wa(a)),u||(t=_({src:t,async:!0,type:"module"},e),(e=De.get(a))&&Qs(t,e),u=l.createElement("script"),Kt(u),te(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},n.set(a,u))}}function oh(t,e,l,n){var a=(a=$.current)?hi(a):null;if(!a)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Pn(l.href),l=yn(a).hoistableStyles,n=l.get(e),n||(n={type:"style",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Pn(l.href);var u=yn(a).hoistableStyles,o=u.get(t);if(o||(a=a.ownerDocument||a,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,o),(u=a.querySelector(Pa(t)))&&!u._p&&(o.instance=u,o.state.loading=5),De.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},De.set(t,l),u||_y(a,t,l,o.state))),e&&n===null)throw Error(s(528,""));return o}if(e&&n!==null)throw Error(s(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Wn(l),l=yn(a).hoistableScripts,n=l.get(e),n||(n={type:"script",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function Pn(t){return'href="'+Ee(t)+'"'}function Pa(t){return'link[rel="stylesheet"]['+t+"]"}function rh(t){return _({},t,{"data-precedence":t.precedence,precedence:null})}function _y(t,e,l,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),te(e,"link",l),Kt(e),t.head.appendChild(e))}function Wn(t){return'[src="'+Ee(t)+'"]'}function Wa(t){return"script[async]"+t}function fh(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+Ee(l.href)+'"]');if(n)return e.instance=n,Kt(n),n;var a=_({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),Kt(n),te(n,"style",a),vi(n,l.precedence,t),e.instance=n;case"stylesheet":a=Pn(l.href);var u=t.querySelector(Pa(a));if(u)return e.state.loading|=4,e.instance=u,Kt(u),u;n=rh(l),(a=De.get(a))&&Xs(n,a),u=(t.ownerDocument||t).createElement("link"),Kt(u);var o=u;return o._p=new Promise(function(d,b){o.onload=d,o.onerror=b}),te(u,"link",n),e.state.loading|=4,vi(u,l.precedence,t),e.instance=u;case"script":return u=Wn(l.src),(a=t.querySelector(Wa(u)))?(e.instance=a,Kt(a),a):(n=l,(a=De.get(u))&&(n=_({},l),Qs(n,a)),t=t.ownerDocument||t,a=t.createElement("script"),Kt(a),te(a,"link",n),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(n=e.instance,e.state.loading|=4,vi(n,l.precedence,t));return e.instance}function vi(t,e,l){for(var n=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=n.length?n[n.length-1]:null,u=a,o=0;o<n.length;o++){var d=n[o];if(d.dataset.precedence===e)u=d;else if(u!==a)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function Xs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Qs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var mi=null;function dh(t,e,l){if(mi===null){var n=new Map,a=mi=new Map;a.set(l,n)}else a=mi,n=a.get(l),n||(n=new Map,a.set(l,n));if(n.has(t))return n;for(n.set(t,null),l=l.getElementsByTagName(t),a=0;a<l.length;a++){var u=l[a];if(!(u[ra]||u[ee]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var o=u.getAttribute(e)||"";o=t+o;var d=n.get(o);d?d.push(u):n.set(o,[u])}}return n}function hh(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function by(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function vh(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Fa=null;function Ey(){}function Ry(t,e,l){if(Fa===null)throw Error(s(475));var n=Fa;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var a=Pn(l.href),u=t.querySelector(Pa(a));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(n.count++,n=yi.bind(n),t.then(n,n)),e.state.loading|=4,e.instance=u,Kt(u);return}u=t.ownerDocument||t,l=rh(l),(a=De.get(a))&&Xs(l,a),u=u.createElement("link"),Kt(u);var o=u;o._p=new Promise(function(d,b){o.onload=d,o.onerror=b}),te(u,"link",l),e.instance=u}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(n.count++,e=yi.bind(n),t.addEventListener("load",e),t.addEventListener("error",e))}}function Ty(){if(Fa===null)throw Error(s(475));var t=Fa;return t.stylesheets&&t.count===0&&Zs(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&Zs(t,t.stylesheets),t.unsuspend){var n=t.unsuspend;t.unsuspend=null,n()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function yi(){if(this.count--,this.count===0){if(this.stylesheets)Zs(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var gi=null;function Zs(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,gi=new Map,e.forEach(My,t),gi=null,yi.call(t))}function My(t,e){if(!(e.state.loading&4)){var l=gi.get(t);if(l)var n=l.get(null);else{l=new Map,gi.set(t,l);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<a.length;u++){var o=a[u];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(l.set(o.dataset.precedence,o),n=o)}n&&l.set(null,n)}a=e.instance,o=a.getAttribute("data-precedence"),u=l.get(o)||n,u===n&&l.set(null,a),l.set(o,a),this.count++,n=yi.bind(this),a.addEventListener("load",n),a.addEventListener("error",n),u?u.parentNode.insertBefore(a,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var Ia={$$typeof:J,Provider:null,Consumer:null,_currentValue:Z,_currentValue2:Z,_threadCount:0};function Ay(t,e,l,n,a,u,o,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Vi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vi(0),this.hiddenUpdates=Vi(null),this.identifierPrefix=n,this.onUncaughtError=a,this.onCaughtError=u,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function mh(t,e,l,n,a,u,o,d,b,D,N,j){return t=new Ay(t,e,l,o,d,b,D,j),e=1,u===!0&&(e|=24),u=he(3,null,null,e),t.current=u,u.stateNode=t,e=Ac(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:n,isDehydrated:l,cache:e},zc(u),t}function yh(t){return t?(t=xn,t):xn}function gh(t,e,l,n,a,u){a=yh(a),n.context===null?n.context=a:n.pendingContext=a,n=Sl(e),n.payload={element:l},u=u===void 0?null:u,u!==null&&(n.callback=u),l=_l(t,n,e),l!==null&&(pe(l,t,e),Da(l,t,e))}function ph(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function Ks(t,e){ph(t,e),(t=t.alternate)&&ph(t,e)}function Sh(t){if(t.tag===13){var e=On(t,67108864);e!==null&&pe(e,t,67108864),Ks(t,67108864)}}var pi=!0;function Oy(t,e,l,n){var a=x.T;x.T=null;var u=w.p;try{w.p=2,Js(t,e,l,n)}finally{w.p=u,x.T=a}}function xy(t,e,l,n){var a=x.T;x.T=null;var u=w.p;try{w.p=8,Js(t,e,l,n)}finally{w.p=u,x.T=a}}function Js(t,e,l,n){if(pi){var a=$s(n);if(a===null)Ls(t,e,n,Si,l),bh(t,n);else if(zy(a,t,e,l,n))n.stopPropagation();else if(bh(t,n),e&4&&-1<Dy.indexOf(t)){for(;a!==null;){var u=mn(a);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var o=Vl(u.pendingLanes);if(o!==0){var d=u;for(d.pendingLanes|=2,d.entangledLanes|=2;o;){var b=1<<31-fe(o);d.entanglements[1]|=b,o&=~b}Ze(u),(Mt&6)===0&&(li=_e()+500,Ka(0))}}break;case 13:d=On(u,2),d!==null&&pe(d,u,2),ai(),Ks(u,2)}if(u=$s(n),u===null&&Ls(t,e,n,Si,l),u===a)break;a=u}a!==null&&n.stopPropagation()}else Ls(t,e,n,null,l)}}function $s(t){return t=Ii(t),ks(t)}var Si=null;function ks(t){if(Si=null,t=vn(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=y(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Si=t,null}function _h(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(dn()){case ql:return 2;case ia:return 8;case Yl:case Ut:return 32;case Wt:return 268435456;default:return 32}default:return 32}}var Ps=!1,Nl=null,Ll=null,Bl=null,tu=new Map,eu=new Map,Hl=[],Dy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bh(t,e){switch(t){case"focusin":case"focusout":Nl=null;break;case"dragenter":case"dragleave":Ll=null;break;case"mouseover":case"mouseout":Bl=null;break;case"pointerover":case"pointerout":tu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":eu.delete(e.pointerId)}}function lu(t,e,l,n,a,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:n,nativeEvent:u,targetContainers:[a]},e!==null&&(e=mn(e),e!==null&&Sh(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function zy(t,e,l,n,a){switch(e){case"focusin":return Nl=lu(Nl,t,e,l,n,a),!0;case"dragenter":return Ll=lu(Ll,t,e,l,n,a),!0;case"mouseover":return Bl=lu(Bl,t,e,l,n,a),!0;case"pointerover":var u=a.pointerId;return tu.set(u,lu(tu.get(u)||null,t,e,l,n,a)),!0;case"gotpointercapture":return u=a.pointerId,eu.set(u,lu(eu.get(u)||null,t,e,l,n,a)),!0}return!1}function Eh(t){var e=vn(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=y(l),e!==null){t.blockedOn=e,Rv(t.priority,function(){if(l.tag===13){var n=ge();n=Gi(n);var a=On(l,n);a!==null&&pe(a,l,n),Ks(l,n)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function _i(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=$s(t.nativeEvent);if(l===null){l=t.nativeEvent;var n=new l.constructor(l.type,l);Fi=n,l.target.dispatchEvent(n),Fi=null}else return e=mn(l),e!==null&&Sh(e),t.blockedOn=l,!1;e.shift()}return!0}function Rh(t,e,l){_i(t)&&l.delete(e)}function Cy(){Ps=!1,Nl!==null&&_i(Nl)&&(Nl=null),Ll!==null&&_i(Ll)&&(Ll=null),Bl!==null&&_i(Bl)&&(Bl=null),tu.forEach(Rh),eu.forEach(Rh)}function bi(t,e){t.blockedOn===e&&(t.blockedOn=null,Ps||(Ps=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Cy)))}var Ei=null;function Th(t){Ei!==t&&(Ei=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Ei===t&&(Ei=null);for(var e=0;e<t.length;e+=3){var l=t[e],n=t[e+1],a=t[e+2];if(typeof n!="function"){if(ks(n||l)===null)continue;break}var u=mn(l);u!==null&&(t.splice(e,3),e-=3,kc(u,{pending:!0,data:a,method:l.method,action:n},n,a))}}))}function nu(t){function e(b){return bi(b,t)}Nl!==null&&bi(Nl,t),Ll!==null&&bi(Ll,t),Bl!==null&&bi(Bl,t),tu.forEach(e),eu.forEach(e);for(var l=0;l<Hl.length;l++){var n=Hl[l];n.blockedOn===t&&(n.blockedOn=null)}for(;0<Hl.length&&(l=Hl[0],l.blockedOn===null);)Eh(l),l.blockedOn===null&&Hl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(n=0;n<l.length;n+=3){var a=l[n],u=l[n+1],o=a[ue]||null;if(typeof u=="function")o||Th(l);else if(o){var d=null;if(u&&u.hasAttribute("formAction")){if(a=u,o=u[ue]||null)d=o.formAction;else if(ks(a)!==null)continue}else d=o.action;typeof d=="function"?l[n+1]=d:(l.splice(n,3),n-=3),Th(l)}}}function Ws(t){this._internalRoot=t}Ri.prototype.render=Ws.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var l=e.current,n=ge();gh(l,n,t,e,null,null)},Ri.prototype.unmount=Ws.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;gh(t.current,2,null,t,null,null),ai(),e[hn]=null}};function Ri(t){this._internalRoot=t}Ri.prototype.unstable_scheduleHydration=function(t){if(t){var e=Yo();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Hl.length&&e!==0&&e<Hl[l].priority;l++);Hl.splice(l,0,t),l===0&&Eh(t)}};var Mh=r.version;if(Mh!=="19.1.0")throw Error(s(527,Mh,"19.1.0"));w.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=m(e),t=t!==null?v(t):null,t=t===null?null:t.stateNode,t};var Uy={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:x,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ti=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ti.isDisabled&&Ti.supportsFiber)try{ca=Ti.inject(Uy),re=Ti}catch{}}return uu.createRoot=function(t,e){if(!f(t))throw Error(s(299));var l=!1,n="",a=Vf,u=Gf,o=Xf,d=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=mh(t,1,!1,null,null,l,n,a,u,o,d,null),t[hn]=e.current,Ns(t),new Ws(e)},uu.hydrateRoot=function(t,e,l){if(!f(t))throw Error(s(299));var n=!1,a="",u=Vf,o=Gf,d=Xf,b=null,D=null;return l!=null&&(l.unstable_strictMode===!0&&(n=!0),l.identifierPrefix!==void 0&&(a=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(o=l.onCaughtError),l.onRecoverableError!==void 0&&(d=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(b=l.unstable_transitionCallbacks),l.formState!==void 0&&(D=l.formState)),e=mh(t,1,!0,e,l??null,n,a,u,o,d,b,D),e.context=yh(null),l=e.current,n=ge(),n=Gi(n),a=Sl(n),a.callback=null,_l(l,a,n),l=n,e.current.lanes=l,oa(e,l),Ze(e),t[hn]=e.current,Ns(t),new Ri(e)},uu.version="19.1.0",uu}var Bh;function Gy(){if(Bh)return to.exports;Bh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),to.exports=Vy(),to.exports}var Xy=Gy(),Qy="Invariant failed";function je(i,r){if(!i)throw new Error(Qy)}const In=new WeakMap,Di=new WeakMap,zi={current:[]};let ao=!1,ou=0;const su=new Set,Mi=new Map;function av(i){const r=Array.from(i).sort((c,s)=>c instanceof ta&&c.options.deps.includes(s)?1:s instanceof ta&&s.options.deps.includes(c)?-1:0);for(const c of r){if(zi.current.includes(c))continue;zi.current.push(c),c.recompute();const s=Di.get(c);if(s)for(const f of s){const h=In.get(f);h&&av(h)}}}function Zy(i){i.listeners.forEach(r=>r({prevVal:i.prevState,currentVal:i.state}))}function Ky(i){i.listeners.forEach(r=>r({prevVal:i.prevState,currentVal:i.state}))}function uv(i){if(ou>0&&!Mi.has(i)&&Mi.set(i,i.prevState),su.add(i),!(ou>0)&&!ao)try{for(ao=!0;su.size>0;){const r=Array.from(su);su.clear();for(const c of r){const s=Mi.get(c)??c.prevState;c.prevState=s,Zy(c)}for(const c of r){const s=In.get(c);s&&(zi.current.push(c),av(s))}for(const c of r){const s=In.get(c);if(s)for(const f of s)Ky(f)}}}finally{ao=!1,zi.current=[],Mi.clear()}}function go(i){ou++;try{i()}finally{if(ou--,ou===0){const r=Array.from(su)[0];r&&uv(r)}}}function Jy(i){return typeof i=="function"}class po{constructor(r,c){this.listeners=new Set,this.subscribe=s=>{var f,h;this.listeners.add(s);const y=(h=(f=this.options)==null?void 0:f.onSubscribe)==null?void 0:h.call(f,s,this);return()=>{this.listeners.delete(s),y?.()}},this.prevState=r,this.state=r,this.options=c}setState(r){var c,s,f;this.prevState=this.state,(c=this.options)!=null&&c.updateFn?this.state=this.options.updateFn(this.prevState)(r):Jy(r)?this.state=r(this.prevState):this.state=r,(f=(s=this.options)==null?void 0:s.onUpdate)==null||f.call(s),uv(this)}}class ta{constructor(r){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{const c=[],s=[];for(const f of this.options.deps)c.push(f.prevState),s.push(f.state);return this.lastSeenDepValues=s,{prevDepVals:c,currDepVals:s,prevVal:this.prevState??void 0}},this.recompute=()=>{var c,s;this.prevState=this.state;const{prevDepVals:f,currDepVals:h,prevVal:y}=this.getDepVals();this.state=this.options.fn({prevDepVals:f,currDepVals:h,prevVal:y}),(s=(c=this.options).onUpdate)==null||s.call(c)},this.checkIfRecalculationNeededDeeply=()=>{for(const h of this.options.deps)h instanceof ta&&h.checkIfRecalculationNeededDeeply();let c=!1;const s=this.lastSeenDepValues,{currDepVals:f}=this.getDepVals();for(let h=0;h<f.length;h++)if(f[h]!==s[h]){c=!0;break}c&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const c of this._subscriptions)c()}),this.subscribe=c=>{var s,f;this.listeners.add(c);const h=(f=(s=this.options).onSubscribe)==null?void 0:f.call(s,c,this);return()=>{this.listeners.delete(c),h?.()}},this.options=r,this.state=r.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(r=this.options.deps){for(const c of r)if(c instanceof ta)c.registerOnGraph(),this.registerOnGraph(c.options.deps);else if(c instanceof po){let s=In.get(c);s||(s=new Set,In.set(c,s)),s.add(this);let f=Di.get(this);f||(f=new Set,Di.set(this,f)),f.add(c)}}unregisterFromGraph(r=this.options.deps){for(const c of r)if(c instanceof ta)this.unregisterFromGraph(c.options.deps);else if(c instanceof po){const s=In.get(c);s&&s.delete(this);const f=Di.get(this);f&&f.delete(c)}}}function So(i){return i[i.length-1]}function $y(i){return typeof i=="function"}function cn(i,r){return $y(i)?i(r):i}function Ci(i,r){return r.reduce((c,s)=>(c[s]=i[s],c),{})}function ze(i,r){if(i===r)return i;const c=r,s=wh(i)&&wh(c);if(s||Hh(i)&&Hh(c)){const f=s?i:Object.keys(i).concat(Object.getOwnPropertySymbols(i)),h=f.length,y=s?c:Object.keys(c).concat(Object.getOwnPropertySymbols(c)),g=y.length,m=s?[]:{};let v=0;for(let _=0;_<g;_++){const p=s?_:y[_];(!s&&f.includes(p)||s)&&i[p]===void 0&&c[p]===void 0?(m[p]=void 0,v++):(m[p]=ze(i[p],c[p]),m[p]===i[p]&&i[p]!==void 0&&v++)}return h===g&&v===h?i:m}return c}function Hh(i){return _o(i)&&Object.getOwnPropertyNames(i).length===Object.keys(i).length}function _o(i){if(!jh(i))return!1;const r=i.constructor;if(typeof r>"u")return!0;const c=r.prototype;return!(!jh(c)||!c.hasOwnProperty("isPrototypeOf"))}function jh(i){return Object.prototype.toString.call(i)==="[object Object]"}function wh(i){return Array.isArray(i)&&i.length===Object.keys(i).length}function qh(i,r){let c=Object.keys(i);return r&&(c=c.filter(s=>i[s]!==void 0)),c}function ea(i,r,c){if(i===r)return!0;if(typeof i!=typeof r)return!1;if(_o(i)&&_o(r)){const s=c?.ignoreUndefined??!0,f=qh(i,s),h=qh(r,s);return!c?.partial&&f.length!==h.length?!1:h.every(y=>ea(i[y],r[y],c))}return Array.isArray(i)&&Array.isArray(r)?i.length!==r.length?!1:!i.some((s,f)=>!ea(s,r[f],c)):!1}function un(i){let r,c;const s=new Promise((f,h)=>{r=f,c=h});return s.status="pending",s.resolve=f=>{s.status="resolved",s.value=f,r(f),i?.(f)},s.reject=f=>{s.status="rejected",c(f)},s}function ky(i){return typeof i?.message!="string"?!1:i.message.startsWith("Failed to fetch dynamically imported module")||i.message.startsWith("error loading dynamically imported module")||i.message.startsWith("Importing a module script failed")}function Py(i){return{id:i.i,__beforeLoadContext:i.b,loaderData:i.l,status:i.s,ssr:i.ssr,updatedAt:i.u,error:i.e}}async function Wy(i){var r,c,s;je((r=window.$_TSR)==null?void 0:r.router);const{manifest:f,dehydratedData:h,lastMatchId:y}=window.$_TSR.router;i.ssr={manifest:f};const g=i.matchRoutes(i.state.location),m=Promise.all(g.map(R=>{const z=i.looseRoutesById[R.routeId];return i.loadRouteChunk(z)}));function v(R){const H=i.looseRoutesById[R.routeId].options.pendingMinMs??i.options.defaultPendingMinMs;if(H){const q=un();R.minPendingPromise=q,R._forcePending=!0,setTimeout(()=>{q.resolve(),i.updateMatch(R.id,lt=>({...lt,minPendingPromise:void 0,_forcePending:void 0}))},H)}}let _;g.forEach(R=>{const z=window.$_TSR.router.matches.find(H=>H.i===R.id);if(!z){Object.assign(R,{dehydrated:!1,ssr:!1});return}Object.assign(R,Py(z)),R.ssr===!1?R._dehydrated=!1:R._dehydrated=!0,(R.ssr==="data-only"||R.ssr===!1)&&_===void 0&&(_=R.index,v(R))}),i.__store.setState(R=>({...R,matches:g})),await((s=(c=i.options).hydrate)==null?void 0:s.call(c,h)),await Promise.all(i.state.matches.map(async R=>{var z,H,q,lt,J,k;const K=i.looseRoutesById[R.routeId],et=i.state.matches[R.index-1],G=et?.context??i.options.context??{},tt={deps:R.loaderDeps,params:R.params,context:G,location:i.state.location,navigate:F=>i.navigate({...F,_fromLocation:i.state.location}),buildLocation:i.buildLocation,cause:R.cause,abortController:R.abortController,preload:!1,matches:g};R.__routeContext=((H=(z=K.options).context)==null?void 0:H.call(z,tt))??{},R.context={...G,...R.__routeContext,...R.__beforeLoadContext};const it={matches:i.state.matches,match:R,params:R.params,loaderData:R.loaderData},Y=await((lt=(q=K.options).head)==null?void 0:lt.call(q,it)),ut=await((k=(J=K.options).scripts)==null?void 0:k.call(J,it));R.meta=Y?.meta,R.links=Y?.links,R.headScripts=Y?.scripts,R.styles=Y?.styles,R.scripts=ut}));const p=g[g.length-1].id!==y;if(!g.some(R=>R.ssr===!1)&&!p)return g.forEach(R=>{R._dehydrated=void 0}),m;const A=Promise.resolve().then(()=>i.load()).catch(R=>{console.error("Error during router hydration:",R)});if(p){const R=g[1];je(R),v(R),R._displayPending=!0,R.displayPendingPromise=A,A.then(()=>{go(()=>{i.__store.state.status==="pending"&&i.__store.setState(z=>({...z,status:"idle",resolvedLocation:z.location})),i.updateMatch(R.id,z=>({...z,_displayPending:void 0,displayPendingPromise:void 0}))})})}return m}const wl="__TSR_index",Yh="popstate",Vh="beforeunload";function iv(i){let r=i.getLocation();const c=new Set,s=y=>{r=i.getLocation(),c.forEach(g=>g({location:r,action:y}))},f=y=>{i.notifyOnIndexChange??!0?s(y):r=i.getLocation()},h=async({task:y,navigateOpts:g,...m})=>{var v,_;if(g?.ignoreBlocker??!1){y();return}const S=((v=i.getBlockers)==null?void 0:v.call(i))??[],A=m.type==="PUSH"||m.type==="REPLACE";if(typeof document<"u"&&S.length&&A)for(const R of S){const z=ru(m.path,m.state);if(await R.blockerFn({currentLocation:r,nextLocation:z,action:m.type})){(_=i.onBlocked)==null||_.call(i);return}}y()};return{get location(){return r},get length(){return i.getLength()},subscribers:c,subscribe:y=>(c.add(y),()=>{c.delete(y)}),push:(y,g,m)=>{const v=r.state[wl];g=bo(v+1,g),h({task:()=>{i.pushState(y,g),s({type:"PUSH"})},navigateOpts:m,type:"PUSH",path:y,state:g})},replace:(y,g,m)=>{const v=r.state[wl];g=bo(v,g),h({task:()=>{i.replaceState(y,g),s({type:"REPLACE"})},navigateOpts:m,type:"REPLACE",path:y,state:g})},go:(y,g)=>{h({task:()=>{i.go(y),f({type:"GO",index:y})},navigateOpts:g,type:"GO"})},back:y=>{h({task:()=>{i.back(y?.ignoreBlocker??!1),f({type:"BACK"})},navigateOpts:y,type:"BACK"})},forward:y=>{h({task:()=>{i.forward(y?.ignoreBlocker??!1),f({type:"FORWARD"})},navigateOpts:y,type:"FORWARD"})},canGoBack:()=>r.state[wl]!==0,createHref:y=>i.createHref(y),block:y=>{var g;if(!i.setBlockers)return()=>{};const m=((g=i.getBlockers)==null?void 0:g.call(i))??[];return i.setBlockers([...m,y]),()=>{var v,_;const p=((v=i.getBlockers)==null?void 0:v.call(i))??[];(_=i.setBlockers)==null||_.call(i,p.filter(S=>S!==y))}},flush:()=>{var y;return(y=i.flush)==null?void 0:y.call(i)},destroy:()=>{var y;return(y=i.destroy)==null?void 0:y.call(i)},notify:s}}function bo(i,r){r||(r={});const c=Mo();return{...r,key:c,__TSR_key:c,[wl]:i}}function Fy(i){var r,c;const s=typeof document<"u"?window:void 0,f=s.history.pushState,h=s.history.replaceState;let y=[];const g=()=>y,m=Y=>y=Y,v=Y=>Y,_=()=>ru(`${s.location.pathname}${s.location.search}${s.location.hash}`,s.history.state);if(!((r=s.history.state)!=null&&r.__TSR_key)&&!((c=s.history.state)!=null&&c.key)){const Y=Mo();s.history.replaceState({[wl]:0,key:Y,__TSR_key:Y},"")}let p=_(),S,A=!1,R=!1,z=!1,H=!1;const q=()=>p;let lt,J;const k=()=>{lt&&(it._ignoreSubscribers=!0,(lt.isPush?s.history.pushState:s.history.replaceState)(lt.state,"",lt.href),it._ignoreSubscribers=!1,lt=void 0,J=void 0,S=void 0)},K=(Y,ut,F)=>{const mt=v(ut);J||(S=p),p=ru(ut,F),lt={href:mt,state:F,isPush:lt?.isPush||Y==="push"},J||(J=Promise.resolve().then(()=>k()))},et=Y=>{p=_(),it.notify({type:Y})},G=async()=>{if(R){R=!1;return}const Y=_(),ut=Y.state[wl]-p.state[wl],F=ut===1,mt=ut===-1,bt=!F&&!mt||A;A=!1;const pt=bt?"GO":mt?"BACK":"FORWARD",x=bt?{type:"GO",index:ut}:{type:mt?"BACK":"FORWARD"};if(z)z=!1;else{const w=g();if(typeof document<"u"&&w.length){for(const Z of w)if(await Z.blockerFn({currentLocation:p,nextLocation:Y,action:pt})){R=!0,s.history.go(1),it.notify(x);return}}}p=_(),it.notify(x)},tt=Y=>{if(H){H=!1;return}let ut=!1;const F=g();if(typeof document<"u"&&F.length)for(const mt of F){const bt=mt.enableBeforeUnload??!0;if(bt===!0){ut=!0;break}if(typeof bt=="function"&&bt()===!0){ut=!0;break}}if(ut)return Y.preventDefault(),Y.returnValue=""},it=iv({getLocation:q,getLength:()=>s.history.length,pushState:(Y,ut)=>K("push",Y,ut),replaceState:(Y,ut)=>K("replace",Y,ut),back:Y=>(Y&&(z=!0),H=!0,s.history.back()),forward:Y=>{Y&&(z=!0),H=!0,s.history.forward()},go:Y=>{A=!0,s.history.go(Y)},createHref:Y=>v(Y),flush:k,destroy:()=>{s.history.pushState=f,s.history.replaceState=h,s.removeEventListener(Vh,tt,{capture:!0}),s.removeEventListener(Yh,G)},onBlocked:()=>{S&&p!==S&&(p=S)},getBlockers:g,setBlockers:m,notifyOnIndexChange:!1});return s.addEventListener(Vh,tt,{capture:!0}),s.addEventListener(Yh,G),s.history.pushState=function(...Y){const ut=f.apply(s.history,Y);return it._ignoreSubscribers||et("PUSH"),ut},s.history.replaceState=function(...Y){const ut=h.apply(s.history,Y);return it._ignoreSubscribers||et("REPLACE"),ut},it}function Iy(i={initialEntries:["/"]}){const r=i.initialEntries;let c=i.initialIndex?Math.min(Math.max(i.initialIndex,0),r.length-1):r.length-1;const s=r.map((h,y)=>bo(y,void 0));return iv({getLocation:()=>ru(r[c],s[c]),getLength:()=>r.length,pushState:(h,y)=>{c<r.length-1&&(r.splice(c+1),s.splice(c+1)),s.push(y),r.push(h),c=Math.max(r.length-1,0)},replaceState:(h,y)=>{s[c]=y,r[c]=h},back:()=>{c=Math.max(c-1,0)},forward:()=>{c=Math.min(c+1,r.length-1)},go:h=>{c=Math.min(Math.max(c+h,0),r.length-1)},createHref:h=>h})}function ru(i,r){const c=i.indexOf("#"),s=i.indexOf("?"),f=Mo();return{href:i,pathname:i.substring(0,c>0?s>0?Math.min(c,s):c:s>0?s:i.length),hash:c>-1?i.substring(c):"",search:s>-1?i.slice(s,c===-1?void 0:c):"",state:r||{[wl]:0,key:f,__TSR_key:f}}}function Mo(){return(Math.random()+1).toString(36).substring(7)}const rl=0,on=1,rn=2,na=3;function fl(i){return Ao(i.filter(r=>r!==void 0).join("/"))}function Ao(i){return i.replace(/\/{2,}/g,"/")}function Oo(i){return i==="/"?i:i.replace(/^\/{1,}/,"")}function aa(i){return i==="/"?i:i.replace(/\/{1,}$/,"")}function uo(i){return aa(Oo(i))}function Ui(i,r){return i?.endsWith("/")&&i!=="/"&&i!==`${r}/`?i.slice(0,-1):i}function t0(i,r,c){return Ui(i,c)===Ui(r,c)}function e0(i){const{type:r,value:c}=i;if(r===rl)return c;const{prefixSegment:s,suffixSegment:f}=i;if(r===on){const h=c.substring(1);if(s&&f)return`${s}{$${h}}${f}`;if(s)return`${s}{$${h}}`;if(f)return`{$${h}}${f}`}if(r===na){const h=c.substring(1);return s&&f?`${s}{-$${h}}${f}`:s?`${s}{-$${h}}`:f?`{-$${h}}${f}`:`{-$${h}}`}if(r===rn){if(s&&f)return`${s}{$}${f}`;if(s)return`${s}{$}`;if(f)return`{$}${f}`}return c}function l0({basepath:i,base:r,to:c,trailingSlash:s="never",caseSensitive:f,parseCache:h}){var y;r=Ni(i,r,f),c=Ni(i,c,f);let g=ua(r,h).slice();const m=ua(c,h);g.length>1&&((y=So(g))==null?void 0:y.value)==="/"&&g.pop();for(let p=0,S=m.length;p<S;p++){const A=m[p],R=A.value;R==="/"?p?p===S-1&&g.push(A):g=[A]:R===".."?g.pop():R==="."||g.push(A)}g.length>1&&(So(g).value==="/"?s==="never"&&g.pop():s==="always"&&g.push({type:rl,value:"/"}));const v=g.map(e0);return fl([i,...v])}const ua=(i,r)=>{if(!i)return[];const c=r?.get(i);if(c)return c;const s=s0(i);return r?.set(i,s),s},n0=/^\$.{1,}$/,a0=/^(.*?)\{(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,u0=/^(.*?)\{-(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,i0=/^\$$/,c0=/^(.*?)\{\$\}(.*)$/;function s0(i){i=Ao(i);const r=[];if(i.slice(0,1)==="/"&&(i=i.substring(1),r.push({type:rl,value:"/"})),!i)return r;const c=i.split("/").filter(Boolean);return r.push(...c.map(s=>{const f=s.match(c0);if(f){const g=f[1],m=f[2];return{type:rn,value:"$",prefixSegment:g||void 0,suffixSegment:m||void 0}}const h=s.match(u0);if(h){const g=h[1],m=h[2],v=h[3];return{type:na,value:m,prefixSegment:g||void 0,suffixSegment:v||void 0}}const y=s.match(a0);if(y){const g=y[1],m=y[2],v=y[3];return{type:on,value:""+m,prefixSegment:g||void 0,suffixSegment:v||void 0}}if(n0.test(s)){const g=s.substring(1);return{type:on,value:"$"+g,prefixSegment:void 0,suffixSegment:void 0}}return i0.test(s)?{type:rn,value:"$",prefixSegment:void 0,suffixSegment:void 0}:{type:rl,value:s.includes("%25")?s.split("%25").map(g=>decodeURI(g)).join("%25"):decodeURI(s)}})),i.slice(-1)==="/"&&(i=i.substring(1),r.push({type:rl,value:"/"})),r}function Ai({path:i,params:r,leaveWildcards:c,leaveParams:s,decodeCharMap:f,parseCache:h}){const y=ua(i,h);function g(p){const S=r[p],A=typeof S=="string";return p==="*"||p==="_splat"?A?encodeURI(S):S:A?o0(S,f):S}let m=!1;const v={},_=fl(y.map(p=>{if(p.type===rl)return p.value;if(p.type===rn){v._splat=r._splat;const S=p.prefixSegment||"",A=p.suffixSegment||"";if(!("_splat"in r))return m=!0,c?`${S}${p.value}${A}`:S||A?`${S}${A}`:void 0;const R=g("_splat");return c?`${S}${p.value}${R??""}${A}`:`${S}${R}${A}`}if(p.type===on){const S=p.value.substring(1);!m&&!(S in r)&&(m=!0),v[S]=r[S];const A=p.prefixSegment||"",R=p.suffixSegment||"";if(s){const z=g(p.value);return`${A}${p.value}${z??""}${R}`}return`${A}${g(S)??"undefined"}${R}`}if(p.type===na){const S=p.value.substring(1),A=p.prefixSegment||"",R=p.suffixSegment||"";if(!(S in r)||r[S]==null)return c?`${A}${S}${R}`:A||R?`${A}${R}`:void 0;if(v[S]=r[S],s){const z=g(p.value);return`${A}${p.value}${z??""}${R}`}return c?`${A}${S}${g(S)??""}${R}`:`${A}${g(S)??""}${R}`}return p.value}));return{usedParams:v,interpolatedPath:_,isMissingParams:m}}function o0(i,r){let c=encodeURIComponent(i);if(r)for(const[s,f]of r)c=c.replaceAll(s,f);return c}function Eo(i,r,c,s){const f=r0(i,r,c,s);if(!(c.to&&!f))return f??{}}function Ni(i,r,c=!1){const s=c?i:i.toLowerCase(),f=c?r:r.toLowerCase();switch(!0){case s==="/":return r;case f===s:return"";case r.length<i.length:return r;case f[s.length]!=="/":return r;case f.startsWith(s):return r.slice(i.length);default:return r}}function r0(i,r,{to:c,fuzzy:s,caseSensitive:f},h){if(i!=="/"&&!r.startsWith(i))return;r=Ni(i,r,f),c=Ni(i,`${c??"$"}`,f);const y=ua(r.startsWith("/")?r:`/${r}`,h),g=ua(c.startsWith("/")?c:`/${c}`,h),m={};return f0(y,g,m,s,f)?m:void 0}function f0(i,r,c,s,f){var h,y,g;let m=0,v=0;for(;m<i.length||v<r.length;){const _=i[m],p=r[v];if(p){if(p.type===rn){const S=i.slice(m);let A;if(p.prefixSegment||p.suffixSegment){if(!_)return!1;const R=p.prefixSegment||"",z=p.suffixSegment||"",H=_.value;if("prefixSegment"in p&&!H.startsWith(R)||"suffixSegment"in p&&!((h=i[i.length-1])!=null&&h.value.endsWith(z)))return!1;let q=decodeURI(fl(S.map(lt=>lt.value)));R&&q.startsWith(R)&&(q=q.slice(R.length)),z&&q.endsWith(z)&&(q=q.slice(0,q.length-z.length)),A=q}else A=decodeURI(fl(S.map(R=>R.value)));return c["*"]=A,c._splat=A,!0}if(p.type===rl){if(p.value==="/"&&!_?.value){v++;continue}if(_){if(f){if(p.value!==_.value)return!1}else if(p.value.toLowerCase()!==_.value.toLowerCase())return!1;m++,v++;continue}else return!1}if(p.type===on){if(!_||_.value==="/")return!1;let S="",A=!1;if(p.prefixSegment||p.suffixSegment){const R=p.prefixSegment||"",z=p.suffixSegment||"",H=_.value;if(R&&!H.startsWith(R)||z&&!H.endsWith(z))return!1;let q=H;R&&q.startsWith(R)&&(q=q.slice(R.length)),z&&q.endsWith(z)&&(q=q.slice(0,q.length-z.length)),S=decodeURIComponent(q),A=!0}else S=decodeURIComponent(_.value),A=!0;A&&(c[p.value.substring(1)]=S,m++),v++;continue}if(p.type===na){if(!_){v++;continue}if(_.value==="/"){v++;continue}let S="",A=!1;if(p.prefixSegment||p.suffixSegment){const R=p.prefixSegment||"",z=p.suffixSegment||"",H=_.value;if((!R||H.startsWith(R))&&(!z||H.endsWith(z))){let q=H;R&&q.startsWith(R)&&(q=q.slice(R.length)),z&&q.endsWith(z)&&(q=q.slice(0,q.length-z.length)),S=decodeURIComponent(q),A=!0}}else{let R=!0;for(let z=v+1;z<r.length;z++){const H=r[z];if(H?.type===rl&&H.value===_.value){R=!1;break}if(H?.type===on||H?.type===rn)break}R&&(S=decodeURIComponent(_.value),A=!0)}A&&(c[p.value.substring(1)]=S,m++),v++;continue}}if(m<i.length&&v>=r.length)return c["**"]=fl(i.slice(m).map(S=>S.value)),!!s&&((y=r[r.length-1])==null?void 0:y.value)!=="/";if(v<r.length&&m>=i.length){for(let S=v;S<r.length;S++)if(((g=r[S])==null?void 0:g.type)!==na)return!1;break}break}return!0}function Ce(i){return!!i?.isNotFound}function d0(){try{if(typeof window<"u"&&typeof window.sessionStorage=="object")return window.sessionStorage}catch{return}}const Li="tsr-scroll-restoration-v1_3",h0=(i,r)=>{let c;return(...s)=>{c||(c=setTimeout(()=>{i(...s),c=null},r))}};function v0(){const i=d0();if(!i)return;const r=i.getItem(Li);let c=r?JSON.parse(r):{};return{state:c,set:s=>(c=cn(s,c)||c,i.setItem(Li,JSON.stringify(c)))}}const io=v0(),Ro=i=>i.state.__TSR_key||i.href;function m0(i){const r=[];let c;for(;c=i.parentNode;)r.unshift(`${i.tagName}:nth-child(${[].indexOf.call(c.children,i)+1})`),i=c;return`${r.join(" > ")}`.toLowerCase()}let Bi=!1;function cv(i,r,c,s,f){var h;let y;try{y=JSON.parse(sessionStorage.getItem(i)||"{}")}catch(v){console.error(v);return}const g=r||((h=window.history.state)==null?void 0:h.key),m=y[g];Bi=!0,(()=>{if(s&&m){for(const _ in m){const p=m[_];if(_==="window")window.scrollTo({top:p.scrollY,left:p.scrollX,behavior:c});else if(_){const S=document.querySelector(_);S&&(S.scrollLeft=p.scrollX,S.scrollTop=p.scrollY)}}return}const v=window.location.hash.split("#")[1];if(v){const _=(window.history.state||{}).__hashScrollIntoViewOptions??!0;if(_){const p=document.getElementById(v);p&&p.scrollIntoView(_)}return}["window",...f?.filter(_=>_!=="window")??[]].forEach(_=>{const p=_==="window"?window:typeof _=="function"?_():document.querySelector(_);p&&p.scrollTo({top:0,left:0,behavior:c})})})(),Bi=!1}function y0(i,r){if(io===void 0||((i.options.scrollRestoration??!1)&&(i.isScrollRestoring=!0),typeof document>"u"||i.isScrollRestorationSetup))return;i.isScrollRestorationSetup=!0,Bi=!1;const s=i.options.getScrollRestorationKey||Ro;window.history.scrollRestoration="manual";const f=h=>{if(Bi||!i.isScrollRestoring)return;let y="";if(h.target===document||h.target===window)y="window";else{const m=h.target.getAttribute("data-scroll-restoration-id");m?y=`[data-scroll-restoration-id="${m}"]`:y=m0(h.target)}const g=s(i.state.location);io.set(m=>{const v=m[g]=m[g]||{},_=v[y]=v[y]||{};if(y==="window")_.scrollX=window.scrollX||0,_.scrollY=window.scrollY||0;else if(y){const p=document.querySelector(y);p&&(_.scrollX=p.scrollLeft||0,_.scrollY=p.scrollTop||0)}return m})};typeof document<"u"&&document.addEventListener("scroll",h0(f,100),!0),i.subscribe("onRendered",h=>{const y=s(h.toLocation);if(!i.resetNextScroll){i.resetNextScroll=!0;return}cv(Li,y,i.options.scrollRestorationBehavior||void 0,i.isScrollRestoring||void 0,i.options.scrollToTopSelectors||void 0),i.isScrollRestoring&&io.set(g=>(g[y]=g[y]||{},g))})}function g0(i){if(typeof document<"u"&&document.querySelector){const r=i.state.location.state.__hashScrollIntoViewOptions??!0;if(r&&i.state.location.hash!==""){const c=document.getElementById(i.state.location.hash);c&&c.scrollIntoView(r)}}}function p0(i,r){const c=Object.entries(i).flatMap(([f,h])=>Array.isArray(h)?h.map(y=>[f,String(y)]):[[f,String(h)]]);return""+new URLSearchParams(c).toString()}function co(i){return i?i==="false"?!1:i==="true"?!0:+i*0===0&&+i+""===i?+i:i:""}function S0(i,r){const c=i;return[...new URLSearchParams(c).entries()].reduce((h,[y,g])=>{const m=h[y];return m==null?h[y]=co(g):h[y]=Array.isArray(m)?[...m,co(g)]:[m,co(g)],h},{})}const _0=E0(JSON.parse),b0=R0(JSON.stringify,JSON.parse);function E0(i){return r=>{r.substring(0,1)==="?"&&(r=r.substring(1));const c=S0(r);for(const s in c){const f=c[s];if(typeof f=="string")try{c[s]=i(f)}catch{}}return c}}function R0(i,r){function c(s){if(typeof s=="object"&&s!==null)try{return i(s)}catch{}else if(typeof s=="string"&&typeof r=="function")try{return r(s),i(s)}catch{}return s}return s=>{s={...s},Object.keys(s).forEach(h=>{const y=s[h];typeof y>"u"||y===void 0?delete s[h]:s[h]=c(y)});const f=p0(s).toString();return f?`?${f}`:""}}const Ue="__root__";function T0(i){if(i.statusCode=i.statusCode||i.code||307,!i.reloadDocument)try{new URL(`${i.href}`),i.reloadDocument=!0}catch{}const r=new Headers(i.headers||{});i.href&&r.get("Location")===null&&r.set("Location",i.href);const c=new Response(null,{status:i.statusCode,headers:r});if(c.options=i,i.throw)throw c;return c}function Ke(i){return i instanceof Response&&!!i.options}function M0(i){const r=new Map;let c,s;const f=h=>{h.next&&(h.prev?(h.prev.next=h.next,h.next.prev=h.prev,h.next=void 0,s&&(s.next=h,h.prev=s)):(h.next.prev=void 0,c=h.next,h.next=void 0,s&&(h.prev=s,s.next=h)),s=h)};return{get(h){const y=r.get(h);if(y)return f(y),y.value},set(h,y){if(r.size>=i&&c){const m=c;r.delete(m.key),m.next&&(c=m.next,m.next.prev=void 0),m===s&&(s=void 0)}const g=r.get(h);if(g)g.value=y,f(g);else{const m={key:h,value:y,prev:s};s&&(s.next=m),s=m,c||(c=m),r.set(h,m)}}}}function A0(i){return i instanceof Error?{name:i.name,message:i.message}:{data:i}}function sn(i){const r=i.resolvedLocation,c=i.location,s=r?.pathname!==c.pathname,f=r?.href!==c.href,h=r?.hash!==c.hash;return{fromLocation:r,toLocation:c,pathChanged:s,hrefChanged:f,hashChanged:h}}class O0{constructor(r){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=c=>c(),this.update=c=>{var s;c.notFoundRoute&&console.warn("The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.");const f=this.options;this.options={...this.options,...c},this.isServer=this.options.isServer??typeof document>"u",this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(h=>[encodeURIComponent(h),h])):void 0,(!this.basepath||c.basepath&&c.basepath!==f.basepath)&&(c.basepath===void 0||c.basepath===""||c.basepath==="/"?this.basepath="/":this.basepath=`/${uo(c.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?Iy({initialEntries:[this.basepath||"/"]}):Fy()),this.latestLocation=this.parseLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new po(D0(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(h=>!["redirected"].includes(h.status))}}}),y0(this)),typeof window<"u"&&"CSS"in window&&typeof((s=window.CSS)==null?void 0:s.supports)=="function"&&(this.isViewTransitionTypesSupported=window.CSS.supports("selector(:active-view-transition-type(a)"))},this.buildRouteTree=()=>{const{routesById:c,routesByPath:s,flatRoutes:f}=N0({routeTree:this.routeTree,initRoute:(y,g)=>{y.init({originalIndex:g})}});this.routesById=c,this.routesByPath=s,this.flatRoutes=f;const h=this.options.notFoundRoute;h&&(h.init({originalIndex:99999999999}),this.routesById[h.id]=h)},this.subscribe=(c,s)=>{const f={eventType:c,fn:s};return this.subscribers.add(f),()=>{this.subscribers.delete(f)}},this.emit=c=>{this.subscribers.forEach(s=>{s.eventType===c.type&&s.fn(c)})},this.parseLocation=(c,s)=>{const f=({pathname:m,search:v,hash:_,state:p})=>{const S=this.options.parseSearch(v),A=this.options.stringifySearch(S);return{pathname:m,searchStr:A,search:ze(c?.search,S),hash:_.split("#").reverse()[0]??"",href:`${m}${A}${_}`,state:ze(c?.state,p)}},h=f(s??this.history.location),{__tempLocation:y,__tempKey:g}=h.state;if(y&&(!g||g===this.tempLocationKey)){const m=f(y);return m.state.key=h.state.key,m.state.__TSR_key=h.state.__TSR_key,delete m.state.__tempLocation,{...m,maskedLocation:h}}return h},this.resolvePathWithBase=(c,s)=>l0({basepath:this.basepath,base:c,to:Ao(s),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive,parseCache:this.parsePathnameCache}),this.matchRoutes=(c,s,f)=>typeof c=="string"?this.matchRoutesInternal({pathname:c,search:s},f):this.matchRoutesInternal(c,s),this.parsePathnameCache=M0(1e3),this.getMatchedRoutes=(c,s)=>L0({pathname:c,routePathname:s,basepath:this.basepath,caseSensitive:this.options.caseSensitive,routesByPath:this.routesByPath,routesById:this.routesById,flatRoutes:this.flatRoutes,parseCache:this.parsePathnameCache}),this.cancelMatch=c=>{const s=this.getMatch(c);s&&(s.abortController.abort(),this.updateMatch(c,f=>(clearTimeout(f.pendingTimeout),{...f,pendingTimeout:void 0})))},this.cancelMatches=()=>{var c;(c=this.state.pendingMatches)==null||c.forEach(s=>{this.cancelMatch(s.id)})},this.buildLocation=c=>{const s=(h={})=>{var y;const g=h._fromLocation||this.latestLocation,m=this.matchRoutes(g,{_buildLocation:!0}),v=So(m);let _=v.fullPath;const p=h.to?this.resolvePathWithBase(_,`${h.to}`):this.resolvePathWithBase(_,"."),S=!!h.to&&!Xh(h.to.toString(),_)&&!Xh(p,_);h.unsafeRelative==="path"?_=g.pathname:S&&h.from&&(_=h.from);const A=v.search,R={...v.params},z=h.to?this.resolvePathWithBase(_,`${h.to}`):this.resolvePathWithBase(_,".");let H=h.params===!1||h.params===null?{}:(h.params??!0)===!0?R:{...R,...cn(h.params,R)};const q=Ai({path:z,params:H??{},parseCache:this.parsePathnameCache}).interpolatedPath,lt=this.matchRoutes(q,{},{_buildLocation:!0}).map(it=>this.looseRoutesById[it.routeId]);Object.keys(H).length>0&&lt.map(it=>{var Y;return((Y=it.options.params)==null?void 0:Y.stringify)??it.options.stringifyParams}).filter(Boolean).forEach(it=>{H={...H,...it(H)}});const J=Ai({path:z,params:H??{},leaveWildcards:!1,leaveParams:c.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap,parseCache:this.parsePathnameCache}).interpolatedPath;let k=A;if(c._includeValidateSearch&&((y=this.options.search)!=null&&y.strict)){let it={};lt.forEach(Y=>{try{Y.options.validateSearch&&(it={...it,...To(Y.options.validateSearch,{...it,...k})??{}})}catch{}}),k=it}k=B0({search:k,dest:h,destRoutes:lt,_includeValidateSearch:c._includeValidateSearch}),k=ze(A,k);const K=this.options.stringifySearch(k),et=h.hash===!0?g.hash:h.hash?cn(h.hash,g.hash):void 0,G=et?`#${et}`:"";let tt=h.state===!0?g.state:h.state?cn(h.state,g.state):{};return tt=ze(g.state,tt),{pathname:J,search:k,searchStr:K,state:tt,hash:et??"",href:`${J}${K}${G}`,unmaskOnReload:h.unmaskOnReload}},f=(h={},y)=>{var g;const m=s(h);let v=y?s(y):void 0;if(!v){let _={};const p=(g=this.options.routeMasks)==null?void 0:g.find(S=>{const A=Eo(this.basepath,m.pathname,{to:S.from,caseSensitive:!1,fuzzy:!1},this.parsePathnameCache);return A?(_=A,!0):!1});if(p){const{from:S,...A}=p;y={...Ci(c,["from"]),...A,params:_},v=s(y)}}if(v){const _=s(y);m.maskedLocation=_}return m};return c.mask?f(c,{...Ci(c,["from"]),...c.mask}):f(c)},this.commitLocation=({viewTransition:c,ignoreBlocker:s,...f})=>{const h=()=>{const m=["key","__TSR_key","__TSR_index","__hashScrollIntoViewOptions"];m.forEach(_=>{f.state[_]=this.latestLocation.state[_]});const v=ea(f.state,this.latestLocation.state);return m.forEach(_=>{delete f.state[_]}),v},y=this.latestLocation.href===f.href,g=this.commitLocationPromise;if(this.commitLocationPromise=un(()=>{g?.resolve()}),y&&h())this.load();else{let{maskedLocation:m,hashScrollIntoView:v,..._}=f;m&&(_={...m,state:{...m.state,__tempKey:void 0,__tempLocation:{..._,search:_.searchStr,state:{..._.state,__tempKey:void 0,__tempLocation:void 0,__TSR_key:void 0,key:void 0}}}},(_.unmaskOnReload??this.options.unmaskOnReload??!1)&&(_.state.__tempKey=this.tempLocationKey)),_.state.__hashScrollIntoViewOptions=v??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=c,this.history[f.replace?"replace":"push"](_.href,_.state,{ignoreBlocker:s})}return this.resetNextScroll=f.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:c,resetScroll:s,hashScrollIntoView:f,viewTransition:h,ignoreBlocker:y,href:g,...m}={})=>{if(g){const _=this.history.location.state.__TSR_index,p=ru(g,{__TSR_index:c?_:_+1});m.to=p.pathname,m.search=this.options.parseSearch(p.search),m.hash=p.hash.slice(1)}const v=this.buildLocation({...m,_includeValidateSearch:!0});return this.commitLocation({...v,viewTransition:h,replace:c,resetScroll:s,hashScrollIntoView:f,ignoreBlocker:y})},this.navigate=({to:c,reloadDocument:s,href:f,...h})=>{if(!s&&f)try{new URL(`${f}`),s=!0}catch{}if(s){if(!f){const y=this.buildLocation({to:c,...h});f=this.history.createHref(y.href)}return h.replace?window.location.replace(f):window.location.href=f,Promise.resolve()}return this.buildAndCommitLocation({...h,href:f,to:c,_isNavigate:!0})},this.beforeLoad=()=>{if(this.cancelMatches(),this.latestLocation=this.parseLocation(this.latestLocation),this.isServer){const s=this.buildLocation({to:this.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0}),f=h=>{try{return encodeURI(decodeURI(h))}catch{return h}};if(uo(f(this.latestLocation.href))!==uo(f(s.href)))throw T0({href:s.href})}const c=this.matchRoutes(this.latestLocation);this.__store.setState(s=>({...s,status:"pending",statusCode:200,isLoading:!0,location:this.latestLocation,pendingMatches:c,cachedMatches:s.cachedMatches.filter(f=>!c.find(h=>h.id===f.id))}))},this.load=async c=>{let s,f,h;for(h=new Promise(y=>{this.startTransition(async()=>{var g;try{this.beforeLoad();const m=this.latestLocation,v=this.state.resolvedLocation;this.state.redirect||this.emit({type:"onBeforeNavigate",...sn({resolvedLocation:v,location:m})}),this.emit({type:"onBeforeLoad",...sn({resolvedLocation:v,location:m})}),await this.loadMatches({sync:c?.sync,matches:this.state.pendingMatches,location:m,onReady:async()=>{this.startViewTransition(async()=>{let _,p,S;go(()=>{this.__store.setState(A=>{const R=A.matches,z=A.pendingMatches||A.matches;return _=R.filter(H=>!z.find(q=>q.id===H.id)),p=z.filter(H=>!R.find(q=>q.id===H.id)),S=R.filter(H=>z.find(q=>q.id===H.id)),{...A,isLoading:!1,loadedAt:Date.now(),matches:z,pendingMatches:void 0,cachedMatches:[...A.cachedMatches,..._.filter(H=>H.status!=="error")]}}),this.clearExpiredCache()}),[[_,"onLeave"],[p,"onEnter"],[S,"onStay"]].forEach(([A,R])=>{A.forEach(z=>{var H,q;(q=(H=this.looseRoutesById[z.routeId].options)[R])==null||q.call(H,z)})})})}})}catch(m){Ke(m)?(s=m,this.isServer||this.navigate({...s.options,replace:!0,ignoreBlocker:!0})):Ce(m)&&(f=m),this.__store.setState(v=>({...v,statusCode:s?s.status:f?404:v.matches.some(_=>_.status==="error")?500:200,redirect:s}))}this.latestLoadPromise===h&&((g=this.commitLocationPromise)==null||g.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),y()})}),this.latestLoadPromise=h,await h;this.latestLoadPromise&&h!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(y=>({...y,statusCode:404}))},this.startViewTransition=c=>{const s=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,s&&typeof document<"u"&&"startViewTransition"in document&&typeof document.startViewTransition=="function"){let f;if(typeof s=="object"&&this.isViewTransitionTypesSupported){const h=this.latestLocation,y=this.state.resolvedLocation,g=typeof s.types=="function"?s.types(sn({resolvedLocation:y,location:h})):s.types;f={update:c,types:g}}else f=c;document.startViewTransition(f)}else c()},this.updateMatch=(c,s)=>{var f;let h;const y=(f=this.state.pendingMatches)==null?void 0:f.find(_=>_.id===c),g=this.state.matches.find(_=>_.id===c),m=this.state.cachedMatches.find(_=>_.id===c),v=y?"pendingMatches":g?"matches":m?"cachedMatches":"";return v&&this.__store.setState(_=>{var p;return{..._,[v]:(p=_[v])==null?void 0:p.map(S=>S.id===c?h=s(S):S)}}),h},this.getMatch=c=>[...this.state.cachedMatches,...this.state.pendingMatches??[],...this.state.matches].find(s=>s.id===c),this.loadMatches=async({location:c,matches:s,preload:f,onReady:h,updateMatch:y=this.updateMatch,sync:g})=>{let m,v=!1;const _=async()=>{v||(v=!0,await h?.())},p=R=>!!(f&&!this.state.matches.find(z=>z.id===R));!this.isServer&&this.state.matches.find(R=>R._forcePending)&&_();const S=(R,z)=>{var H,q,lt;if(Ke(z)||Ce(z)){if(Ke(z)&&z.redirectHandled&&!z.options.reloadDocument)throw z;if((H=R.beforeLoadPromise)==null||H.resolve(),(q=R.loaderPromise)==null||q.resolve(),y(R.id,J=>({...J,status:Ke(z)?"redirected":Ce(z)?"notFound":"error",isFetching:!1,error:z,beforeLoadPromise:void 0,loaderPromise:void 0})),z.routeId||(z.routeId=R.routeId),(lt=R.loadPromise)==null||lt.resolve(),Ke(z))throw v=!0,z.options._fromLocation=c,z.redirectHandled=!0,z=this.resolveRedirect(z),z;if(Ce(z))throw this._handleNotFound(s,z,{updateMatch:y}),z}},A=R=>{const z=this.getMatch(R);return!!(!this.isServer&&z._dehydrated||this.isServer&&z.ssr===!1)};try{await new Promise((R,z)=>{(async()=>{var H,q,lt,J;try{const k=(G,tt,it)=>{var Y,ut;const{id:F,routeId:mt}=s[G],bt=this.looseRoutesById[mt];if(tt instanceof Promise)throw tt;tt.routerCode=it,m=m??G,S(this.getMatch(F),tt);try{(ut=(Y=bt.options).onError)==null||ut.call(Y,tt)}catch(pt){tt=pt,S(this.getMatch(F),tt)}y(F,pt=>{var x,w;return(x=pt.beforeLoadPromise)==null||x.resolve(),(w=pt.loadPromise)==null||w.resolve(),{...pt,error:tt,status:"error",isFetching:!1,updatedAt:Date.now(),abortController:new AbortController,beforeLoadPromise:void 0}})};for(const[G,{id:tt,routeId:it}]of s.entries()){const Y=this.getMatch(tt),ut=(H=s[G-1])==null?void 0:H.id,F=ut?this.getMatch(ut):void 0,mt=this.looseRoutesById[it],bt=mt.options.pendingMs??this.options.defaultPendingMs;if(this.isServer){let Z;if(this.isShell())Z=tt===Ue;else{const ot=this.options.defaultSsr??!0;if(F?.ssr===!1)Z=!1;else{let E;if(mt.options.ssr===void 0)E=ot;else if(typeof mt.options.ssr=="function"){let L=function(nt,$){return $?{status:"error",error:$}:{status:"success",value:nt}};const{search:X,params:V}=this.getMatch(tt),P={search:L(X,Y.searchError),params:L(V,Y.paramsError),location:c,matches:s.map(nt=>({index:nt.index,pathname:nt.pathname,fullPath:nt.fullPath,staticData:nt.staticData,id:nt.id,routeId:nt.routeId,search:L(nt.search,nt.searchError),params:L(nt.params,nt.paramsError),ssr:nt.ssr}))};E=await mt.options.ssr(P)??ot}else E=mt.options.ssr;E===!0&&F?.ssr==="data-only"?Z="data-only":Z=E}}y(tt,ot=>({...ot,ssr:Z}))}if(A(tt))continue;const pt=!!(h&&!this.isServer&&!p(tt)&&(mt.options.loader||mt.options.beforeLoad||Qh(mt))&&typeof bt=="number"&&bt!==1/0&&(mt.options.pendingComponent??((q=this.options)==null?void 0:q.defaultPendingComponent)));let x=!0;const w=()=>{if(pt&&this.getMatch(tt).pendingTimeout===void 0){const Z=setTimeout(()=>{try{_()}catch{}},bt);y(tt,ot=>({...ot,pendingTimeout:Z}))}};if(Y.beforeLoadPromise||Y.loaderPromise){w(),await Y.beforeLoadPromise;const Z=this.getMatch(tt);Z.status==="error"?x=!0:Z.preload&&(Z.status==="redirected"||Z.status==="notFound")&&S(Z,Z.error)}if(x){try{y(tt,Rt=>{const Lt=Rt.loadPromise;return{...Rt,loadPromise:un(()=>{Lt?.resolve()}),beforeLoadPromise:un()}});const{paramsError:Z,searchError:ot}=this.getMatch(tt);Z&&k(G,Z,"PARSE_PARAMS"),ot&&k(G,ot,"VALIDATE_SEARCH"),w();const E=new AbortController,L=F?.context??this.options.context??{};y(tt,Rt=>({...Rt,isFetching:"beforeLoad",fetchCount:Rt.fetchCount+1,abortController:E,context:{...L,...Rt.__routeContext}}));const{search:X,params:V,context:P,cause:nt}=this.getMatch(tt),$=p(tt),_t={search:X,abortController:E,params:V,preload:$,context:P,location:c,navigate:Rt=>this.navigate({...Rt,_fromLocation:c}),buildLocation:this.buildLocation,cause:$?"preload":nt,matches:s},ft=await((J=(lt=mt.options).beforeLoad)==null?void 0:J.call(lt,_t));(Ke(ft)||Ce(ft))&&k(G,ft,"BEFORE_LOAD"),y(tt,Rt=>({...Rt,__beforeLoadContext:ft,context:{...L,...Rt.__routeContext,...ft},abortController:E}))}catch(Z){k(G,Z,"BEFORE_LOAD")}y(tt,Z=>{var ot;return(ot=Z.beforeLoadPromise)==null||ot.resolve(),{...Z,beforeLoadPromise:void 0,isFetching:!1}})}}const K=s.slice(0,m),et=[];K.forEach(({id:G,routeId:tt},it)=>{et.push((async()=>{let Y=!1,ut=!1;const F=this.looseRoutesById[tt],mt=async()=>{var x,w,Z,ot,E,L;const X=this.getMatch(G);if(!X)return;const V={matches:s,match:X,params:X.params,loaderData:X.loaderData},P=await((w=(x=F.options).head)==null?void 0:w.call(x,V)),nt=P?.meta,$=P?.links,_t=P?.scripts,ft=P?.styles,Rt=await((ot=(Z=F.options).scripts)==null?void 0:ot.call(Z,V)),Lt=await((L=(E=F.options).headers)==null?void 0:L.call(E,V));return{meta:nt,links:$,headScripts:_t,headers:Lt,scripts:Rt,styles:ft}},bt=async()=>{const x=this.getMatch(G);x.minPendingPromise&&await x.minPendingPromise},pt=this.getMatch(G);if(A(G)){if(this.isServer){const x=await mt();return y(G,w=>({...w,...x})),this.getMatch(G)}}else if(pt.loaderPromise){if(pt.status==="success"&&!g&&!pt.preload)return this.getMatch(G);await pt.loaderPromise;const x=this.getMatch(G);x.error&&S(x,x.error)}else{const x=et[it-1],w=()=>{const{params:$,loaderDeps:_t,abortController:ft,context:Rt,cause:Lt}=this.getMatch(G),Ht=p(G);return{params:$,deps:_t,preload:!!Ht,parentMatchPromise:x,abortController:ft,context:Rt,location:c,navigate:Pt=>this.navigate({...Pt,_fromLocation:c}),cause:Ht?"preload":Lt,route:F}},Z=Date.now()-this.getMatch(G).updatedAt,ot=p(G),E=ot?F.options.preloadStaleTime??this.options.defaultPreloadStaleTime??3e4:F.options.staleTime??this.options.defaultStaleTime??0,L=F.options.shouldReload,X=typeof L=="function"?L(w()):L;y(G,$=>({...$,loaderPromise:un(),preload:!!ot&&!this.state.matches.find(_t=>_t.id===G)}));const V=async()=>{var $,_t,ft,Rt;try{try{(!this.isServer||this.isServer&&this.getMatch(G).ssr===!0)&&this.loadRouteChunk(F),y(G,Pt=>({...Pt,isFetching:"loader"}));const Lt=await((_t=($=F.options).loader)==null?void 0:_t.call($,w()));S(this.getMatch(G),Lt),y(G,Pt=>({...Pt,loaderData:Lt})),await F._lazyPromise;const Ht=await mt();await bt(),await F._componentsPromise,y(G,Pt=>({...Pt,error:void 0,status:"success",isFetching:!1,updatedAt:Date.now(),...Ht}))}catch(Lt){let Ht=Lt;await bt(),S(this.getMatch(G),Lt);try{(Rt=(ft=F.options).onError)==null||Rt.call(ft,Lt)}catch(qe){Ht=qe,S(this.getMatch(G),qe)}const Pt=await mt();y(G,qe=>({...qe,error:Ht,status:"error",isFetching:!1,...Pt}))}}catch(Lt){const Ht=await mt();y(G,Pt=>({...Pt,loaderPromise:void 0,...Ht})),S(this.getMatch(G),Lt)}},{status:P,invalid:nt}=this.getMatch(G);if(Y=P==="success"&&(nt||(X??Z>E)),!(ot&&F.options.preload===!1))if(Y&&!g)ut=!0,(async()=>{try{await V();const{loaderPromise:$,loadPromise:_t}=this.getMatch(G);$?.resolve(),_t?.resolve(),y(G,ft=>({...ft,loaderPromise:void 0}))}catch($){Ke($)&&await this.navigate($.options)}})();else if(P!=="success"||Y&&g)await V();else{const $=await mt();y(G,_t=>({..._t,...$}))}}if(!ut){const{loaderPromise:x,loadPromise:w}=this.getMatch(G);x?.resolve(),w?.resolve()}return y(G,x=>(clearTimeout(x.pendingTimeout),{...x,isFetching:ut?x.isFetching:!1,loaderPromise:ut?x.loaderPromise:void 0,invalid:!1,pendingTimeout:void 0,_dehydrated:void 0})),this.getMatch(G)})())}),await Promise.all(et),R()}catch(k){z(k)}})()}),await _()}catch(R){if(Ke(R)||Ce(R))throw Ce(R)&&!f&&await _(),R}return s},this.invalidate=c=>{const s=f=>{var h;return((h=c?.filter)==null?void 0:h.call(c,f))??!0?{...f,invalid:!0,...c?.forcePending||f.status==="error"?{status:"pending",error:void 0}:{}}:f};return this.__store.setState(f=>{var h;return{...f,matches:f.matches.map(s),cachedMatches:f.cachedMatches.map(s),pendingMatches:(h=f.pendingMatches)==null?void 0:h.map(s)}}),this.shouldViewTransition=!1,this.load({sync:c?.sync})},this.resolveRedirect=c=>(c.options.href||(c.options.href=this.buildLocation(c.options).href,c.headers.set("Location",c.options.href)),c.headers.get("Location")||c.headers.set("Location",c.options.href),c),this.clearCache=c=>{const s=c?.filter;s!==void 0?this.__store.setState(f=>({...f,cachedMatches:f.cachedMatches.filter(h=>!s(h))})):this.__store.setState(f=>({...f,cachedMatches:[]}))},this.clearExpiredCache=()=>{const c=s=>{const f=this.looseRoutesById[s.routeId];if(!f.options.loader)return!0;const h=(s.preload?f.options.preloadGcTime??this.options.defaultPreloadGcTime:f.options.gcTime??this.options.defaultGcTime)??300*1e3;return!(s.status!=="error"&&Date.now()-s.updatedAt<h)};this.clearCache({filter:c})},this.loadRouteChunk=c=>(c._lazyPromise===void 0&&(c.lazyFn?c._lazyPromise=c.lazyFn().then(s=>{const{id:f,...h}=s.options;Object.assign(c.options,h)}):c._lazyPromise=Promise.resolve()),c._componentsPromise===void 0&&(c._componentsPromise=c._lazyPromise.then(()=>Promise.all(sv.map(async s=>{const f=c.options[s];f?.preload&&await f.preload()})))),c._componentsPromise),this.preloadRoute=async c=>{const s=this.buildLocation(c);let f=this.matchRoutes(s,{throwOnError:!0,preload:!0,dest:c});const h=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(g=>g.id)),y=new Set([...h,...this.state.cachedMatches.map(g=>g.id)]);go(()=>{f.forEach(g=>{y.has(g.id)||this.__store.setState(m=>({...m,cachedMatches:[...m.cachedMatches,g]}))})});try{return f=await this.loadMatches({matches:f,location:s,preload:!0,updateMatch:(g,m)=>{h.has(g)?f=f.map(v=>v.id===g?m(v):v):this.updateMatch(g,m)}}),f}catch(g){if(Ke(g))return g.options.reloadDocument?void 0:await this.preloadRoute({...g.options,_fromLocation:s});Ce(g)||console.error(g);return}},this.matchRoute=(c,s)=>{const f={...c,to:c.to?this.resolvePathWithBase(c.from||"",c.to):void 0,params:c.params||{},leaveParams:!0},h=this.buildLocation(f);if(s?.pending&&this.state.status!=="pending")return!1;const g=(s?.pending===void 0?!this.state.isLoading:s.pending)?this.latestLocation:this.state.resolvedLocation||this.state.location,m=Eo(this.basepath,g.pathname,{...s,to:h.pathname},this.parsePathnameCache);return!m||c.params&&!ea(m,c.params,{partial:!0})?!1:m&&(s?.includeSearch??!0)?ea(g.search,h.search,{partial:!0})?m:!1:m},this._handleNotFound=(c,s,{updateMatch:f=this.updateMatch}={})=>{var h;const y=this.routesById[s.routeId??""]??this.routeTree,g={};for(const v of c)g[v.routeId]=v;!y.options.notFoundComponent&&((h=this.options)!=null&&h.defaultNotFoundComponent)&&(y.options.notFoundComponent=this.options.defaultNotFoundComponent),je(y.options.notFoundComponent);const m=g[y.id];je(m,"Could not find match for route: "+y.id),f(m.id,v=>({...v,status:"notFound",error:s,isFetching:!1})),s.routerCode==="BEFORE_LOAD"&&y.parentRoute&&(s.routeId=y.parentRoute.id,this._handleNotFound(c,s,{updateMatch:f}))},this.hasNotFoundMatch=()=>this.__store.state.matches.some(c=>c.status==="notFound"||c.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...r,caseSensitive:r.caseSensitive??!1,notFoundMode:r.notFoundMode??"fuzzy",stringifySearch:r.stringifySearch??b0,parseSearch:r.parseSearch??_0}),typeof document<"u"&&(self.__TSR_ROUTER__=this)}isShell(){return this.options.isShell}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(r,c){var s;const{foundRoute:f,matchedRoutes:h,routeParams:y}=this.getMatchedRoutes(r.pathname,(s=c?.dest)==null?void 0:s.to);let g=!1;(f?f.path!=="/"&&y["**"]:aa(r.pathname))&&(this.options.notFoundRoute?h.push(this.options.notFoundRoute):g=!0);const m=(()=>{if(g){if(this.options.notFoundMode!=="root")for(let S=h.length-1;S>=0;S--){const A=h[S];if(A.children)return A.id}return Ue}})(),v=h.map(S=>{var A;let R;const z=((A=S.options.params)==null?void 0:A.parse)??S.options.parseParams;if(z)try{const H=z(y);Object.assign(y,H)}catch(H){if(R=new x0(H.message,{cause:H}),c?.throwOnError)throw R;return R}}),_=[],p=S=>S?.id?S.context??this.options.context??{}:this.options.context??{};return h.forEach((S,A)=>{var R,z;const H=_[A-1],[q,lt,J]=(()=>{const bt=H?.search??r.search,pt=H?._strictSearch??{};try{const x=To(S.options.validateSearch,{...bt})??{};return[{...bt,...x},{...pt,...x},void 0]}catch(x){let w=x;if(x instanceof Hi||(w=new Hi(x.message,{cause:x})),c?.throwOnError)throw w;return[bt,{},w]}})(),k=((z=(R=S.options).loaderDeps)==null?void 0:z.call(R,{search:q}))??"",K=k?JSON.stringify(k):"",{usedParams:et,interpolatedPath:G}=Ai({path:S.fullPath,params:y,decodeCharMap:this.pathParamsDecodeCharMap}),tt=Ai({path:S.id,params:y,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap,parseCache:this.parsePathnameCache}).interpolatedPath+K,it=this.getMatch(tt),Y=this.state.matches.find(bt=>bt.routeId===S.id),ut=Y?"stay":"enter";let F;if(it)F={...it,cause:ut,params:Y?ze(Y.params,y):y,_strictParams:et,search:ze(Y?Y.search:it.search,q),_strictSearch:lt};else{const bt=S.options.loader||S.options.beforeLoad||S.lazyFn||Qh(S)?"pending":"success";F={id:tt,index:A,routeId:S.id,params:Y?ze(Y.params,y):y,_strictParams:et,pathname:fl([this.basepath,G]),updatedAt:Date.now(),search:Y?ze(Y.search,q):q,_strictSearch:lt,searchError:void 0,status:bt,isFetching:!1,error:void 0,paramsError:v[A],__routeContext:{},__beforeLoadContext:void 0,context:{},abortController:new AbortController,fetchCount:0,cause:ut,loaderDeps:Y?ze(Y.loaderDeps,k):k,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:S.options.staticData||{},loadPromise:un(),fullPath:S.fullPath}}c?.preload||(F.globalNotFound=m===S.id),F.searchError=J;const mt=p(H);F.context={...mt,...F.__routeContext,...F.__beforeLoadContext},_.push(F)}),_.forEach((S,A)=>{var R,z;const H=this.looseRoutesById[S.routeId];if(!this.getMatch(S.id)&&c?._buildLocation!==!0){const lt=_[A-1],J=p(lt),k={deps:S.loaderDeps,params:S.params,context:J,location:r,navigate:K=>this.navigate({...K,_fromLocation:r}),buildLocation:this.buildLocation,cause:S.cause,abortController:S.abortController,preload:!!S.preload,matches:_};S.__routeContext=((z=(R=H.options).context)==null?void 0:z.call(R,k))??{},S.context={...J,...S.__routeContext,...S.__beforeLoadContext}}}),_}}class Hi extends Error{}class x0 extends Error{}const Gh=i=>i.endsWith("/")&&i.length>1?i.slice(0,-1):i;function Xh(i,r){return Gh(i)===Gh(r)}function D0(i){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:"idle",resolvedLocation:void 0,location:i,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function To(i,r){if(i==null)return{};if("~standard"in i){const c=i["~standard"].validate(r);if(c instanceof Promise)throw new Hi("Async validation not supported");if(c.issues)throw new Hi(JSON.stringify(c.issues,void 0,2),{cause:c});return c.value}return"parse"in i?i.parse(r):typeof i=="function"?i(r):{}}const sv=["component","errorComponent","pendingComponent","notFoundComponent"];function Qh(i){var r;for(const c of sv)if((r=i.options[c])!=null&&r.preload)return!0;return!1}const z0=.5,C0=.4,U0=.25;function Zh(i,r){return i.prefixSegment&&i.suffixSegment?r+.05:i.prefixSegment?r+.02:i.suffixSegment?r+.01:r}function N0({routeTree:i,initRoute:r}){const c={},s={},f=m=>{m.forEach((v,_)=>{r?.(v,_);const p=c[v.id];if(je(!p,`Duplicate routes found with id: ${String(v.id)}`),c[v.id]=v,!v.isRoot&&v.path){const A=aa(v.fullPath);(!s[A]||v.fullPath.endsWith("/"))&&(s[A]=v)}const S=v.children;S?.length&&f(S)})};f([i]);const h=[];Object.values(c).forEach((m,v)=>{var _;if(m.isRoot||!m.path)return;const p=Oo(m.fullPath);let S=ua(p),A=0;for(;S.length>A+1&&((_=S[A])==null?void 0:_.value)==="/";)A++;A>0&&(S=S.slice(A));let R=0,z=!1;const H=S.map((q,lt)=>{if(q.value==="/")return .75;let J;if(q.type===on?J=z0:q.type===na?(J=C0,R++):q.type===rn&&(J=U0),J){for(let k=lt+1;k<S.length;k++){const K=S[k];if(K.type===rl&&K.value!=="/")return z=!0,Zh(q,J+.2)}return Zh(q,J)}return 1});h.push({child:m,trimmed:p,parsed:S,index:v,scores:H,optionalParamCount:R,hasStaticAfter:z})});const g=h.sort((m,v)=>{const _=Math.min(m.scores.length,v.scores.length);for(let p=0;p<_;p++)if(m.scores[p]!==v.scores[p])return v.scores[p]-m.scores[p];if(m.scores.length!==v.scores.length){if(m.optionalParamCount!==v.optionalParamCount){if(m.hasStaticAfter===v.hasStaticAfter)return m.optionalParamCount-v.optionalParamCount;if(m.hasStaticAfter&&!v.hasStaticAfter)return-1;if(!m.hasStaticAfter&&v.hasStaticAfter)return 1}return v.scores.length-m.scores.length}for(let p=0;p<_;p++)if(m.parsed[p].value!==v.parsed[p].value)return m.parsed[p].value>v.parsed[p].value?1:-1;return m.index-v.index}).map((m,v)=>(m.child.rank=v,m.child));return{routesById:c,routesByPath:s,flatRoutes:g}}function L0({pathname:i,routePathname:r,basepath:c,caseSensitive:s,routesByPath:f,routesById:h,flatRoutes:y,parseCache:g}){let m={};const v=aa(i),_=R=>{var z;return Eo(c,v,{to:R.fullPath,caseSensitive:((z=R.options)==null?void 0:z.caseSensitive)??s,fuzzy:!0},g)};let p=r!==void 0?f[r]:void 0;if(p)m=_(p);else{let R;for(const z of y){const H=_(z);if(H)if(z.path!=="/"&&H["**"])R||(R={foundRoute:z,routeParams:H});else{p=z,m=H;break}}!p&&R&&(p=R.foundRoute,m=R.routeParams)}let S=p||h[Ue];const A=[S];for(;S.parentRoute;)S=S.parentRoute,A.push(S);return A.reverse(),{matchedRoutes:A,routeParams:m,foundRoute:p}}function B0({search:i,dest:r,destRoutes:c,_includeValidateSearch:s}){const f=c.reduce((g,m)=>{var v;const _=[];if("search"in m.options)(v=m.options.search)!=null&&v.middlewares&&_.push(...m.options.search.middlewares);else if(m.options.preSearchFilters||m.options.postSearchFilters){const p=({search:S,next:A})=>{let R=S;"preSearchFilters"in m.options&&m.options.preSearchFilters&&(R=m.options.preSearchFilters.reduce((H,q)=>q(H),S));const z=A(R);return"postSearchFilters"in m.options&&m.options.postSearchFilters?m.options.postSearchFilters.reduce((H,q)=>q(H),z):z};_.push(p)}if(s&&m.options.validateSearch){const p=({search:S,next:A})=>{const R=A(S);try{return{...R,...To(m.options.validateSearch,R)??{}}}catch{return R}};_.push(p)}return g.concat(_)},[])??[],h=({search:g})=>r.search?r.search===!0?g:cn(r.search,g):{};f.push(h);const y=(g,m)=>{if(g>=f.length)return m;const v=f[g];return v({search:m,next:p=>y(g+1,p)})};return y(0,i)}const Je=Symbol.for("TSR_DEFERRED_PROMISE");function H0(i,r){const c=i;return c[Je]||(c[Je]={status:"pending"},c.then(s=>{c[Je].status="success",c[Je].data=s}).catch(s=>{c[Je].status="error",c[Je].error={data:A0(s),__isServerError:!0}})),c}const j0="Error preloading route! ☝️";class ov{constructor(r){if(this.init=c=>{var s,f;this.originalIndex=c.originalIndex;const h=this.options,y=!h?.path&&!h?.id;this.parentRoute=(f=(s=this.options).getParentRoute)==null?void 0:f.call(s),y?this._path=Ue:this.parentRoute||je(!1);let g=y?Ue:h?.path;g&&g!=="/"&&(g=Oo(g));const m=h?.id||g;let v=y?Ue:fl([this.parentRoute.id===Ue?"":this.parentRoute.id,m]);g===Ue&&(g="/"),v!==Ue&&(v=fl(["/",v]));const _=v===Ue?"/":fl([this.parentRoute.fullPath,g]);this._path=g,this._id=v,this._fullPath=_,this._to=_},this.clone=c=>{this._path=c._path,this._id=c._id,this._fullPath=c._fullPath,this._to=c._to,this.options.getParentRoute=c.options.getParentRoute,this.children=c.children},this.addChildren=c=>this._addFileChildren(c),this._addFileChildren=c=>(Array.isArray(c)&&(this.children=c),typeof c=="object"&&c!==null&&(this.children=Object.values(c)),this),this._addFileTypes=()=>this,this.updateLoader=c=>(Object.assign(this.options,c),this),this.update=c=>(Object.assign(this.options,c),this),this.lazy=c=>(this.lazyFn=c,this),this.options=r||{},this.isRoot=!r?.getParentRoute,r?.id&&r?.path)throw new Error("Route cannot have both an 'id' and a 'path' option.")}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}}class w0 extends ov{constructor(r){super(r)}}const q0="modulepreload",Y0=function(i){return"/"+i},Kh={},dl=function(r,c,s){let f=Promise.resolve();if(c&&c.length>0){let m=function(v){return Promise.all(v.map(_=>Promise.resolve(_).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};document.getElementsByTagName("link");const y=document.querySelector("meta[property=csp-nonce]"),g=y?.nonce||y?.getAttribute("nonce");f=m(c.map(v=>{if(v=Y0(v),v in Kh)return;Kh[v]=!0;const _=v.endsWith(".css"),p=_?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${v}"]${p}`))return;const S=document.createElement("link");if(S.rel=_?"stylesheet":q0,_||(S.as="script"),S.crossOrigin="",S.href=v,g&&S.setAttribute("nonce",g),document.head.appendChild(S),_)return new Promise((A,R)=>{S.addEventListener("load",A),S.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${v}`)))})}))}function h(y){const g=new Event("vite:preloadError",{cancelable:!0});if(g.payload=y,window.dispatchEvent(g),!g.defaultPrevented)throw y}return f.then(y=>{for(const g of y||[])g.status==="rejected"&&h(g.reason);return r().catch(h)})};function V0({promise:i}){const r=H0(i);if(r[Je].status==="pending")throw r;if(r[Je].status==="error")throw r[Je].error;return[r[Je].data,r]}function G0(i){const r=Q.jsx(X0,{...i});return i.fallback?Q.jsx(W.Suspense,{fallback:i.fallback,children:r}):r}function X0(i){const[r]=V0(i);return i.children(r)}function xo(i){const r=i.errorComponent??ji;return Q.jsx(Q0,{getResetKey:i.getResetKey,onCatch:i.onCatch,children:({error:c,reset:s})=>c?W.createElement(r,{error:c,reset:s}):i.children})}class Q0 extends W.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(r){return{resetKey:r.getResetKey()}}static getDerivedStateFromError(r){return{error:r}}reset(){this.setState({error:null})}componentDidUpdate(r,c){c.error&&c.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(r,c){this.props.onCatch&&this.props.onCatch(r,c)}render(){return this.props.children({error:this.state.resetKey!==this.props.getResetKey()?null:this.state.error,reset:()=>{this.reset()}})}}function ji({error:i}){const[r,c]=W.useState(!1);return Q.jsxs("div",{style:{padding:".5rem",maxWidth:"100%"},children:[Q.jsxs("div",{style:{display:"flex",alignItems:"center",gap:".5rem"},children:[Q.jsx("strong",{style:{fontSize:"1rem"},children:"Something went wrong!"}),Q.jsx("button",{style:{appearance:"none",fontSize:".6em",border:"1px solid currentColor",padding:".1rem .2rem",fontWeight:"bold",borderRadius:".25rem"},onClick:()=>c(s=>!s),children:r?"Hide Error":"Show Error"})]}),Q.jsx("div",{style:{height:".25rem"}}),r?Q.jsx("div",{children:Q.jsx("pre",{style:{fontSize:".7em",border:"1px solid red",borderRadius:".25rem",padding:".3rem",color:"red",overflow:"auto"},children:i.message?Q.jsx("code",{children:i.message}):null})}):null]})}function Z0({children:i,fallback:r=null}){return K0()?Q.jsx(la.Fragment,{children:i}):Q.jsx(la.Fragment,{children:r})}function K0(){return la.useSyncExternalStore(J0,()=>!0,()=>!1)}function J0(){return()=>{}}var so={exports:{}},oo={},ro={exports:{}},fo={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jh;function $0(){if(Jh)return fo;Jh=1;var i=fu();function r(p,S){return p===S&&(p!==0||1/p===1/S)||p!==p&&S!==S}var c=typeof Object.is=="function"?Object.is:r,s=i.useState,f=i.useEffect,h=i.useLayoutEffect,y=i.useDebugValue;function g(p,S){var A=S(),R=s({inst:{value:A,getSnapshot:S}}),z=R[0].inst,H=R[1];return h(function(){z.value=A,z.getSnapshot=S,m(z)&&H({inst:z})},[p,A,S]),f(function(){return m(z)&&H({inst:z}),p(function(){m(z)&&H({inst:z})})},[p]),y(A),A}function m(p){var S=p.getSnapshot;p=p.value;try{var A=S();return!c(p,A)}catch{return!0}}function v(p,S){return S()}var _=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?v:g;return fo.useSyncExternalStore=i.useSyncExternalStore!==void 0?i.useSyncExternalStore:_,fo}var $h;function k0(){return $h||($h=1,ro.exports=$0()),ro.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kh;function P0(){if(kh)return oo;kh=1;var i=fu(),r=k0();function c(v,_){return v===_&&(v!==0||1/v===1/_)||v!==v&&_!==_}var s=typeof Object.is=="function"?Object.is:c,f=r.useSyncExternalStore,h=i.useRef,y=i.useEffect,g=i.useMemo,m=i.useDebugValue;return oo.useSyncExternalStoreWithSelector=function(v,_,p,S,A){var R=h(null);if(R.current===null){var z={hasValue:!1,value:null};R.current=z}else z=R.current;R=g(function(){function q(et){if(!lt){if(lt=!0,J=et,et=S(et),A!==void 0&&z.hasValue){var G=z.value;if(A(G,et))return k=G}return k=et}if(G=k,s(J,et))return G;var tt=S(et);return A!==void 0&&A(G,tt)?(J=et,G):(J=et,k=tt)}var lt=!1,J,k,K=p===void 0?null:p;return[function(){return q(_())},K===null?void 0:function(){return q(K())}]},[_,p,S,A]);var H=f(v,R[0],R[1]);return y(function(){z.hasValue=!0,z.value=H},[H]),m(H),H},oo}var Ph;function W0(){return Ph||(Ph=1,so.exports=P0()),so.exports}var F0=W0();function I0(i,r=c=>c){return F0.useSyncExternalStoreWithSelector(i.subscribe,()=>i.state,()=>i.state,r,tg)}function tg(i,r){if(Object.is(i,r))return!0;if(typeof i!="object"||i===null||typeof r!="object"||r===null)return!1;if(i instanceof Map&&r instanceof Map){if(i.size!==r.size)return!1;for(const[s,f]of i)if(!r.has(s)||!Object.is(f,r.get(s)))return!1;return!0}if(i instanceof Set&&r instanceof Set){if(i.size!==r.size)return!1;for(const s of i)if(!r.has(s))return!1;return!0}if(i instanceof Date&&r instanceof Date)return i.getTime()===r.getTime();const c=Object.keys(i);if(c.length!==Object.keys(r).length)return!1;for(let s=0;s<c.length;s++)if(!Object.prototype.hasOwnProperty.call(r,c[s])||!Object.is(i[c[s]],r[c[s]]))return!1;return!0}const ho=W.createContext(null);function rv(){return typeof document>"u"?ho:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=ho,ho)}function Se(i){const r=W.useContext(rv());return i?.warn,r}function Vt(i){const r=Se({warn:i?.router===void 0}),c=i?.router||r,s=W.useRef(void 0);return I0(c.__store,f=>{if(i?.select){if(i.structuralSharing??c.options.defaultStructuralSharing){const h=ze(s.current,i.select(f));return s.current=h,h}return i.select(f)}return f})}const wi=W.createContext(void 0),eg=W.createContext(void 0);function Ne(i){const r=W.useContext(i.from?eg:wi);return Vt({select:s=>{const f=s.matches.find(h=>i.from?i.from===h.routeId:h.id===r);if(je(!((i.shouldThrow??!0)&&!f),`Could not find ${i.from?`an active match from "${i.from}"`:"a nearest match!"}`),f!==void 0)return i.select?i.select(f):f},structuralSharing:i.structuralSharing})}function Do(i){return Ne({from:i.from,strict:i.strict,structuralSharing:i.structuralSharing,select:r=>i.select?i.select(r.loaderData):r.loaderData})}function zo(i){const{select:r,...c}=i;return Ne({...c,select:s=>r?r(s.loaderDeps):s.loaderDeps})}function Co(i){return Ne({from:i.from,strict:i.strict,shouldThrow:i.shouldThrow,structuralSharing:i.structuralSharing,select:r=>i.select?i.select(r.params):r.params})}function Uo(i){return Ne({from:i.from,strict:i.strict,shouldThrow:i.shouldThrow,structuralSharing:i.structuralSharing,select:r=>i.select?i.select(r.search):r.search})}function No(i){const{navigate:r,state:c}=Se(),s=Ne({strict:!1,select:f=>f.index});return W.useCallback(f=>{const h=f.from??i?.from??c.matches[s].fullPath;return r({...f,from:h})},[i?.from,r])}var fv=nv();const Mp=lv(fv),Oi=typeof window<"u"?W.useLayoutEffect:W.useEffect;function vo(i){const r=W.useRef({value:i,prev:null}),c=r.current.value;return i!==c&&(r.current={value:i,prev:c}),r.current.prev}function lg(i,r,c={},s={}){W.useEffect(()=>{if(!i.current||s.disabled||typeof IntersectionObserver!="function")return;const f=new IntersectionObserver(([h])=>{r(h)},c);return f.observe(i.current),()=>{f.disconnect()}},[r,c,s.disabled,i])}function ng(i){const r=W.useRef(null);return W.useImperativeHandle(i,()=>r.current,[]),r}function ag(i,r){const c=Se(),[s,f]=W.useState(!1),h=W.useRef(!1),y=ng(r),{activeProps:g,inactiveProps:m,activeOptions:v,to:_,preload:p,preloadDelay:S,hashScrollIntoView:A,replace:R,startTransition:z,resetScroll:H,viewTransition:q,children:lt,target:J,disabled:k,style:K,className:et,onClick:G,onFocus:tt,onMouseEnter:it,onMouseLeave:Y,onTouchStart:ut,ignoreBlocker:F,params:mt,search:bt,hash:pt,state:x,mask:w,reloadDocument:Z,unsafeRelative:ot,from:E,_fromLocation:L,...X}=i,V=W.useMemo(()=>{try{return new URL(_),"external"}catch{}return"internal"},[_]),P=Vt({select:Ut=>Ut.location.search,structuralSharing:!0}),nt=Ne({strict:!1,select:Ut=>i.from??Ut.fullPath}),$=W.useMemo(()=>c.buildLocation({...i,from:nt}),[c,P,i._fromLocation,nt,i.hash,i.to,i.search,i.params,i.state,i.mask,i.unsafeRelative]),_t=V==="external",ft=i.reloadDocument||_t?!1:p??c.options.defaultPreload,Rt=S??c.options.defaultPreloadDelay??0,Lt=Vt({select:Ut=>{if(_t)return!1;if(v?.exact){if(!t0(Ut.location.pathname,$.pathname,c.basepath))return!1}else{const Wt=Ui(Ut.location.pathname,c.basepath),Ye=Ui($.pathname,c.basepath);if(!(Wt.startsWith(Ye)&&(Wt.length===Ye.length||Wt[Ye.length]==="/")))return!1}return(v?.includeSearch??!0)&&!ea(Ut.location.search,$.search,{partial:!v?.exact,ignoreUndefined:!v?.explicitUndefined})?!1:v?.includeHash?Ut.location.hash===$.hash:!0}}),Ht=W.useCallback(()=>{c.preloadRoute({...i,from:nt}).catch(Ut=>{console.warn(Ut),console.warn(j0)})},[c,i.to,i._fromLocation,nt,i.search,i.hash,i.params,i.state,i.mask,i.unsafeRelative,i.hashScrollIntoView,i.href,i.ignoreBlocker,i.reloadDocument,i.replace,i.resetScroll,i.viewTransition]),Pt=W.useCallback(Ut=>{Ut?.isIntersecting&&Ht()},[Ht]);if(lg(y,Pt,og,{disabled:!!k||ft!=="viewport"}),W.useEffect(()=>{h.current||!k&&ft==="render"&&(Ht(),h.current=!0)},[k,Ht,ft]),_t)return{...X,ref:y,type:V,href:_,...lt&&{children:lt},...J&&{target:J},...k&&{disabled:k},...K&&{style:K},...et&&{className:et},...G&&{onClick:G},...tt&&{onFocus:tt},...it&&{onMouseEnter:it},...Y&&{onMouseLeave:Y},...ut&&{onTouchStart:ut}};const qe=Ut=>{if(!k&&!rg(Ut)&&!Ut.defaultPrevented&&(!J||J==="_self")&&Ut.button===0){Ut.preventDefault(),fv.flushSync(()=>{f(!0)});const Wt=c.subscribe("onResolved",()=>{Wt(),f(!1)});c.navigate({...i,from:nt,replace:R,resetScroll:H,hashScrollIntoView:A,startTransition:z,viewTransition:q,ignoreBlocker:F})}},fn=Ut=>{k||ft&&Ht()},qi=fn,Yi=Ut=>{if(!(k||!ft))if(!Rt)Ht();else{const Wt=Ut.target;if(iu.has(Wt))return;const Ye=setTimeout(()=>{iu.delete(Wt),Ht()},Rt);iu.set(Wt,Ye)}},_e=Ut=>{if(k||!ft||!Rt)return;const Wt=Ut.target,Ye=iu.get(Wt);Ye&&(clearTimeout(Ye),iu.delete(Wt))},dn=Lt?cn(g,{})??ug:mo,ql=Lt?mo:cn(m,{})??mo,ia=[et,dn.className,ql.className].filter(Boolean).join(" "),Yl=(K||dn.style||ql.style)&&{...K,...dn.style,...ql.style};return{...X,...dn,...ql,href:k?void 0:$.maskedLocation?c.history.createHref($.maskedLocation.href):c.history.createHref($.href),ref:y,onClick:cu([G,qe]),onFocus:cu([tt,fn]),onMouseEnter:cu([it,Yi]),onMouseLeave:cu([Y,_e]),onTouchStart:cu([ut,qi]),disabled:!!k,target:J,...Yl&&{style:Yl},...ia&&{className:ia},...k&&ig,...Lt&&cg,...s&&sg}}const mo={},ug={className:"active"},ig={role:"link","aria-disabled":!0},cg={"data-status":"active","aria-current":"page"},sg={"data-transitioning":"transitioning"},iu=new WeakMap,og={rootMargin:"100px"},cu=i=>r=>{i.filter(Boolean).forEach(c=>{r.defaultPrevented||c(r)})},dv=W.forwardRef((i,r)=>{const{_asChild:c,...s}=i,{type:f,ref:h,...y}=ag(s,r),g=typeof s.children=="function"?s.children({isActive:y["data-status"]==="active"}):s.children;return c===void 0&&delete y.disabled,W.createElement(c||"a",{...y,ref:h},g)});function rg(i){return!!(i.metaKey||i.altKey||i.ctrlKey||i.shiftKey)}let fg=class extends ov{constructor(r){super(r),this.useMatch=c=>Ne({select:c?.select,from:this.id,structuralSharing:c?.structuralSharing}),this.useRouteContext=c=>Ne({...c,from:this.id,select:s=>c?.select?c.select(s.context):s.context}),this.useSearch=c=>Uo({select:c?.select,structuralSharing:c?.structuralSharing,from:this.id}),this.useParams=c=>Co({select:c?.select,structuralSharing:c?.structuralSharing,from:this.id}),this.useLoaderDeps=c=>zo({...c,from:this.id}),this.useLoaderData=c=>Do({...c,from:this.id}),this.useNavigate=()=>No({from:this.fullPath}),this.Link=la.forwardRef((c,s)=>Q.jsx(dv,{ref:s,from:this.fullPath,...c})),this.$$typeof=Symbol.for("react.memo")}};function dg(i){return new fg(i)}class hg extends w0{constructor(r){super(r),this.useMatch=c=>Ne({select:c?.select,from:this.id,structuralSharing:c?.structuralSharing}),this.useRouteContext=c=>Ne({...c,from:this.id,select:s=>c?.select?c.select(s.context):s.context}),this.useSearch=c=>Uo({select:c?.select,structuralSharing:c?.structuralSharing,from:this.id}),this.useParams=c=>Co({select:c?.select,structuralSharing:c?.structuralSharing,from:this.id}),this.useLoaderDeps=c=>zo({...c,from:this.id}),this.useLoaderData=c=>Do({...c,from:this.id}),this.useNavigate=()=>No({from:this.fullPath}),this.Link=la.forwardRef((c,s)=>Q.jsx(dv,{ref:s,from:this.fullPath,...c})),this.$$typeof=Symbol.for("react.memo")}}function vg(i){return new hg(i)}function we(i){return typeof i=="object"?new Wh(i,{silent:!0}).createRoute(i):new Wh(i,{silent:!0}).createRoute}class Wh{constructor(r,c){this.path=r,this.createRoute=s=>{this.silent;const f=dg(s);return f.isRoot=!1,f},this.silent=c?.silent}}class Fh{constructor(r){this.useMatch=c=>Ne({select:c?.select,from:this.options.id,structuralSharing:c?.structuralSharing}),this.useRouteContext=c=>Ne({from:this.options.id,select:s=>c?.select?c.select(s.context):s.context}),this.useSearch=c=>Uo({select:c?.select,structuralSharing:c?.structuralSharing,from:this.options.id}),this.useParams=c=>Co({select:c?.select,structuralSharing:c?.structuralSharing,from:this.options.id}),this.useLoaderDeps=c=>zo({...c,from:this.options.id}),this.useLoaderData=c=>Do({...c,from:this.options.id}),this.useNavigate=()=>{const c=Se();return No({from:c.routesById[this.options.id].fullPath})},this.options=r,this.$$typeof=Symbol.for("react.memo")}}function Ih(i){return typeof i=="object"?new Fh(i):r=>new Fh({id:i,...r})}function hl(i,r){let c,s,f,h;const y=()=>(c||(c=i().then(m=>{c=void 0,s=m[r]}).catch(m=>{if(f=m,ky(f)&&f instanceof Error&&typeof window<"u"&&typeof sessionStorage<"u"){const v=`tanstack_router_reload:${f.message}`;sessionStorage.getItem(v)||(sessionStorage.setItem(v,"1"),h=!0)}})),c),g=function(v){if(h)throw window.location.reload(),new Promise(()=>{});if(f)throw f;if(!s)throw y();return W.createElement(s,v)};return g.preload=y,g}function mg(){const i=Se(),r=W.useRef({router:i,mounted:!1}),[c,s]=W.useState(!1),{hasPendingMatches:f,isLoading:h}=Vt({select:p=>({isLoading:p.isLoading,hasPendingMatches:p.matches.some(S=>S.status==="pending")}),structuralSharing:!0}),y=vo(h),g=h||c||f,m=vo(g),v=h||f,_=vo(v);return i.startTransition=p=>{s(!0),W.startTransition(()=>{p(),s(!1)})},W.useEffect(()=>{const p=i.history.subscribe(i.load),S=i.buildLocation({to:i.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return aa(i.latestLocation.href)!==aa(S.href)&&i.commitLocation({...S,replace:!0}),()=>{p()}},[i,i.history]),Oi(()=>{if(typeof window<"u"&&i.ssr||r.current.router===i&&r.current.mounted)return;r.current={router:i,mounted:!0},(async()=>{try{await i.load()}catch(S){console.error(S)}})()},[i]),Oi(()=>{y&&!h&&i.emit({type:"onLoad",...sn(i.state)})},[y,i,h]),Oi(()=>{_&&!v&&i.emit({type:"onBeforeRouteMount",...sn(i.state)})},[v,_,i]),Oi(()=>{m&&!g&&(i.emit({type:"onResolved",...sn(i.state)}),i.__store.setState(p=>({...p,status:"idle",resolvedLocation:p.location})),g0(i))},[g,m,i]),null}function yg(i){const r=Vt({select:c=>`not-found-${c.location.pathname}-${c.status}`});return Q.jsx(xo,{getResetKey:()=>r,onCatch:(c,s)=>{var f;if(Ce(c))(f=i.onCatch)==null||f.call(i,c,s);else throw c},errorComponent:({error:c})=>{var s;if(Ce(c))return(s=i.fallback)==null?void 0:s.call(i,c);throw c},children:i.children})}function gg(){return Q.jsx("p",{children:"Not Found"})}function Fn(i){return Q.jsx(Q.Fragment,{children:i.children})}function hv(i,r,c){return r.options.notFoundComponent?Q.jsx(r.options.notFoundComponent,{data:c}):i.options.defaultNotFoundComponent?Q.jsx(i.options.defaultNotFoundComponent,{data:c}):Q.jsx(gg,{})}function pg({children:i}){return typeof document<"u"?null:Q.jsx("script",{className:"$tsr",dangerouslySetInnerHTML:{__html:[i].filter(Boolean).join(`
`)}})}function Sg(){const i=Se(),c=(i.options.getScrollRestorationKey||Ro)(i.latestLocation),s=c!==Ro(i.latestLocation)?c:null;return!i.isScrollRestoring||!i.isServer?null:Q.jsx(pg,{children:`(${cv.toString()})(${JSON.stringify(Li)},${JSON.stringify(s)}, undefined, true)`})}const vv=W.memo(function({matchId:r}){var c,s;const f=Se(),h=Vt({select:J=>{const k=J.matches.find(K=>K.id===r);return je(k),Ci(k,["routeId","ssr","_displayPending"])},structuralSharing:!0}),y=f.routesById[h.routeId],g=y.options.pendingComponent??f.options.defaultPendingComponent,m=g?Q.jsx(g,{}):null,v=y.options.errorComponent??f.options.defaultErrorComponent,_=y.options.onCatch??f.options.defaultOnCatch,p=y.isRoot?y.options.notFoundComponent??((c=f.options.notFoundRoute)==null?void 0:c.options.component):y.options.notFoundComponent,S=h.ssr===!1||h.ssr==="data-only",A=(!y.isRoot||y.options.wrapInSuspense||S)&&(y.options.wrapInSuspense??g??(((s=y.options.errorComponent)==null?void 0:s.preload)||S))?W.Suspense:Fn,R=v?xo:Fn,z=p?yg:Fn,H=Vt({select:J=>J.loadedAt}),q=Vt({select:J=>{var k;const K=J.matches.findIndex(et=>et.id===r);return(k=J.matches[K-1])==null?void 0:k.routeId}}),lt=y.isRoot?y.options.shellComponent??Fn:Fn;return Q.jsxs(lt,{children:[Q.jsx(wi.Provider,{value:r,children:Q.jsx(A,{fallback:m,children:Q.jsx(R,{getResetKey:()=>H,errorComponent:v||ji,onCatch:(J,k)=>{if(Ce(J))throw J;_?.(J,k)},children:Q.jsx(z,{fallback:J=>{if(!p||J.routeId&&J.routeId!==h.routeId||!J.routeId&&!y.isRoot)throw J;return W.createElement(p,J)},children:S||h._displayPending?Q.jsx(Z0,{fallback:m,children:Q.jsx(tv,{matchId:r})}):Q.jsx(tv,{matchId:r})})})})}),q===Ue&&f.options.scrollRestoration?Q.jsxs(Q.Fragment,{children:[Q.jsx(_g,{}),Q.jsx(Sg,{})]}):null]})});function _g(){const i=Se(),r=W.useRef(void 0);return Q.jsx("script",{suppressHydrationWarning:!0,ref:c=>{c&&(r.current===void 0||r.current.href!==i.latestLocation.href)&&(i.emit({type:"onRendered",...sn(i.state)}),r.current=i.latestLocation)}},i.latestLocation.state.__TSR_key)}const tv=W.memo(function({matchId:r}){var c,s,f,h,y;const g=Se(),{match:m,key:v,routeId:_}=Vt({select:A=>{const R=A.matches.findIndex(k=>k.id===r),z=A.matches[R],H=z.routeId,q=g.routesById[H].options.remountDeps??g.options.defaultRemountDeps,lt=q?.({routeId:H,loaderDeps:z.loaderDeps,params:z._strictParams,search:z._strictSearch});return{key:lt?JSON.stringify(lt):void 0,routeId:H,match:Ci(z,["id","status","error","_forcePending","_displayPending"])}},structuralSharing:!0}),p=g.routesById[_],S=W.useMemo(()=>{const A=p.options.component??g.options.defaultComponent;return A?Q.jsx(A,{},v):Q.jsx(mv,{})},[v,p.options.component,g.options.defaultComponent]);if(m._displayPending)throw(c=g.getMatch(m.id))==null?void 0:c.displayPendingPromise;if(m._forcePending)throw(s=g.getMatch(m.id))==null?void 0:s.minPendingPromise;if(m.status==="pending"){const A=p.options.pendingMinMs??g.options.defaultPendingMinMs;if(A&&!((f=g.getMatch(m.id))!=null&&f.minPendingPromise)&&!g.isServer){const R=un();Promise.resolve().then(()=>{g.updateMatch(m.id,z=>({...z,minPendingPromise:R}))}),setTimeout(()=>{R.resolve(),g.updateMatch(m.id,z=>({...z,minPendingPromise:void 0}))},A)}throw(h=g.getMatch(m.id))==null?void 0:h.loadPromise}if(m.status==="notFound")return je(Ce(m.error)),hv(g,p,m.error);if(m.status==="redirected")throw je(Ke(m.error)),(y=g.getMatch(m.id))==null?void 0:y.loadPromise;if(m.status==="error"){if(g.isServer){const A=(p.options.errorComponent??g.options.defaultErrorComponent)||ji;return Q.jsx(A,{error:m.error,reset:void 0,info:{componentStack:""}})}throw m.error}return S}),mv=W.memo(function(){const r=Se(),c=W.useContext(wi),s=Vt({select:v=>{var _;return(_=v.matches.find(p=>p.id===c))==null?void 0:_.routeId}}),f=r.routesById[s],h=Vt({select:v=>{const p=v.matches.find(S=>S.id===c);return je(p),p.globalNotFound}}),y=Vt({select:v=>{var _;const p=v.matches,S=p.findIndex(A=>A.id===c);return(_=p[S+1])==null?void 0:_.id}}),g=r.options.defaultPendingComponent?Q.jsx(r.options.defaultPendingComponent,{}):null;if(h)return hv(r,f,void 0);if(!y)return null;const m=Q.jsx(vv,{matchId:y});return c===Ue?Q.jsx(W.Suspense,{fallback:g,children:m}):m});function bg(){const i=Se(),r=i.options.defaultPendingComponent?Q.jsx(i.options.defaultPendingComponent,{}):null,c=i.isServer||typeof document<"u"&&i.ssr?Fn:W.Suspense,s=Q.jsxs(c,{fallback:r,children:[!i.isServer&&Q.jsx(mg,{}),Q.jsx(Eg,{})]});return i.options.InnerWrap?Q.jsx(i.options.InnerWrap,{children:s}):s}function Eg(){const i=Vt({select:c=>{var s;return(s=c.matches[0])==null?void 0:s.id}}),r=Vt({select:c=>c.loadedAt});return Q.jsx(wi.Provider,{value:i,children:Q.jsx(xo,{getResetKey:()=>r,errorComponent:ji,onCatch:c=>{c.message||c.toString()},children:i?Q.jsx(vv,{matchId:i}):null})})}const Rg=i=>new Tg(i);class Tg extends O0{constructor(r){super(r)}}typeof globalThis<"u"?(globalThis.createFileRoute=we,globalThis.createLazyFileRoute=Ih):typeof window<"u"&&(window.createFileRoute=we,window.createFileRoute=Ih);function Mg({router:i,children:r,...c}){Object.keys(c).length>0&&i.update({...i.options,...c,context:{...i.options.context,...c.context}});const s=rv(),f=Q.jsx(s.Provider,{value:i,children:r});return i.options.Wrap?Q.jsx(i.options.Wrap,{children:f}):f}function Ag({router:i,...r}){return Q.jsx(Mg,{router:i,...r,children:Q.jsx(bg,{})})}function yv({tag:i,attrs:r,children:c}){switch(i){case"title":return Q.jsx("title",{...r,suppressHydrationWarning:!0,children:c});case"meta":return Q.jsx("meta",{...r,suppressHydrationWarning:!0});case"link":return Q.jsx("link",{...r,suppressHydrationWarning:!0});case"style":return Q.jsx("style",{...r,dangerouslySetInnerHTML:{__html:c}});case"script":return Q.jsx(Og,{attrs:r,children:c});default:return null}}function Og({attrs:i,children:r}){return W.useEffect(()=>{if(i?.src){const c=document.createElement("script");for(const[s,f]of Object.entries(i))s!=="suppressHydrationWarning"&&f!==void 0&&f!==!1&&c.setAttribute(s,typeof f=="boolean"?"":String(f));return document.head.appendChild(c),()=>{c.parentNode&&c.parentNode.removeChild(c)}}if(typeof r=="string"){const c=document.createElement("script");if(c.textContent=r,i)for(const[s,f]of Object.entries(i))s!=="suppressHydrationWarning"&&f!==void 0&&f!==!1&&c.setAttribute(s,typeof f=="boolean"?"":String(f));return document.head.appendChild(c),()=>{c.parentNode&&c.parentNode.removeChild(c)}}},[i,r]),i?.src&&typeof i.src=="string"?Q.jsx("script",{...i,suppressHydrationWarning:!0}):typeof r=="string"?Q.jsx("script",{...i,dangerouslySetInnerHTML:{__html:r},suppressHydrationWarning:!0}):null}const xg=()=>{const i=Se(),r=Vt({select:g=>g.matches.map(m=>m.meta).filter(Boolean)}),c=W.useMemo(()=>{const g=[],m={};let v;return[...r].reverse().forEach(_=>{[..._].reverse().forEach(p=>{if(p)if(p.title)v||(v={tag:"title",children:p.title});else{const S=p.name??p.property;if(S){if(m[S])return;m[S]=!0}g.push({tag:"meta",attrs:{...p}})}})}),v&&g.push(v),g.reverse(),g},[r]),s=Vt({select:g=>{var m;const v=g.matches.map(S=>S.links).filter(Boolean).flat(1).map(S=>({tag:"link",attrs:{...S}})),_=(m=i.ssr)==null?void 0:m.manifest,p=g.matches.map(S=>{var A;return((A=_?.routes[S.routeId])==null?void 0:A.assets)??[]}).filter(Boolean).flat(1).filter(S=>S.tag==="link").map(S=>({tag:"link",attrs:{...S.attrs,suppressHydrationWarning:!0}}));return[...v,...p]},structuralSharing:!0}),f=Vt({select:g=>{const m=[];return g.matches.map(v=>i.looseRoutesById[v.routeId]).forEach(v=>{var _,p,S,A;return(A=(S=(p=(_=i.ssr)==null?void 0:_.manifest)==null?void 0:p.routes[v.id])==null?void 0:S.preloads)==null?void 0:A.filter(Boolean).forEach(R=>{m.push({tag:"link",attrs:{rel:"modulepreload",href:R}})})}),m},structuralSharing:!0}),h=Vt({select:g=>g.matches.map(m=>m.styles).flat(1).filter(Boolean).map(({children:m,...v})=>({tag:"style",attrs:v,children:m})),structuralSharing:!0}),y=Vt({select:g=>g.matches.map(m=>m.headScripts).flat(1).filter(Boolean).map(({children:m,...v})=>({tag:"script",attrs:{...v},children:m})),structuralSharing:!0});return zg([...c,...f,...s,...h,...y],g=>JSON.stringify(g))};function Dg(){return xg().map(r=>W.createElement(yv,{...r,key:`tsr-meta-${JSON.stringify(r)}`}))}function zg(i,r){const c=new Set;return i.filter(s=>{const f=r(s);return c.has(f)?!1:(c.add(f),!0)})}const Cg=()=>{const i=Se(),r=Vt({select:f=>{var h;const y=[],g=(h=i.ssr)==null?void 0:h.manifest;return g?(f.matches.map(m=>i.looseRoutesById[m.routeId]).forEach(m=>{var v,_;return(_=(v=g.routes[m.id])==null?void 0:v.assets)==null?void 0:_.filter(p=>p.tag==="script").forEach(p=>{y.push({tag:"script",attrs:p.attrs,children:p.children})})}),y):[]},structuralSharing:!0}),{scripts:c}=Vt({select:f=>({scripts:f.matches.map(h=>h.scripts).flat(1).filter(Boolean).map(({children:h,...y})=>({tag:"script",attrs:{...y,suppressHydrationWarning:!0},children:h}))}),structuralSharing:!0}),s=[...c,...r];return Q.jsx(Q.Fragment,{children:s.map((f,h)=>W.createElement(yv,{...f,key:`tsr-scripts-${f.tag}-${h}`}))})};let xi;function Ug(i){return xi||(i.router.state.matches.length?xi=Promise.resolve():xi=Wy(i.router)),Q.jsx(G0,{promise:xi,children:()=>Q.jsx(Ag,{router:i.router})})}var Ng=(i,r,c,s,f,h,y,g)=>{let m=document.documentElement,v=["light","dark"];function _(A){(Array.isArray(i)?i:[i]).forEach(R=>{let z=R==="class",H=z&&h?f.map(q=>h[q]||q):f;z?(m.classList.remove(...H),m.classList.add(h&&h[A]?h[A]:A)):m.setAttribute(R,A)}),p(A)}function p(A){g&&v.includes(A)&&(m.style.colorScheme=A)}function S(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(s)_(s);else try{let A=localStorage.getItem(r)||c,R=y&&A==="system"?S():A;_(R)}catch{}},ev=["light","dark"],gv="(prefers-color-scheme: dark)",Lg=typeof window>"u",Lo=W.createContext(void 0),Bg={setTheme:i=>{},themes:[]},Op=()=>{var i;return(i=W.useContext(Lo))!=null?i:Bg},Hg=i=>W.useContext(Lo)?W.createElement(W.Fragment,null,i.children):W.createElement(wg,{...i}),jg=["light","dark"],wg=({forcedTheme:i,disableTransitionOnChange:r=!1,enableSystem:c=!0,enableColorScheme:s=!0,storageKey:f="theme",themes:h=jg,defaultTheme:y=c?"system":"light",attribute:g="data-theme",value:m,children:v,nonce:_,scriptProps:p})=>{let[S,A]=W.useState(()=>Yg(f,y)),[R,z]=W.useState(()=>S==="system"?yo():S),H=m?Object.values(m):h,q=W.useCallback(K=>{let et=K;if(!et)return;K==="system"&&c&&(et=yo());let G=m?m[et]:et,tt=r?Vg(_):null,it=document.documentElement,Y=ut=>{ut==="class"?(it.classList.remove(...H),G&&it.classList.add(G)):ut.startsWith("data-")&&(G?it.setAttribute(ut,G):it.removeAttribute(ut))};if(Array.isArray(g)?g.forEach(Y):Y(g),s){let ut=ev.includes(y)?y:null,F=ev.includes(et)?et:ut;it.style.colorScheme=F}tt?.()},[_]),lt=W.useCallback(K=>{let et=typeof K=="function"?K(S):K;A(et);try{localStorage.setItem(f,et)}catch{}},[S]),J=W.useCallback(K=>{let et=yo(K);z(et),S==="system"&&c&&!i&&q("system")},[S,i]);W.useEffect(()=>{let K=window.matchMedia(gv);return K.addListener(J),J(K),()=>K.removeListener(J)},[J]),W.useEffect(()=>{let K=et=>{et.key===f&&(et.newValue?A(et.newValue):lt(y))};return window.addEventListener("storage",K),()=>window.removeEventListener("storage",K)},[lt]),W.useEffect(()=>{q(i??S)},[i,S]);let k=W.useMemo(()=>({theme:S,setTheme:lt,forcedTheme:i,resolvedTheme:S==="system"?R:S,themes:c?[...h,"system"]:h,systemTheme:c?R:void 0}),[S,lt,i,R,c,h]);return W.createElement(Lo.Provider,{value:k},W.createElement(qg,{forcedTheme:i,storageKey:f,attribute:g,enableSystem:c,enableColorScheme:s,defaultTheme:y,value:m,themes:h,nonce:_,scriptProps:p}),v)},qg=W.memo(({forcedTheme:i,storageKey:r,attribute:c,enableSystem:s,enableColorScheme:f,defaultTheme:h,value:y,themes:g,nonce:m,scriptProps:v})=>{let _=JSON.stringify([c,r,h,i,g,y,s,f]).slice(1,-1);return W.createElement("script",{...v,suppressHydrationWarning:!0,nonce:typeof window>"u"?m:"",dangerouslySetInnerHTML:{__html:`(${Ng.toString()})(${_})`}})}),Yg=(i,r)=>{if(Lg)return;let c;try{c=localStorage.getItem(i)||void 0}catch{}return c||r},Vg=i=>{let r=document.createElement("style");return i&&r.setAttribute("nonce",i),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},yo=i=>(i||(i=window.matchMedia(gv)),i.matches?"dark":"light");const Gg="/assets/app-BH5oN-bv.css",$e=vg({head:()=>({meta:[{charSet:"utf-8"},{name:"viewport",content:"width=device-width, initial-scale=1"},{title:"库无忧"}],links:[{rel:"stylesheet",href:Gg},{rel:"icon",type:"image/png",href:"/sinochem.png"},{rel:"apple-touch-icon",href:"/sinochem.png"}]}),component:Xg});function Xg(){return Q.jsx(Qg,{children:Q.jsx(Hg,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:Q.jsx(mv,{})})})}function Qg({children:i}){return Q.jsxs("html",{suppressHydrationWarning:!0,children:[Q.jsx("head",{children:Q.jsx(Dg,{})}),Q.jsxs("body",{suppressHydrationWarning:!0,children:[i,Q.jsx(Cg,{})]})]})}const Zg=()=>dl(()=>import("./user-C4NI_B9l.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10])),Kg=we("/user")({component:hl(Zg,"component")}),Jg=()=>dl(()=>import("./setting-BniCEFhQ.js"),__vite__mapDeps([11,3,4,6,2,12,5])),$g=we("/setting")({component:hl(Jg,"component")}),kg=()=>dl(()=>import("./index-DplnlYdB.js"),__vite__mapDeps([13,3,4,12,5,8])),Pg=we("/")({component:hl(kg,"component")}),Wg=()=>dl(()=>import("./index-BCEHtj6m.js"),__vite__mapDeps([14,6,4,12,7])),Fg=we("/dashboard/")({component:hl(Wg,"component")}),Ig=()=>dl(()=>import("./index-BCJK7tzR.js"),__vite__mapDeps([15,4,5,1,2,3,8,12,9])),tp=we("/ai/")({component:hl(Ig,"component")}),ep=()=>dl(()=>import("./user-BGDbajKV.js"),__vite__mapDeps([16,6,4,3,12,7])),lp=we("/dashboard/user")({component:hl(ep,"component")}),np=()=>dl(()=>import("./register-R_X2xmaS.js"),__vite__mapDeps([17,5,9,10,18])),ap=we("/auth/register")({component:hl(np,"component")}),up=()=>dl(()=>import("./login-DnDHC_si.js"),__vite__mapDeps([19,8,5,10,18])),ip=we("/auth/login")({component:hl(up,"component")}),cp=()=>dl(()=>import("./callback-CBP9_-Zy.js"),__vite__mapDeps([20,8,7])),sp=we("/auth/dingtalk/callback")({component:hl(cp,"component")}),op=Kg.update({id:"/user",path:"/user",getParentRoute:()=>$e}),rp=$g.update({id:"/setting",path:"/setting",getParentRoute:()=>$e}),fp=Pg.update({id:"/",path:"/",getParentRoute:()=>$e}),dp=Fg.update({id:"/dashboard/",path:"/dashboard/",getParentRoute:()=>$e}),hp=tp.update({id:"/ai/",path:"/ai/",getParentRoute:()=>$e}),vp=lp.update({id:"/dashboard/user",path:"/dashboard/user",getParentRoute:()=>$e}),mp=ap.update({id:"/auth/register",path:"/auth/register",getParentRoute:()=>$e}),yp=ip.update({id:"/auth/login",path:"/auth/login",getParentRoute:()=>$e}),gp=sp.update({id:"/auth/dingtalk/callback",path:"/auth/dingtalk/callback",getParentRoute:()=>$e}),pp={IndexRoute:fp,SettingRoute:rp,UserRoute:op,AuthLoginRoute:yp,AuthRegisterRoute:mp,DashboardUserRoute:vp,AiIndexRoute:hp,DashboardIndexRoute:dp,AuthDingtalkCallbackRoute:gp},Sp=$e._addFileChildren(pp)._addFileTypes();function _p(){return Rg({routeTree:Sp,scrollRestoration:!0,defaultNotFoundComponent:()=>Q.jsxs("div",{className:"flex flex-col items-center justify-center min-h-screen",children:[Q.jsx("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:"404"}),Q.jsx("p",{className:"text-gray-600 mb-8",children:"页面未找到"}),Q.jsx("a",{href:"/",className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回首页"})]})})}const bp=_p();W.startTransition(()=>{Xy.hydrateRoot(document,Q.jsx(W.StrictMode,{children:Q.jsx(Ug,{router:bp})}))});export{la as R,dl as _,Tp as a,Mp as b,fv as c,k0 as d,Se as e,Rp as f,lv as g,Ep as h,Q as j,W as r,No as u,Op as z};
