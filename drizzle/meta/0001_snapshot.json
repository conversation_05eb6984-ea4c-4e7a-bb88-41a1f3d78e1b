{"version": "6", "dialect": "sqlite", "id": "c97dd1d4-0b2a-47b0-87fe-fc43ef6de0d8", "prevId": "57ca2513-6839-418f-a318-4967929bb694", "tables": {"conversations": {"name": "conversations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'knowledge'"}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {"conversations_userId_users_id_fk": {"name": "conversations_userId_users_id_fk", "tableFrom": "conversations", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "conversationId": {"name": "conversationId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {"messages_conversationId_conversations_id_fk": {"name": "messages_conversationId_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "statistics": {"name": "statistics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "totalRequestTimes": {"name": "totalRequestTimes", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "totalTokenUsage": {"name": "totalTokenUsage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "dingTalkUnionId": {"name": "dingTalkUnionId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "isAdmin": {"name": "isAdmin", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "token": {"name": "token", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 20000}, "requestTimes": {"name": "requestTimes", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "dingTalkUserId": {"name": "dingTalkUserId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mobile": {"name": "mobile", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}