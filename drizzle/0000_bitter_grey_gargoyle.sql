CREATE TABLE `conversations` (
	`id` text PRIMARY KEY NOT NULL,
	`userId` integer NOT NULL,
	`title` text NOT NULL,
	`createdAt` text DEFAULT (datetime('now')) NOT NULL,
	`updatedAt` text DEFAULT (datetime('now')) NOT NULL,
	FOREIGN KEY (`userId`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `messages` (
	`id` text PRIMARY KEY NOT NULL,
	`conversationId` text NOT NULL,
	`content` text NOT NULL,
	`role` text NOT NULL,
	`timestamp` text DEFAULT (datetime('now')) NOT NULL,
	FOREIGN KEY (`conversationId`) REFERENCES `conversations`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `statistics` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`totalRequestTimes` integer DEFAULT 0 NOT NULL,
	`totalTokenUsage` integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`dingTalkUnionId` text,
	`email` text,
	`password` text,
	`isAdmin` integer DEFAULT false NOT NULL,
	`token` integer DEFAULT 20000 NOT NULL,
	`requestTimes` integer DEFAULT 0 NOT NULL,
	`dingTalkUserId` text,
	`name` text,
	`avatar` text,
	`mobile` text,
	`createdAt` text DEFAULT (datetime('now')) NOT NULL,
	`updatedAt` text DEFAULT (datetime('now')) NOT NULL
);
